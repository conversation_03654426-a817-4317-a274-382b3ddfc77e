#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_trade.py
@Date: 2024/5/29
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence


class TableTrade(TableBase):

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_not_similar(self, db: str, table_name: str, sids: list) -> dict:
        """
        获取 当前批次相似ID 的非相似ID
        """
        if not db or not table_name or not sids:
            return {}
        sids = list(set(sids))
        sql = f'''
            select min(id) id,sid,uuid,is_similar from {db}.{table_name} where 
            sid in ({','.join(['%s'] * len(sids))}) and is_similar=2 group by sid
        '''
        data_src = self.db_mysql.read(sql, sids, return_dict=True)
        results: Sequence[dict] = data_src.data
        sid_uid_not_similar_map = {}
        for result in results:
            sid_uid_not_similar_map[result['sid']] = result['uuid']
        return sid_uid_not_similar_map

