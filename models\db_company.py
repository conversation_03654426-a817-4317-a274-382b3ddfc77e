#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: Su<PERSON>u
@File: db_company.py
@Date: 2023/12/22
@Desc: 
@Server: 
"""
from dataclasses import dataclass
from public.public_funcs import public_funcs_obj
from common.utils import clean_key_fields, keep_alnum_chars, clean_key_fields_hashdata


@dataclass
class AddressTypeMap:
    """
    地址类型映射表
    """
    address_type: str


@dataclass
class CompanyNameTypeMap:
    """
    公司名称类型映射表
    """
    company_name_type: str


@dataclass
class Company:
    """
    公司表
    """
    source_name: str
    company_name: str  # 公司当前使用实体名称
    country: str  # 公司所属国家
    province: str  # 公司所属州省
    pid: str = ''  # 公司ID；md5(company_name_cleaned+country+province)
    company_name_cleaned: str = ''  # 公司当前使用实体名称(仅保留数字字母，用于去重匹配)
    company_name_cleaned2: str = ''  # 基于company_name_cleaned去除后缀，仅用于匹配
    company_code: str = ''  # 公司编号(注册号)；企业在当地的唯一标识
    inc_date: int = 0  # 成立日期，秒级时间戳
    reg_date: int = 0  # 注册日期，秒级时间戳
    latitude: str = ''  # 维度|Latitude
    longitude: str = ''  # 经度|Longitude
    opgname: str = ''  # 同业组名称
    business_scope: str = ''  # 企业经营范围
    overview: str = ''  # 公司简介
    agent_name: str = ''  # 代理人名称
    revenue_usd: int = 0  # 营收：单位千美元
    company_size: str = ''  # 公司规模或员工人数; 0-10, 11-50, 51-200, 201-500, 501-1000, 1001-5000, 5001-10000, 10001+
    company_type_id: int = 0  # 公司类型;关联公司类型映射表
    company_status_id: int = 0  # 公司状态代码；关联公司状态映射表

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, company_name: {self.company_name}, country: {self.country}, pid: {self.pid}")

    def __post_init__(self):
        self.company_name = clean_key_fields(self.company_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        # 清洗公司名
        self.company_name = public_funcs_obj.clean_company_name(self.company_name)
        # 先替换公司后缀
        company_name_new = public_funcs_obj.replace_company_suffix(self.company_name)
        # 仅保留数字字母
        self.company_name_cleaned = keep_alnum_chars(company_name_new)
        self.company_name_cleaned = clean_key_fields(self.company_name_cleaned)
        if not self.pid:
            self.pid = clean_key_fields_hashdata(self.company_name_cleaned + self.country + self.province, preprocess=False)
        # 去除公司后缀并且仅保留数字字母
        self.company_name_cleaned2 = keep_alnum_chars(public_funcs_obj.remove_company_suffix(company_name_new))
        self.company_name_cleaned2 = clean_key_fields(self.company_name_cleaned2)

    # def check_pid(self, checker, bus_id_old):
    #     """
    #     校验 数据源ID的已存在映射，优先使用映射表对应的新IDD
    #     """
    #     if not bus_id_old:
    #         return False
    #     bus_id_new = checker.get_bus_id_new(self.source_name, bus_type=2, bus_id_old=bus_id_old)
    #     if bus_id_new and bus_id_new != self.pid:
    #         self.pid = bus_id_new


@dataclass
class CompanyAddresses:
    """
    公司地址表
    """
    source_name: str
    pid: str  # 公司ID
    address: str  # 公司地址
    uuid: str = ''  # 业务去重字段；md5(pid+address)
    postal_code: str = ''  # 邮编
    address_type_id: int = 0  # 公司地址类型映射表ID
    start_date: int = 0  # 地址使用起始日期；精确到年月日
    end_date: int = 0  # 地址使用截止日期；精确到年月日
    country_code_iso2: str = ''  # 地址所属-国家二字码
    country_en: str = ''  # 地址所属-国家名称
    province_en: str = ''  # 地址所属-州省名称
    city_en: str = ''  # 地址所属-城市名称
    street: str = ''  # 地址所属-街道
    province_id: int = 0    # 地址所属-州省ID
    city_id: int = 0    # 地址所属-城市ID

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, address_type_id: {self.address_type_id}, pid: {self.pid}")

    def __post_init__(self):
        self.address = clean_key_fields(self.address)
        self.uuid = clean_key_fields_hashdata(self.pid+self.address)


@dataclass
class CompanyAreaInfo:
    """
    公司地区详细表
    """
    source_name: str
    pid: str  # 公司ID
    country: str  # 公司所属国家
    province: str  # 公司所属州省
    city: str = ''  # 市名
    country_code: str = ""  # 关联国家表二字码
    province_id: int = 0  # 关联省表ID
    city_id: int = 0  # 关联市表ID

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, country: {self.country}, province: {self.province}, pid: {self.pid}")

    def __post_init__(self):
        pass


@dataclass
class CompanyIndustry:
    """
    公司行业表
    """
    source_name: str
    pid: str  # 公司ID
    industry: str  # 行业信息
    uuid: str = ''  # 业务唯一ID;md5(pid+industry)
    industry_id: int = 0  # 关联行业表ID

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, industry: {self.industry}, pid: {self.pid}")

    def __post_init__(self):
        self.industry = clean_key_fields(self.industry)
        self.uuid = clean_key_fields_hashdata(self.pid + self.industry)


@dataclass
class CompanyNames:
    """
    公司非实体现用名称表
    """
    source_name: str
    pid: str  # 公司ID
    company_name: str  # 公司非实体现用名称
    company_name_type_id: int = 0  # 公司名称类型映射表ID
    start_date: int = 0  # 名称使用起始日期;精确到年月日
    end_date: int = 0  # 名称使用结束日期;精确到年月日

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, company_name: {self.company_name}, pid: {self.pid}")

    def __post_init__(self):
        self.company_name = clean_key_fields(self.company_name)


@dataclass
class CompanyProducts:
    """
    公司产品表
    """
    source_name: str
    pid: str  # 公司ID
    product_name: str  # 公司产品名称

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, product_name: {self.product_name}, pid: {self.pid}")

    def __post_init__(self):
        self.product_name = clean_key_fields(self.product_name)


@dataclass
class CompanyStock:
    """
    公司上市信息表
    """
    source_name: str
    pid: str  # 公司ID
    stock_code: str  # 股票代码
    listing_sector: str = ''  # 上市板块
    listing_status: str = ''  # 上市状态

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, stock_code: {self.stock_code}, pid: {self.pid}")

    def __post_init__(self):
        self.stock_code = clean_key_fields(self.stock_code)


@dataclass
class CompanyLogo:
    """
    公司logo信息表
    """
    source_name: str
    pid: str  # 公司ID
    logo_url: str = ''   # 公司logo链接
    logo_url_local: str = ''   # 公司本地logo链接

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, logo_url: {self.logo_url}, pid: {self.pid}")

    def __post_init__(self):
        # TODO: 如果 logo_url_local 值不存在 则 将 logo_url 图片存入cos，并将地址存入logo_url_local
        pass


@dataclass
class CompanyShareholders:
    """
    公司 股东 模型
    """
    source_name: str
    pid: str  # 公司ID
    shareholder_name: str  # 股东名称
    country: str  # 股东所属国家
    province: str  # 股东所属州省
    country_code: str = ''  # 股东所属国家二字码
    shareholder_id: str = ''  # 股东ID
    bus_id_old: str = ''  # 源数据股东ID
    shareholder_type: int = 0     # 股东类型；0-未检测，1-个人，2-公司，3-不确定
    shareholder_direct: str = ''    # 持股比
    shareholder_total: str = ''     # 持股总比

    def __post_init__(self):
        self.shareholder_name = clean_key_fields(self.shareholder_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        if self.shareholder_type == 1:
            self.shareholder_id = clean_key_fields_hashdata(self.pid + self.shareholder_name)
        elif self.shareholder_type == 2:
            # 清洗公司名
            self.shareholder_name = public_funcs_obj.clean_company_name(self.shareholder_name)
            # 先替换公司后缀
            company_name_new = public_funcs_obj.replace_company_suffix(self.shareholder_name)
            # 仅保留数字字母
            company_name_cleaned = keep_alnum_chars(company_name_new)
            company_name_cleaned = clean_key_fields(company_name_cleaned)
            self.shareholder_id = clean_key_fields_hashdata(company_name_cleaned+self.country+self.province)

    # def check_shareholder_id(self, checker, bus_id_old):
    #     """
    #     校验 数据源ID的已存在映射，优先使用映射表对应的新IDD
    #     """
    #     if not bus_id_old:
    #         return False
    #     bus_id_new = checker.get_bus_id_new(self.source_name, bus_type=self.shareholder_type, bus_id_old=bus_id_old)
    #     if bus_id_new and bus_id_new != self.shareholder_id:
    #         self.shareholder_id = bus_id_new


@dataclass
class CompanySubsidiary:
    """
    公司 子公司 模型
    """
    source_name: str
    pid: str  # 公司ID
    subsidiary_name: str  # 子公司名称
    country: str  # 子公司所属国家
    province: str  # 子公司所属州省
    country_code: str = ''  # 子公司所属国家二字码
    subsidiary_id: str = ''  # 子公司ID
    bus_id_old: str = ''  # 源数据子公司ID
    shareholder_direct: str = ''    # 持股比
    shareholder_total: str = ''     # 持股总比

    def __post_init__(self):
        self.subsidiary_name = clean_key_fields(self.subsidiary_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        # 清洗公司名
        self.subsidiary_name = public_funcs_obj.clean_company_name(self.subsidiary_name)
        # 先替换公司后缀
        company_name_new = public_funcs_obj.replace_company_suffix(self.subsidiary_name)
        # 仅保留数字字母
        company_name_cleaned = keep_alnum_chars(company_name_new)
        company_name_cleaned = clean_key_fields(company_name_cleaned)
        self.subsidiary_id = clean_key_fields_hashdata(company_name_cleaned+self.country+self.province)

    # def check_subsidiary_id(self, checker, bus_id_old):
    #     """
    #     校验 数据源ID的已存在映射，优先使用映射表对应的新IDD
    #     """
    #     if not bus_id_old:
    #         return False
    #     bus_id_new = checker.get_bus_id_new(self.source_name, bus_type=2, bus_id_old=bus_id_old)
    #     if bus_id_new and bus_id_new != self.subsidiary_id:
    #         self.subsidiary_id = bus_id_new


@dataclass
class CompanyStatusMap:
    """
    公司状态映射表
    """
    source_name: str
    company_status: str

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, company_status: {self.company_status}")

    def __post_init__(self):
        self.company_status = clean_key_fields(self.company_status)


@dataclass
class CompanyTypeMap:
    """
    公司类型映射表
    """
    source_name: str
    company_type: str

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, company_type: {self.company_type}")

    def __post_init__(self):
        self.company_type = clean_key_fields(self.company_type)


@dataclass
class BusIdMap:
    """
    新旧ID映射表
    """
    source_name: str
    bus_id_old: str     # 业务唯一标识(旧)
    bus_id_new: str     # 业务唯一标识(新)
    bus_type: int = 0       # 业务类型;1-人物标识,2-公司标识,3-贸易标识

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, bus_id_old: {self.bus_id_old}, bus_id_new: {self.bus_id_new}")


@dataclass
class BusIdMapCompany:
    """
    新旧ID映射表 - 公司
    """
    source_name: str
    bus_id_old: str     # 业务唯一标识(旧)
    bus_id_new: str     # 业务唯一标识(新)

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, bus_id_old: {self.bus_id_old}, bus_id_new: {self.bus_id_new}")


@dataclass
class CompanyNationalIdentifiers:
    """
    公司编号信息表
    """
    source_name: str
    pid: str  # 公司ID
    nat_id: str   # 编号
    nat_label_id: str = ''   # 编号标签类型ID

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, pid: {self.pid}, nat_id: {self.nat_id}, nat_label_id: {self.nat_label_id}")


@dataclass
class CompanyTechStack:
    """
    公司技术栈 模型
    """
    source_name: str
    pid: str  # 公司ID
    tech_name: str  # 技术名称
    tech_logo_url: str = ''  # 技术logo链接
    tech_logo_path: str = ''  # 技术logo于COS存储地址
    tech_category: str = ''  # 技术所属类别
    by_cname: str = ''  # 技术所属公司名称

    def __post_init__(self):
        self.tech_name = clean_key_fields(self.tech_name)

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, tech_name: {self.tech_name}")


class TestUnit:
    def __init__(self):
        pass

    def test_unit(self):
        """调用测试"""
        company_name = 'vong sawat textile co ltd'
        # company_obj = Company(source_name='test', company_name=company_name, country='', province='')
        # company_data = company_obj.__dict__
        # print(company_data)
        # 先替换公司后缀
        company_name_new = public_funcs_obj.replace_company_suffix(company_name)
        print(company_name_new)
        # 仅保留数字字母
        company_name_cleaned = keep_alnum_chars(company_name_new)
        print(company_name_cleaned)
        # 去除公司后缀并且仅保留数字字母
        company_name_cleaned2 = keep_alnum_chars(public_funcs_obj.remove_company_suffix(company_name_new))
        print(company_name_cleaned2)


if __name__ == "__main__":
    TestUnit().test_unit()
