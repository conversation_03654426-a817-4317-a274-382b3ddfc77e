services:
  # 定时任务
  datak_customs_trade_timing:  # 容器名(前面会自动加上项目目录的名字)
    container_name: datak_customs_trade_timing  # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build: .  # 指定Dockerfile的路径，用于构建容器镜像(仅使用于Dockerfile文件名为默认的情况)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: datak_customs_trade_timing:0.1  # 镜像名:标签
    working_dir: /app/preprocess/sync_normalization # 容器的工作目录
    command: python clean_customs_trade.py  # 容器的启动命令
    volumes:  # 数据卷
      - ./logs:/app/logs
    logging:  # 日志
      options:
        max-size: "10m"
        max-file: "3"
