#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public.py
@Date: 2024/4/12
@Desc: 
@Server: 
"""
from dataclasses import dataclass
from common.utils import clean_key_fields
from settings import COUNTRY_NAME_MAP


def check_attributes(func):
    def wrapper(self, attrs=None):
        # 校检属性是否为None;为None则替换为默认值
        if attrs is None:
            attrs = []
        if '*' in attrs:
            attrs = [attr_name for attr_name in self.__annotations__]
        for attr_name in attrs:
            if getattr(self, attr_name) is None:
                default_value = getattr(self.__dataclass_fields__[attr_name], 'default', None)
                setattr(self, attr_name, default_value)
        return func(self, attrs)

    return wrapper


black_name = {"NOT AVAILABLE", "UNKNOW", "NO DISPONIBLE", "N A", "N/A"}


def transform_country_name(name: str):
    """转换国家名字"""
    if not name:
        return ""
    name = name.strip().lower()
    name = COUNTRY_NAME_MAP.get(name, name)
    return name


@dataclass
class PreprocessAddress:
    """
    预处理方法 地址 模型
    """
    address: str
    address_type_id: int = 8  # 地址类型
    postal_code: str = ''  # 邮编
    start_date: int = 0  # 地址使用起始日期；精确到年月日
    end_date: int = 0  # 地址使用截止日期；精确到年月日
    country_code_iso2: str = ''  # 地址所属-国家二字码
    country_en: str = ''  # 地址所属-国家名称
    province_en: str = ''  # 地址所属-州省名称
    city_en: str = ''  # 地址所属-城市名称
    street: str = ''  # 地址所属-街道
    province_id: int = 0  # 地址所属-州省ID
    city_id: int = 0  # 地址所属-城市ID

    def __post_init__(self):
        self.address = clean_key_fields(self.address)
        if len(self.province_en) <= 1:
            self.province_en = ''
        if len(self.city_en) <= 1:
            self.city_en = ''

        # 特殊映射
        self.country_en = transform_country_name(self.country_en)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessLogo:
    """
    预处理方法 logo信息表
    """
    logo_url: str = ''   # logo链接
    logo_url_local: str = ''   # 本地logo链接
