#!/bin/bash

# 检查是否提供了服务名称作为参数
if [ -z "$1" ]; then
  echo "Usage: $0 <service_name>"
  exit 1
fi

SERVICE_NAME="$1"

# 获取脚本所在目录
SCRIPT_DIR=$(dirname $(realpath "$0"))
cd "$SCRIPT_DIR" || exit

# 获取容器的镜像名（如果存在）
IMAGE_NAME=$(docker compose images $SERVICE_NAME --quiet | head -n 1)

# 如果找到镜像名，则删除镜像和容器
if [ -n "$IMAGE_NAME" ]; then
  # 停止并删除指定服务的容器
  docker compose stop $SERVICE_NAME
  docker compose rm -f $SERVICE_NAME
  echo "rm container '$SERVICE_NAME' success"
  # 删除指定服务的镜像
  docker rmi $IMAGE_NAME
  echo "rm image '$IMAGE_NAME' success"
fi

# 构建并启动指定服务
docker compose up -d --build $SERVICE_NAME
echo "build service '$SERVICE_NAME' success"

# 清理未使用的构建缓存和镜像
docker builder prune -f
docker image prune -f

# 使用样例
# bash up.sh datak_depth_company
# bash up.sh datak_customs_trade
# bash up.sh datak_apollo_company
# bash up.sh datak_link
# bash up.sh datak_customs_company