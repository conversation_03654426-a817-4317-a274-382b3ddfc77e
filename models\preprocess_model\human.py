#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: human.py
@Date: 2024/5/9
@Desc: 
@Server: 
"""
from dataclasses import dataclass
from typing import List
from common.utils import clean_key_fields
from .public import check_attributes
from .public import PreprocessAddress
from .public import PreprocessLogo
from .public import transform_country_name
from .concat import PreprocessConcatMain


@dataclass
class PreprocessEducation:
    """
    预处理方法 教育经历 模型
    """
    school_name: str  # 学校名称
    bus_id_old: str = ''  # 数据源学校ID
    school_type: str = ''  # 学校类型
    country: str = ''  # 学校所属国家
    country_code: str = ''  # 学校所属国家二字码
    province: str = ''  # 学校所属州省
    city: str = ''  # 市名
    start_date: int = 0  # 开始日期，秒级时间戳
    end_date: int = 0  # 结束日期，秒级时间戳
    degrees: str = ''  # 学业程度
    majors: str = ''  # 专业
    minors: str = ''  # 辅修科目
    gpa: str = ''  # 平均学分绩点
    summary: str = ''  # 总结
    concat_obj: PreprocessConcatMain = None  # 联系方式信息

    def __post_init__(self):
        self.school_name = clean_key_fields(self.school_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        self.city = clean_key_fields(self.city)

        # 特殊映射
        self.country = transform_country_name(self.country)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessExperience:
    """
    预处理方法 任职经历 模型
    """
    company_name: str  # 公司当前使用实体名称
    country: str  # 公司所属国家
    country_code: str  # 公司所属国家二字码
    province: str  # 公司所属州省
    city: str = ''  # 市名
    bus_id_old: str = ''  # 数据源公司ID
    title_name: str = ''  # 担任职位    director
    title_role: str = ''  # 担任职务角色  operations
    title_sub_role: str = ''  # 担任职务子角色 product
    title_levels: str = ''  # 职级    ["member"]
    department: str = ''  # 理事会，委员会或部门  审计委员会; 公司治理委员会
    start_date: int = 0  # 任职开始日期，秒级时间戳
    end_date: int = 0  # 任职结束日期，秒级时间戳
    post_status: int = 0  # 任职状态;0-未检测，1-在职，2-离职，3-不确定
    summary: str = ''  # 经历总结

    def __post_init__(self):
        self.company_name = clean_key_fields(self.company_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        self.city = clean_key_fields(self.city)

        # 特殊映射
        self.country = transform_country_name(self.country)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessPeopleMain:
    """
    预处理方法 人物 模型
    """
    source_name: str
    human_name: str  # 人物名称
    company_bus_id_old: str = ''  # 人物当前任职公司 数据源ID
    human_type: int = 0  # 个人或公司(在股东中可能存在公司主体，在预处理无法判断的情况下可能会放入该表，由二次清洗服务去标识); 0-未检测，1-个人，2-公司，3-不确定
    country: str = ''  # 人物所属国家
    country_code: str = ''  # 人物所属国家二字码
    province: str = ''  # 人物所属州省
    city: str = ''  # 市名
    bus_id_old: str = ''  # 数据源人物ID
    gender: str = ''  # 性别;F-女性(female),M-男性(male)
    birth_year: str = ''  # 出生年;YYYY
    birth_date: str = ''  # 出生日期;YYYY-MM-DD
    industry: str = ''  # 人物所属行业
    interests: str = ''  # 兴趣爱好
    skills: str = ''  # 技能
    profiles: str = ''  # 简介
    languages: str = ''  # 语言
    certifications: str = ''  # 证书
    human_logo_obj_list: List[PreprocessLogo] = None  # 人物logo数据
    human_address_obj_list: List[PreprocessAddress] = None  # 人物地址数据
    human_education_obj_list: List[PreprocessEducation] = None  # 人物教育经历信息
    human_experience_obj_list: List[PreprocessExperience] = None  # 人物任职经历信息
    concat_obj: PreprocessConcatMain = None  # 联系方式信息

    def __post_init__(self):
        self.human_name = clean_key_fields(self.human_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        self.city = clean_key_fields(self.city)

        # 特殊映射
        self.country = transform_country_name(self.country)

        if len(self.province) <= 1:
            self.province = ''
        if len(self.city) <= 1:
            self.city = ''

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass