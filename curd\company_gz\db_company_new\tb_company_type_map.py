#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_type_map.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from models import MysqlResult
from typing import Sequence



class TableCompanyTypeMap(TableBase):
    db = 'db_company_new'
    table = 'company_type_map'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_company_type_id(self, company_type_set: set) -> dict:
        """获取公司类型ID"""
        if not company_type_set:
            return {}
        bfhs_str = ','.join(['%s'] * len(company_type_set))
        # 查询公司类型ID
        sql = f'''
                select `id`, `company_type` from {self.db}.{self.table} where `company_type` in ({bfhs_str})
            '''
        data_src: MysqlResult = self.db_mysql.read(sql, value=list(company_type_set), return_dict=True)
        results: Sequence[dict] = data_src.data
        company_type_id_map = {}
        for row in results:
            tid = row.get('id', 0)
            company_type = row.get('company_type', '')
            if not tid:
                continue
            company_type_id_map[company_type] = tid
        # 将不存在的公司类型插入数据表，并获取自增ID
        for company_type in company_type_set:
            if company_type in company_type_id_map:
                continue
            data_src = self.db_mysql.save(self.table, {'company_type': company_type}, db=self.db, ignore=True)
            company_type_id_map[company_type] = data_src.lastrowid
        return company_type_id_map
