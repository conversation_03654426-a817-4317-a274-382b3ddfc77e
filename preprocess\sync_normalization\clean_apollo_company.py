#!/usr/bin/env python
# -*- coding: utf-8 -*-

import arrow
import time
import traceback

from settings import *
from typing import Optional, Sequence, Tuple, Union, Dict
from preprocess import PreprocessBase
from preprocess.sync_normalization.clean_funcs import preprocess_clean_funcs_obj
from models.preprocess_model import *
from models.public_clean_model import *
from common.database import MysqlConnectionManager
from common.database import DBPoolMysql
from curd import TableDimWorldLocationCountry
from curd import TableApolloEmploymentHistory
from curd import TableApolloPersonInfo
from public.public_clean_main import PublicCleanMain


class CleanApolloCompany(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.source_name = 'apollo'
        self.mysql_obj_sp02: DBPoolMysql = mysql_manager.get_pool("sp02")
        self.mysql_obj_company_gz: DBPoolMysql = mysql_manager.get_pool("company_gz")
        self.table_dim_world_location_country_obj = TableDimWorldLocationCountry(self.mysql_obj_company_gz)
        self.table_apollo_employment_history_obj = TableApolloEmploymentHistory(self.mysql_obj_sp02)
        self.table_apollo_person_info_obj = TableApolloPersonInfo(self.mysql_obj_sp02)
        self.public_clean_main_obj = PublicCleanMain(mysql_manager)

    def clean_revenue(self, value: str) -> int:
        """
        清洗 营收 为 单位千美元
        将营收数据统一转换为千美元单位
        """
        if not value:
            return 0
        try:
            # 转换单位映射：千(K) -> 1, 百万(M) -> 1000, 十亿(B) -> 1_000_000
            unit_mapping = {"K": 1, "M": 1000, "B": 1_000_000, "": 0.001}
            # 判断是否带有单位后缀
            if value[-1].isalpha():
                number, unit = float(value[:-1]), value[-1].upper()
            else:
                number, unit = float(value), ""
            # 转换为千美元单位
            revenue = int(number * unit_mapping[unit])
            return revenue
        except Exception as e:
            logging.error(f'clean revenue【{value}】 failed: {traceback.format_exc()}')
            return 0

    def deal_data_company(self, company_data: dict, **kwargs) -> Union[PreprocessCompanyMain, None]:
        """
        处理公司数据
        """
        if not company_data:
            return None
        company_name = company_data.get('name', '')
        if not company_name:
            return None
        bus_id_old = company_data.get('apollo_id', '')
        logo_url = company_data.get('logo_url', '')
        website_url = company_data.get('website_url', '')
        phone_number = company_data.get('phone_number', '')
        street_address = company_data.get('street_address', '')
        country = company_data.get('country', '')
        state = company_data.get('state', '')
        city = company_data.get('city', '')
        postal_code = company_data.get('postal_code', '')
        twitter_url = company_data.get('twitter_url', '')
        facebook_url = company_data.get('facebook_url', '')
        instagram_url = company_data.get('instagram_url', '')
        linkedin_url = company_data.get('linkedin_url', '')
        angellist_url = company_data.get('angellist_url', '')
        crunchbase_url = company_data.get('crunchbase_url', '')
        industries = company_data.get('industries', '')
        founded_at = company_data.get('founded_at', '')
        description = company_data.get('description', '')
        technologies = company_data.get('technologies', '')
        revenue = company_data.get('revenue', '')
        employee_count = company_data.get('employee_count', 0)

        company_logo_obj_list = []
        company_address_obj_list = []
        company_industry_obj_list = []
        company_tech_stack_obj_list = []

        # 处理清洗字段
        if logo_url and '63edac09a4ccae0001634980' in logo_url:
            # 无效 logo链接
            logo_url = ''
        try:
            industries = eval(industries) if industries else []
            industries = list(set([_d.lower().strip() for _d in industries]))
        except Exception as e:
            industries = []
        try:
            technologies = eval(technologies) if technologies else []
        except Exception as e:
            technologies = []

        inc_date = preprocess_clean_funcs_obj.date_str_to_timestamp(founded_at, date_format='%Y')
        revenue = self.clean_revenue(revenue)

        address = preprocess_clean_funcs_obj.format_address(
            country_name=country,
            state_name=state,
            city_name=city,
            street1=street_address,
            postal_code=postal_code,
        )
        if address:
            company_address_obj = PreprocessAddress(
                address=address,
                postal_code=postal_code,
                country_en=country,
                province_en=state,
                city_en=city,
            )
            company_address_obj_list.append(company_address_obj)
        for industry in industries:
            company_industry_obj = PreprocessCompanyIndustry(
                industry=industry
            )
            company_industry_obj_list.append(company_industry_obj)
        if logo_url:
            company_logo_obj = PreprocessLogo(
                logo_url=logo_url
            )
            company_logo_obj_list.append(company_logo_obj)
        for technology in technologies:
            tech_name = technology.get('name', '')
            tech_category = technology.get('category', '')
            if not tech_name:
                continue
            company_tech_stack_obj = PreprocessCompanyTechStack(
                tech_name=tech_name,
                tech_category=tech_category,
            )
            company_tech_stack_obj_list.append(company_tech_stack_obj)

        # 联系方式
        bp_datalist = [
            {"source_name": self.source_name, "phone_raw": phone_number}
        ] if phone_number else []
        bs_datalist = []
        if twitter_url:
            bs_datalist.append(
                {"source_name": self.source_name, "social_url": twitter_url, "social_type": "twitter"}
            )
        if facebook_url:
            bs_datalist.append(
                {"source_name": self.source_name, "social_url": facebook_url, "social_type": "facebook"}
            )
        if instagram_url:
            bs_datalist.append(
                {"source_name": self.source_name, "social_url": instagram_url, "social_type": "instagram"}
            )
        if linkedin_url:
            bs_datalist.append(
                {"source_name": self.source_name, "social_url": linkedin_url, "social_type": "linkedin"}
            )

        bw_datalist = [
            {"source_name": self.source_name, "website": website_url}
        ] if website_url else []

        company_email_obj_list = []
        company_phone_obj_list = preprocess_clean_funcs_obj.create_preprocess_phone(bp_datalist)
        company_social_obj_list = preprocess_clean_funcs_obj.create_preprocess_social(bs_datalist)
        company_website_obj_list = preprocess_clean_funcs_obj.create_preprocess_website(bw_datalist)

        preprocess_concat_main_obj = PreprocessConcatMain(
            email_obj_list=company_email_obj_list,
            phone_obj_list=company_phone_obj_list,
            social_obj_list=company_social_obj_list,
            website_obj_list=company_website_obj_list,
        )

        preprocess_company_main_obj: PreprocessCompanyMain = PreprocessCompanyMain(
            source_name=self.source_name,
            company_name=company_name,
            country=country,
            province=state,
            city=city,
            bus_id_old=bus_id_old,
            inc_date=inc_date,
            overview=description,
            revenue_usd=revenue,
            company_size=employee_count,
            company_logo_obj_list=company_logo_obj_list,
            company_address_obj_list=company_address_obj_list,
            company_industry_obj_list=company_industry_obj_list,
            company_tech_stack_obj_list=company_tech_stack_obj_list,
            concat_obj=preprocess_concat_main_obj
        )
        return preprocess_company_main_obj

    def deal_data_human(self, data: dict, **kwargs) -> Union[PreprocessPeopleMain, None]:
        """
        处理 人物数据
        :param data: 人物数据
        :return: PreprocessPeopleMain
        """
        person_name = data.get('person_name', '')
        if not person_name:
            return None
        bus_id_old = data.get('person_id', '')
        country = data.get('country', '')
        state = data.get('state', '')
        city = data.get('city', '')
        street_address = data.get('street_address', '')
        postal_code = data.get('postal_code', '')
        email = data.get('email', '')
        phone = data.get('phone', '')

        human_address_obj_list = []
        human_experience_obj_list = []

        person_director_data_map = kwargs.get('person_director_data_map', {})
        preprocess_company_main_obj_map = kwargs.get('preprocess_company_main_obj_map', {})
        experience_datalist = person_director_data_map.get(bus_id_old, []) or []

        address = preprocess_clean_funcs_obj.format_address(
            country_name=country,
            state_name=state,
            city_name=city,
            street1=street_address,
            postal_code=postal_code,
        )
        if address:
            address_obj = PreprocessAddress(
                address=address,
                postal_code=postal_code,
                country_en=country,
                province_en=state,
                city_en=city,
            )
            human_address_obj_list.append(address_obj)

        # 如果工作经历中存在当前工作公司，取出
        company_bus_id_old = ''
        for _data in experience_datalist:
            company_id = _data.get('company_id', '')
            position = _data.get('position', '')
            current = _data.get('current', 0)
            start_date = _data.get('start_date', '')
            end_date = _data.get('end_date', '')
            preprocess_company_main_obj = preprocess_company_main_obj_map.get(company_id, None)
            if not preprocess_company_main_obj:
                continue
            if current == 1:
                company_bus_id_old = company_id
            try:
                start_date = int(arrow.get(start_date, 'YYYY-MM-DD').timestamp()) if start_date else 0
            except Exception as e:
                logger.warning(f'日期转换异常[{company_id}] === start_date: {start_date}')
                start_date = 0
            try:
                end_date = int(arrow.get(end_date, 'YYYY-MM-DD').timestamp()) if end_date else 0
            except Exception as e:
                logger.warning(f'日期转换异常[{company_id}] === end_date: {end_date}')
                end_date = 0

            human_experience_obj = PreprocessExperience(
                company_name=preprocess_company_main_obj.company_name,
                country=preprocess_company_main_obj.country,
                country_code=preprocess_company_main_obj.country_code,
                province=preprocess_company_main_obj.province,
                city=preprocess_company_main_obj.city,
                bus_id_old=company_id,
                title_name=position,
                start_date=start_date,
                end_date=end_date,
                post_status=current
            )
            human_experience_obj_list.append(human_experience_obj)

        # 联系方式
        be_datalist = [
            {"source_name": self.source_name, "email": email}
        ] if email else []
        bp_datalist = [
            {"source_name": self.source_name, "phone_raw": phone}
        ] if phone else []
        email_obj_list = preprocess_clean_funcs_obj.create_preprocess_email(be_datalist)
        phone_obj_list = preprocess_clean_funcs_obj.create_preprocess_phone(bp_datalist)
        preprocess_concat_main_obj = PreprocessConcatMain(
            email_obj_list=email_obj_list,
            phone_obj_list=phone_obj_list,
        )

        preprocess_people_main_obj = PreprocessPeopleMain(
            source_name=self.source_name,
            human_name=person_name,
            company_bus_id_old=company_bus_id_old,
            country=country,
            province=state,
            city=city,
            bus_id_old=bus_id_old,
            human_address_obj_list=human_address_obj_list,
            human_experience_obj_list=human_experience_obj_list,
            concat_obj=preprocess_concat_main_obj
        )
        return preprocess_people_main_obj

    def deal_datalist(self, datalist: List[dict]) -> Tuple[
        List[PreprocessCompanyMain], List[PreprocessPeopleMain],
        List[PublicCleanCompanyMain], List[PublicCleanHumanMain],
        List[PublicCleanSchoolMain]
    ]:
        """
        处理批量数据
        :param datalist:
        :return:
        """
        (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
         public_clean_company_main_obj_list, public_clean_human_main_obj_list,
         public_clean_school_main_obj_list) = [], [], [], [], []
        logger.info(f'start preprocess deal datalist ...')
        s_t = time.time()
        hids = set()
        cids = set()
        for data in datalist:
            apollo_id = data.get('apollo_id', '')
            if apollo_id:
                cids.add(apollo_id)
        cids = list(cids)
        # 获取 员工人物-任职 信息
        person_director_datalist: Sequence[dict] = (
            self.table_apollo_employment_history_obj.get_person_director(cids=cids))
        # 获取涉及的所有人物 ID
        person_director_data_map = {}
        for _data in person_director_datalist:
            person_id = _data.get('person_id', '')
            if not person_id:
                continue
            hids.add(person_id)
            if person_id not in person_director_data_map:
                person_director_data_map[person_id] = []
            person_director_data_map[person_id].append(_data)
        hids = list(hids)
        # 获取涉及的所有人物 基本信息
        person_info_datalist = self.table_apollo_person_info_obj.get_person_infos(hids=hids)

        # 预处理 公司信息
        preprocess_company_main_obj_map = {}
        for company_data in datalist:
            bus_id_old = company_data.get('apollo_id', '')
            preprocess_company_main_obj = self.deal_data_company(
                company_data=company_data
            )
            preprocess_company_main_obj_map[bus_id_old] = preprocess_company_main_obj
            preprocess_company_main_obj_list.append(preprocess_company_main_obj)

        # 预处理 人物信息
        for _data in person_info_datalist:
            preprocess_people_main_obj = self.deal_data_human(
                _data,
                person_director_data_map=person_director_data_map,
                preprocess_company_main_obj_map=preprocess_company_main_obj_map,
            )
            preprocess_people_main_obj_list.append(preprocess_people_main_obj)

        logger.info(f'end preprocess deal datalist === hs: {time.time() - s_t:.2f} s')

        # ===== 执行公共清洗 =====
        # 执行公共清洗 - 公司
        public_clean_company_main_obj_list: List[PublicCleanCompanyMain] = (
            self.public_clean_main_obj.main_clean_company(preprocess_company_main_obj_list)
        )
        # 获取 公司清洗后的 新旧 ID 映射
        # 所有新旧 ID映射
        company_bus_id_map_obj_list = []
        for public_clean_company_main_obj in public_clean_company_main_obj_list:
            _bus_id_map_obj_list = public_clean_company_main_obj.bus_id_map_obj_list
            if _bus_id_map_obj_list:
                company_bus_id_map_obj_list += _bus_id_map_obj_list
        # 执行公共清洗 - 人物
        public_clean_human_main_obj_list, public_clean_school_main_obj_list = (
            self.public_clean_main_obj.main_clean_human(
                preprocess_people_main_obj_list=preprocess_people_main_obj_list,
                company_bus_id_map_obj_list=company_bus_id_map_obj_list
            ))
        return (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
                public_clean_company_main_obj_list, public_clean_human_main_obj_list,
                public_clean_school_main_obj_list)

    def test_unit(self):
        """
        单元测试
        :return: 
        """
        mysql_obj = self.mysql_obj_sp02
        # sql = f'''select * from db_spider.apollo_company_info order by id limit 10'''
        sql = f'''select * from db_spider.apollo_company_info where id between ********* and *********'''
        # sql = f'''select * from db_spider.apollo_company_info where apollo_id = "5da3beb88f77a30001928728"'''
        data_src = mysql_obj.read(sql, return_dict=True)
        results: List[dict] = data_src.data
        (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
         public_clean_company_main_obj_list, public_clean_human_main_obj_list,
         public_clean_school_main_obj_list) = self.deal_datalist(results)
        for _data in preprocess_company_main_obj_list:
            print(_data)
            print('*' * 100)
        for _data in preprocess_people_main_obj_list:
            print(_data)
            print('*' * 100)
        for _data in public_clean_company_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in public_clean_human_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in public_clean_school_main_obj_list:
            print(_data)


if __name__ == '__main__':
    from models import ConfMySQL
    conf_list = [
        ConfMySQL(name='sp02', max_connections=1),
        ConfMySQL(name='company_gz', max_connections=1),
        ConfMySQL(name='company_new_gz', max_connections=1),
    ]
    m_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    m_manager.init()
    obj_ = CleanApolloCompany(m_manager)
    obj_.test_unit()
