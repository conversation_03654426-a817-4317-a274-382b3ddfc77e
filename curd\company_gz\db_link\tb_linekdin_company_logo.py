#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-01-07 15:23:57
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence, Dict


class TableLinkCompanyLogo(TableBase):
    db = 'db_link'
    table = 'linekdin_company_logo'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_logo_infos(self, cids: list) -> dict:
        """
        获取 公司 logo 信息
        :param cids: 公司ID列表
        :return: {公司ID: []}
        """
        if not cids:
            return {}
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select * from {self.db}.{self.table} where job_company_id in ({bfhs_str}) and is_deleted=0
        '''
        data_src = self.db_mysql.read(sql, value=cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        data: dict = {}
        for result in results:
            job_company_id = result['job_company_id']
            logo_url = result['logo_url']
            logo_path = result['logo_path']
            if job_company_id not in data:
                data[job_company_id] = []
            data[job_company_id].append({
                'logo_url': logo_url,
                'logo_path': logo_path,
            })
        return data
