#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_bus_id_map.py
@Date: 2024/3/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from models import MysqlResult
from typing import Sequence, Union


class TableBusIdMapHuman(TableBase):
    db = 'db_company_new'
    table = 'bus_id_map_human'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_bus_id_new(self, source_name: str, bus_id_old: str) -> Union[str, None]:
        """获取新业务标识"""
        if not bus_id_old or not source_name:
            return None
        # 查询新公司ID
        sql = f'''
                select `id`, `source_name`, `bus_id_old`, `bus_id_new` from {self.db}.{self.table} 
                where `bus_id_old`=%(bus_id_old)s and source_name=%(source_name)s
            '''
        value = {'bus_id_old': bus_id_old, 'source_name': source_name}
        data_src: MysqlResult = self.db_mysql.read(sql, value=value, return_dict=True)
        results: Sequence[dict] = data_src.data
        if not results:
            return None
        bus_id_new = results[0]['bus_id_new']
        if not bus_id_new:
            return None
        return bus_id_new

    def get_bus_id_news(self, source_name: str, bus_id_olds: list) -> dict:
        """批量-获取新业务标识"""
        if not bus_id_olds or not source_name:
            return {}
        bus_id_olds = list(set(bus_id_olds))
        bfhs_str = ','.join(['%s'] * len(bus_id_olds))
        # 查询新ID
        sql = f'''
                select `id`, `source_name`, `bus_id_old`, `bus_id_new` from {self.db}.{self.table} 
                where source_name="{source_name}" and `bus_id_old` in ({bfhs_str})
            '''
        data_src: MysqlResult = self.db_mysql.read(sql, value=bus_id_olds, return_dict=True)
        results: Sequence[dict] = data_src.data
        if not results:
            return {}
        data = {}
        for result in results:
            bus_id_old = result['bus_id_old']
            bus_id_new = result['bus_id_new']
            data[bus_id_old] = bus_id_new
        return data
