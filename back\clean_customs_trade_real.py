#!/usr/bin/env python
# -*- coding: utf-8 -*-


import time
from typing import Union
from common.utils.public_funcs import get_address_region
from common.database import MysqlConnectionManager
from preprocess import PreprocessBase
from models.preprocess_model.customs import *
from curd.customs_gz.db_customs import tb_area, t_company
from curd.company_gz import db_std_map
from settings import *
from public.public_clean_main import PublicCleanMain
from models.preprocess_model.public import *
import concurrent.futures
import traceback


class CleanCustomsTrade(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.mysql_obj_customs_gz = mysql_manager.get_pool("customs_gz")
        self.mysql_obj_company_gz = mysql_manager.get_pool("company_gz")
        self.mysql_obj_customs_new_gz = mysql_manager.get_pool("customs_new_gz")
        self.source_name = 'customs_trade'
        self.concat_source = '海关公司'
        self.customs_db = "db_customs"
        self.customs_company_table = "t_company"
        self.customs_trade_table = "t_trade"
        self.table_country_obj = tb_area.TableArea(self.mysql_obj_customs_gz)
        self.table_company_obj = t_company.TableCompany(self.mysql_obj_customs_gz)
        # 国家id与国家相关信息映射
        self.country_id_map = self.table_country_obj.get_country_id_map()
        self.public_clean_main_obj = PublicCleanMain(mysql_manager)
        self.std_arad_obj = db_std_map.tb_area.TableArea(self.mysql_obj_company_gz)
        self.province_map = self.std_arad_obj.get_province_map()
        self.city_map = self.std_arad_obj.get_city_map()

    def parse_address_str(self, one):
        """
        海关数据的地址较为脏乱
        当地址字段为空时，则使用国家、省份、城市字段信息进行拼凑再去检验
        """
        country_id = one["country_id"]
        country = one["country"]
        province = one["province"]
        city = one["city"]
        source_address = one["address"]
        if not source_address:
            # 只有国家字段有信息则不用，要有省或城市字段信息
            if not province or not city:
                address_1 = f'''{city}, {province}, {country}'''
                address_2 = ""
            else:
                return "", "", ""
        else:
            if country in source_address:
                address_1 = source_address
                address_2 = ""
            else:
                address_1 = f'''{source_address}, {country}'''
                address_2 = source_address
        if country_id != 0:
            coutry_code = self.country_id_map[country_id]["country_iso_code"]
        else:
            coutry_code = ""
        return address_1, address_2, coutry_code

    def parse_thread(self, one):
        """
        是否添加国家名称，都要校验，当不添加国家名称，有省份信息的则选择使用，只有国家信息则不用，
        优先使用不加国家名称的地址，也就是address_2
        """
        try:
            address_1, address_2, coutry_code = self.parse_address_str(one)
            if not address_1:
                return {one["openid"]: {}}
            if address_2:
                area_infos_dict = get_address_region(address_2)
            else:
                area_infos_dict = {}
            if not area_infos_dict:
                area_infos_dict = get_address_region(address_1)
            if not area_infos_dict:
                return {one["openid"]: {}}
            if coutry_code and not area_infos_dict["province_en"]:
                # 源数据已有国家信息，但解析出来的地址也只有国家信息时，不写入
                # 只有当源数据没有国家信息，而解析出来的地址只有国家信息时，才写入
                return {one["openid"]: {}}
            area_infos_dict["source_country_code_iso2"] = coutry_code
            area_infos_dict["identify_country_code_iso2"] = area_infos_dict["country_code_iso2"]
            return {one["openid"]: area_infos_dict}
        except Exception as e:
            logging.error(f'''异常：{e}''')
            logging.error(traceback.format_exc())

    def parse_address_info(self, company_info_list):
        """
        是否添加国家名称，都要校验，当不添加国家名称，有省份信息的则选择使用，只有国家信息则不用，
        优先使用不加国家名称的地址，也就是address_2
        """
        company_address_identify_map = {}
        with concurrent.futures.ThreadPoolExecutor(10) as executor:
            futures = []
            for company_info in company_info_list:
                if not company_info:
                    continue
                futures.append(executor.submit(self.parse_thread, company_info))
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                company_address_identify_map.update(result)
        return company_address_identify_map

    def parse_company_obj(self, company_info, country_code, openid, company_identify_info_map):
        if not company_info:
            return None
        country_id = self.country_id_map.get(country_code.lower(), {}).get("id", 0)
        country = self.country_id_map.get(country_code.lower(), {}).get("country_en", "")
        if country_id != company_info["country_id"]:
            province = company_identify_info_map.get(openid, {}).get("province_en", "")
            province_code = company_identify_info_map.get(openid, {}).get("province_code", "")
            province_code = int(province_code) if province_code.isdigit() else province_code
            province_id = self.province_map.get(province_code, {}).get("id", 0)
            city = company_identify_info_map.get(openid, {}).get("city_en", "")
            city_id = self.city_map.get(city.lower(), {}).get("id", 0)  # TODO 不同国家同个城市名称
        else:
            province = company_info["province"]
            province_id = company_info["province_id"]
            city = company_info["city"]
            city_id = company_info["city_id"]
        return PreprocessCustomsCompanyMain(
            source=company_info.get("source", 0),
            openid=company_info.get("openid", ''),
            uniqueid=company_info.get("uniqueid", ''),
            name=company_info.get("name", ''),
            name_new=company_info.get("name_new", ''),
            logo=company_info.get("logo", ''),
            introduce=company_info.get("introduce", ''),
            industry=company_info.get("industry", ''),
            scope=company_info.get("scope", ''),
            attach=company_info.get("attach", ''),
            texture=company_info.get("texture", ''),
            location=company_info.get("location", ''),
            country_id=country_id,
            province_id=province_id,
            city_id=city_id,
            country=country,
            province=province,
            city=city,
            postcode=company_info.get("postcode", ''),
            is_ws=company_info.get("is_ws", 0),
            address=[PreprocessAddress(address=company_info["address"], postal_code=company_info.get("postcode", ""))],
            phone=[PreprocessPhone(phone_raw=company_info["phone"], source_name=self.concat_source)],
            email=[PreprocessEmail(email=company_info["email"], source_name=self.concat_source)],  # TODO 是否需要加上状态
            website=[PreprocessWebsite(website=company_info["website"], source_name=self.concat_source)],
            fburl=[PreprocessSocial(social_url=company_info.get("fburl", ""), social_type="facebook", source_name=self.concat_source)],
            tturl=[PreprocessSocial(social_url=company_info.get("tturl", ""), social_type="twitter", source_name=self.concat_source)],
            isurl=[PreprocessSocial(social_url=company_info.get("isurl", ""), social_type="instagram", source_name=self.concat_source)],
            liurl=[PreprocessSocial(social_url=company_info.get("liurl", ""), social_type="linkedin", source_name=self.concat_source)],
            yturl=[PreprocessSocial(social_url=company_info.get("yturl", ""), social_type="youtube", source_name=self.concat_source)],
            pturl=[PreprocessSocial(social_url=company_info.get("pturl", ""), social_type="pinterest", source_name=self.concat_source)],
            tkurl=[PreprocessSocial(social_url=company_info.get("tkurl", ""), social_type="tiktok", source_name=self.concat_source)],
            register_person=company_info.get("register_person", ''),
            register_number=company_info.get("register_number", ''),
            register_date=company_info.get("register_date", ''),
            register_type=company_info.get("register_type", ''),
            register_capital=company_info.get("register_capital", ''),
            register_state=company_info.get("register_state", ''),
            company_type=company_info.get("company_type", 0),
            repair_method=company_info.get("repair_method", 0),
        )

    def deal_data(self, data: dict, **kwargs) -> Union[PreprocessCustomsTradeMain, None]:
        """
        处理单条数据
        :param data: 
        :return: 
        """
        trade_id = data.get('trade_id', 0)
        trade_date = data.get('trade_date', 0)
        company_identify_info_map = kwargs.get('company_identify_info_map', {})
        # 过滤贸易日期小于 2000-01-01 的数据
        if not trade_date or trade_date < 946656000000:
            return None
        if data["source"] == 4 and data["product_tag"] == data["product_desc"]:
            data["product_tag"] = ""
        trade_date = trade_date / 1000
        origin_country = data.get('seller_country', '')
        arrival_country = data.get('buyer_country', '')
        seller_country_id = data.get('seller_country_id', 0)
        buyer_country_id = data.get('buyer_country_id', 0)
        product_country_id = data.get('product_country_id', 0)
        seller_info = data["seller_info"]
        seller_openid = seller_info.get("openid", "")
        buyer_info = data["buyer_info"]
        buyer_openid = buyer_info.get("openid", "")
        product_country_code = self.country_id_map.get(product_country_id, {}).get("country_iso_code", "")
        origin_country_code = self.country_id_map.get(seller_country_id, {}).get("country_iso_code", "")
        arrival_country_code = self.country_id_map.get(buyer_country_id, {}).get("country_iso_code", "")
        seller_country_code = self.country_id_map[seller_info.get("country_id", 0)]["country_iso_code"] if seller_info else ""
        buyer_country_code = self.country_id_map[buyer_info.get("country_id", 0)]["country_iso_code"] if buyer_info else ""
        seller_country_code = company_identify_info_map.get(seller_openid, {}).get("identify_country_code_iso2", "") if not seller_country_code else seller_country_code
        buyer_country_code = company_identify_info_map.get(buyer_openid, {}).get("identify_country_code_iso2", "") if not buyer_country_code else buyer_country_code

        seller_company_info = self.parse_company_obj(
            seller_info,
            seller_country_code,
            seller_openid,
            company_identify_info_map
        )
        buyer_company_info = self.parse_company_obj(
            buyer_info,
            seller_country_code,
            buyer_openid,
            company_identify_info_map
        )

        return PreprocessCustomsTradeMain(
            trade_id=trade_id,
            trade_date=trade_date,
            source=data.get('source', 0),
            openid=data.get('openid', 0),
            trade_code=data.get('trade_code', ''),
            weight=data.get('weight', 0),
            weight_unit=data.get('weight_unit'),
            quantity=data.get('quantity', 0),
            quantity_unit=data.get('quantity_unit', ''),
            price=data.get('price', 0),
            amount=data.get('amount', 0),
            amount_unit=data.get('amount_unit', ''),
            product_country=data.get('product_country', ''),
            product_country_code=product_country_code,
            product_desc=data.get('product_desc', ''),
            product_tag=data.get('product_tag', ''),
            hscode=data.get('product_hscode', ''),
            seller_id=0,
            seller=seller_info.get('name', ''),
            seller_country=self.country_id_map.get(seller_country_code.lower(), {}).get("country_en", ""),
            seller_country_code=seller_country_code,
            origin_country=origin_country,
            origin_country_code=origin_country_code,
            origin_port=data.get('seller_port', ''),
            buyer_id=0,
            buyer=buyer_info.get('name', ''),
            buyer_country=self.country_id_map.get(buyer_country_code.lower(), {}).get("country_en", ""),
            buyer_country_code=buyer_country_code,
            arrival_country=arrival_country,
            arrival_country_code=arrival_country_code,
            arrival_port=data.get('buyer_port', ''),
            notifier=data.get('notifier', ''),
            container=data.get('container', ''),
            transport=data.get('transport', ''),
            trade_type=data.get('trade_type', 4),
            create_time=str(data.get('create_time', "")),
            update_time=str(data.get('update_time', "")),
            seller_company_info=seller_company_info,
            buyer_company_info=buyer_company_info
        )

    def deal_datalist(self, datalist: list):
        """
        处理批量数据
        :param datalist: 
        :return: 
        """
        logger.info(f'start preprocess deal datalist ...')
        s_t = time.time()
        company_info_list = []
        c_id = set()
        for one in datalist:
            seller_openid = one["seller_openid"]
            buyer_openid = one["buyer_openid"]
            if seller_openid != "0" and seller_openid not in c_id:
                c_id.add(seller_openid)
                company_info_list.append(one["seller_info"])
            if buyer_openid != "0" and buyer_openid not in c_id:
                c_id.add(buyer_openid)
                company_info_list.append(one["buyer_info"])
        # 公司地址解析
        company_identify_info_map = self.parse_address_info(company_info_list)

        preprocess_customs_trade_main_obj_list = []
        for data in datalist:
            preprocess_custom_trade_data = self.deal_data(data, company_identify_info_map=company_identify_info_map)
            if not preprocess_custom_trade_data:
                continue
            preprocess_customs_trade_main_obj_list.append(preprocess_custom_trade_data)
        logger.info(f'end preprocess deal datalist === hs: {time.time() - s_t:.2f} s')
        # 执行公共清洗
        self.public_clean_main_obj.main_clean_customs_trade(preprocess_customs_trade_main_obj_list)

    def test_unit(self):
        # sql = f'''select * from {self.customs_db}.{self.customs_trade_table} order by trade_id limit 1000'''
        sql = f'''select * from {self.customs_db}.{self.customs_trade_table} where trade_id in (64554, 64649, 230364, 230367, 230368, 230369, 230371)'''
        # sql = f'''select * from {self.customs_db}.{self.customs_trade_table} where trade_id=1763113529'''
        data_src = self.mysql_obj_customs_gz.read(sql, return_dict=True)
        results: List[dict] = data_src.data
        self.deal_datalist(results)


if __name__ == '__main__':
    from models import ConfMySQL

    conf_list = [
        ConfMySQL(name='customs_new_gz', max_connections=1),
        ConfMySQL(name='customs_gz', max_connections=1),
        ConfMySQL(name='company_gz', max_connections=1),
    ]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    CleanCustomsTrade(mysql_manager).test_unit()
