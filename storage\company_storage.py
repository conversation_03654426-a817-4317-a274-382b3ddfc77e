#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: company_storage.py
@Date: 2023/12/22
@Desc: 
@Server: 
"""
import pymysql
from storage.public_storage import Storage


class CompanyStorage(Storage):
    """
    公司基本信息表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)


class CompanyAddressesStorage(Storage):
    """
    公司地址表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)


class CompanyAreaInfoStorage(Storage):
    """
    公司地区详细表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)


class CompanyIndustryStorage(Storage):
    """
    公司行业表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)


class CompanyNamesStorage(Storage):
    """
    公司非实体现用名称表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)


class CompanyProductsStorage(Storage):
    """
    公司产品表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)


class CompanyStatusMapStorage(Storage):
    """
    公司状态映射表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)


class CompanyTypeMapStorage(Storage):
    """
    公司类型映射表 数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        super().__init__(table_name, db, mysql_obj, duplicate_cols)
