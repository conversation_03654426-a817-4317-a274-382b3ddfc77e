#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_base_information_v2.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableCompanyBaseInformationV2(TableBase):
    db = 'db_comp'
    table = 'company_base_information_v2'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_overview_business_scope(self, cids: list) -> dict:
        """
        获取 公司 简介与经营范围
        :param cids:
        :return:
        """
        if not cids:
            return {}
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select pid,trade_en,trade_orig,product_service,overview,businessline,main_product_servers,sectors from {self.db}.{self.table} where pid in ({bfhs_str})
        '''
        data_src = self.db_mysql.read(sql, cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        cid_obs_map = {}
        for data in results:
            pid = data.get('pid', '')
            trade_en = data.get('trade_en', '')
            trade_orig = data.get('trade_orig', '')
            product_service = data.get('product_service', '')
            overview = data.get('overview', '')
            businessline = data.get('businessline', '')
            main_product_servers = data.get('main_product_servers', '')
            sectors = data.get('sectors', '')
            if not pid:
                continue
            # 简介
            business_scope = ''
            if product_service:
                business_scope = product_service
            elif businessline:
                business_scope = businessline
            elif main_product_servers:
                business_scope = main_product_servers
            elif sectors:
                business_scope = sectors
            elif trade_en:
                business_scope = trade_en
            elif trade_orig:
                business_scope = trade_orig
            cid_obs_map[pid] = {'overview': overview, 'business_scope': business_scope}
        return cid_obs_map
