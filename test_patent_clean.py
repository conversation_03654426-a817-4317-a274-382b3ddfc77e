#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: Claude 4.0 Sonnet
@File: test_patent_clean.py
@Date: 2025-01-28
@Desc: 专利数据清洗测试脚本（不涉及数据库操作）
@Server: 
"""
import json
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from models.preprocess_model.patent import *
from preprocess.sync_normalization.clean_patent import CleanPatent


class MockMysqlConnectionManager:
    """模拟数据库连接管理器"""
    
    def get_pool(self, name):
        return MockDBPool()


class MockDBPool:
    """模拟数据库连接池"""
    
    def read(self, sql, params=None, return_dict=True):
        return MockResult([])
    
    def save(self, table, items, ignore=True):
        return MockResult([])
    
    def execute(self, sql, params=None):
        return MockResult([])


class MockResult:
    """模拟查询结果"""
    
    def __init__(self, data):
        self.data = data
        self.affected_rows = len(data) if data else 0


def load_test_data():
    """加载测试数据"""
    test_data_files = [
        'test_data/db_spider_swissreg_patent_info.json',
        'test_data/db_spider_swissreg_ptent_registeradressen.json',
        'test_data/db_spider_swissreg_patent_ipc.json',
        'test_data/db_spider_swissreg_patent_cpc.json'
    ]
    
    all_test_data = []
    
    for file_path in test_data_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    all_test_data.extend(data[:5])  # 只取前5条数据进行测试
                    print(f"加载测试数据文件: {file_path}, 数量: {len(data[:5])}")
        except FileNotFoundError:
            print(f"测试数据文件不存在: {file_path}")
        except Exception as e:
            print(f"加载测试数据文件失败: {file_path}, 错误: {e}")
    
    return all_test_data


def test_patent_info_processing():
    """测试专利基础信息处理"""
    print("\n=== 测试专利基础信息处理 ===")
    
    # 模拟专利信息数据
    patent_info_data = {
        "id": 12487890,
        "open_id": "10434722",
        "patent_id": "EP3912864",
        "application_number": "EP20190123456",
        "publication_date": "12.03.2025",
        "application_date": "15.01.2020",
        "grant_date": "01.02.2025",
        "status": "Active",
        "ip_type": "Patent",
        "an_inheritance": 0,
        "International_registration": "PCT/EP2020/123456",
        "international_publication": "WO2021/123456",
        "Cancellation": "",
        "exhibition_immunity": "",
        "paediatric_SPC": ""
    }
    
    # 创建清洗器实例
    mock_manager = MockMysqlConnectionManager()
    cleaner = CleanPatent(mock_manager)
    
    # 处理数据
    patent_obj = cleaner.deal_patent_info_data(patent_info_data)
    
    if patent_obj:
        print(f"✓ 专利信息处理成功")
        print(f"  专利ID: {patent_obj.p_id}")
        print(f"  专利号: {patent_obj.patent_number}")
        print(f"  申请国家: {patent_obj.filing_country}")
        print(f"  公开日期: {patent_obj.publication_date}")
        print(f"  申请日期: {patent_obj.application_date}")
        print(f"  授权日期: {patent_obj.grant_date}")
        return True
    else:
        print("✗ 专利信息处理失败")
        return False


def test_patent_owner_processing():
    """测试专利所属关系处理"""
    print("\n=== 测试专利所属关系处理 ===")
    
    # 模拟所属关系数据
    owner_data_list = [
        {
            "id": 23235996,
            "open_id": "4854026",
            "owner_id": "18394445",
            "owner_type": "INHABER",  # 公司
            "name": "IDC Investment Development Corporation Limited",
            "additional_name": "",
            "street": "Room 2203\n22/F, Tower 1\nLippo Centre\n89 Queensway",
            "town": "Hong Kong",
            "country": "HK",
            "email": "",
            "phone_number": "",
            "title": ""
        },
        {
            "id": 22870914,
            "open_id": "4761325",
            "owner_id": "18348272",
            "owner_type": "ERFINDER",  # 个人发明者
            "name": "VOIGT, Christian",
            "additional_name": "",
            "street": "FTE automotive GmbH\nAndreas-Humann-Strasse 2",
            "town": "96106 Ebern",
            "country": "DE",
            "title": "Dr.",
            "email": "",
            "phone_number": ""
        }
    ]
    
    # 创建清洗器实例
    mock_manager = MockMysqlConnectionManager()
    cleaner = CleanPatent(mock_manager)
    
    success_count = 0
    company_count = 0
    human_count = 0
    
    for owner_data in owner_data_list:
        # 生成测试用的专利ID
        p_id = "test_patent_id_123"
        
        # 处理所属关系数据
        owner_obj = cleaner.deal_patent_owner_data(owner_data, p_id)
        
        if owner_obj:
            success_count += 1
            print(f"✓ 所属关系处理成功: {owner_obj.name}")
            print(f"  实体类型: {owner_obj.entity_type}")
            print(f"  所有者类型: {owner_obj.owner_type}")
            print(f"  国家: {owner_obj.country}")
            
            # 转换为公司或个人模型
            company_obj, people_obj = cleaner.convert_to_company_human(owner_obj)
            
            if company_obj:
                company_count += 1
                print(f"  ✓ 转换为公司模型: {company_obj.company_name}")
            
            if people_obj:
                human_count += 1
                print(f"  ✓ 转换为个人模型: {people_obj.name}")
            
            print()
        else:
            print(f"✗ 所属关系处理失败: {owner_data.get('name', 'Unknown')}")
    
    print(f"处理结果: 成功 {success_count}/{len(owner_data_list)}, 公司 {company_count}, 个人 {human_count}")
    return success_count == len(owner_data_list)


def test_patent_class_processing():
    """测试专利分类处理"""
    print("\n=== 测试专利分类处理 ===")
    
    # 模拟分类数据
    class_data = {
        "class_symbol": "A01B1/00",
        "class_system": "IPC",
        "class_version": "2023.01",
        "subclass": "A01B",
        "main_group": "1",
        "subgroup": "00",
        "class_date": "01.01.2023",
        "effective_date": "01.01.2023",
        "class_code": "A01",
        "level_depth": 4,
        "class_type": "invention",
        "class_status": "active",
        "description_en": "Hand tools for soil working",
        "is_primary": 1,
        "priority_order": 1
    }
    
    # 创建清洗器实例
    mock_manager = MockMysqlConnectionManager()
    cleaner = CleanPatent(mock_manager)
    
    # 生成测试用的专利ID
    p_id = "test_patent_id_123"
    
    # 处理分类数据
    class_obj = cleaner.deal_patent_class_data(class_data, p_id)
    
    if class_obj:
        print(f"✓ 专利分类处理成功")
        print(f"  分类ID: {class_obj.class_id}")
        print(f"  分类符号: {class_obj.class_symbol}")
        print(f"  分类体系: {class_obj.class_system}")
        print(f"  是否主要分类: {class_obj.is_primary}")
        print(f"  英文描述: {class_obj.description_en}")
        return True
    else:
        print("✗ 专利分类处理失败")
        return False


def test_batch_processing():
    """测试批量数据处理"""
    print("\n=== 测试批量数据处理 ===")
    
    # 加载真实测试数据
    test_data = load_test_data()
    
    if not test_data:
        print("✗ 没有可用的测试数据")
        return False
    
    # 创建清洗器实例
    mock_manager = MockMysqlConnectionManager()
    cleaner = CleanPatent(mock_manager)
    
    # 处理批量数据
    try:
        results = cleaner.deal_datalist(test_data)
        patent_list, owner_list, class_list, company_list, people_list = results
        
        print(f"✓ 批量数据处理成功")
        print(f"  输入数据量: {len(test_data)}")
        print(f"  专利信息: {len(patent_list)}")
        print(f"  所属关系: {len(owner_list)}")
        print(f"  分类信息: {len(class_list)}")
        print(f"  公司信息: {len(company_list)}")
        print(f"  个人信息: {len(people_list)}")
        
        # 显示一些样例数据
        if patent_list:
            print(f"\n专利样例: {patent_list[0].patent_number} ({patent_list[0].filing_country})")
        
        if owner_list:
            print(f"所属关系样例: {owner_list[0].name} ({owner_list[0].entity_type})")
        
        if company_list:
            print(f"公司样例: {company_list[0].company_name} ({company_list[0].country})")
        
        if people_list:
            print(f"个人样例: {people_list[0].name} ({people_list[0].country})")
        
        return True
        
    except Exception as e:
        print(f"✗ 批量数据处理失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始专利数据清洗测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_patent_info_processing())
    test_results.append(test_patent_owner_processing())
    test_results.append(test_patent_class_processing())
    test_results.append(test_batch_processing())
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    test_names = [
        "专利基础信息处理",
        "专利所属关系处理", 
        "专利分类处理",
        "批量数据处理"
    ]
    
    success_count = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(test_results)} 项测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！专利数据清洗功能实现正确。")
    else:
        print("⚠️  部分测试失败，请检查实现。")


if __name__ == "__main__":
    main()
