#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_linekdin_company_base_info_id.py
@Date: 2024/5/7
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase, MysqlResult
from typing import List, Sequence


class TableLinkCompanyBaseInformation(TableBase):
    db = 'db_link'
    table = 'linekdin_company_base_info_id'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_company_information(self, cids: list) -> dict[str, dict]:
        """
        获取公司 照面信息
        :param cids: 公司ID列表
        :return:
        """
        if not cids:
            return {}
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select *
            from {self.db}.{self.table} 
            where job_company_id in ({bfhs_str})
        '''
        data_src = self.db_mysql.read(sql, cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        company_information_data: dict = {}
        for result in results:
            job_company_id = result.get('job_company_id', '')
            company_name = result.get('job_company_name', '')
            if not job_company_id or not company_name:
                continue
            company_information_data[job_company_id] = result
        return company_information_data
