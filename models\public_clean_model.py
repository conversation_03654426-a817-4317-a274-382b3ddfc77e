#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public_clean_model.py
@Date: 2024/2/7
@Desc: 公共清洗模型
@Server: 
"""
from typing import List, Union
from .db_company import *
from .db_concat import *
from .db_human import *
from .db_customs import CustomsTrade
from .db_customs import CustomsCompany


@dataclass
class PublicCleanConcatMain:
    """
    联系方式 公共清洗方法 模型
    """
    emails_obj_list: Union[List[EmailsAll], None] = None
    phones_obj_list: Union[List[PhonesAll], None] = None
    socials_obj_list: Union[List[SocialsAll], None] = None
    websites_obj_list: Union[List[WebsitesAll], None] = None
    business_emails_obj_list: Union[List[BusinessEmailsAll], None] = None
    business_phones_obj_list: Union[List[BusinessPhonesAll], None] = None
    business_socials_obj_list: Union[List[BusinessSocialsAll], None] = None
    business_websites_obj_list: Union[List[BusinessWebsitesAll], None] = None


@dataclass
class PublicCleanSchoolMain:
    """
    学校 公共清洗方法 模型
    """
    school_obj: School
    bus_id_map_obj: Union[BusIdMapSchool, None] = None  # 学校 新旧 ID
    concat_obj: Union[PublicCleanConcatMain, None] = None  # 学校 联系方式

    def __post_init__(self):
        pass


@dataclass
class PublicCleanHumanMain:
    """
    人物 公共清洗方法 模型
    """
    human_obj: Human
    bus_id_map_obj: Union[BusIdMapHuman, None] = None     # 人物 新旧 ID
    human_logo_obj_list: Union[List[HumanLogo], None] = None
    human_addresses_obj_list: Union[List[HumanAddresses], None] = None
    human_education_obj_list: Union[List[HumanEducation], None] = None
    human_experience_obj_list: Union[List[HumanExperience], None] = None
    concat_obj: Union[PublicCleanConcatMain, None] = None   # 人物联系方式

    def __post_init__(self):
        pass


@dataclass
class PublicCleanCompanyMain:
    """
    公司 公共清洗方法 模型
    """
    company_obj: Company
    company_status_map_obj: CompanyStatusMap = None
    company_type_map_obj: CompanyTypeMap = None
    company_area_info_obj: CompanyAreaInfo = None
    company_logo_obj_list: Union[List[CompanyLogo], None] = None
    bus_id_map_obj_list: Union[List[BusIdMapCompany], None] = None     # 公司新旧 ID
    company_addresses_obj_list: Union[List[CompanyAddresses], None] = None
    company_industry_obj_list: Union[List[CompanyIndustry], None] = None
    company_names_obj_list: Union[List[CompanyNames], None] = None
    company_products_obj_list: Union[List[CompanyProducts], None] = None
    company_stock_obj_list: Union[List[CompanyStock], None] = None
    company_shareholders_obj_list: Union[List[CompanyShareholders], None] = None
    company_subsidiary_obj_list: Union[List[CompanySubsidiary], None] = None
    company_national_identifiers_obj_list: Union[List[CompanyNationalIdentifiers], None] = None
    company_tech_stack_obj_list: Union[List[CompanyTechStack], None] = None
    concat_obj: Union[PublicCleanConcatMain, None] = None   # 公司联系方式

    def __post_init__(self):
        pass


@dataclass
class PublicCleanCustomsTradeMain:
    """
    海关贸易 公共清洗方法 模型
    """
    customs_trade_obj: CustomsTrade
    customs_company_obj_list: Union[List[CustomsCompany], None] = None

    def __post_init__(self):
        pass
