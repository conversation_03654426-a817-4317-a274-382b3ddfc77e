#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_base_information.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence


class TableCompanyBaseInformation(TableBase):
    db = 'db_comp'
    table = 'company_base_information'
    table_old = 'company_base_information_old'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_company_information(self, cids: list) -> dict[str, dict]:
        """
        获取公司 照面信息
        :param cids: 公司ID列表
        :return:
        """
        if not cids:
            return {}
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select *
            from {self.db}.{self.table} 
            where pid in ({bfhs_str})
        '''
        data_src = self.db_mysql.read(sql, cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        company_information_data: dict = {}
        for result in results:
            pid = result.get('pid', '')
            company_name = result.get('company_name', '')
            if not pid or not company_name:
                continue
            company_information_data[pid] = result
        return company_information_data

    def get_company_information_old(self, cids: list, cols: list = None) -> dict[str, dict]:
        """
        获取公司 照面信息
        :param cids: 公司ID列表
        :param cols: 要查询的字段列表
        :return:
        """
        if not cids:
            return {}
        if not cols:
            cols_str = '*'
        else:
            cols_str = ','.join(cols)
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select {cols_str}
            from {self.db}.{self.table_old} 
            where pid in ({bfhs_str})
        '''
        data_src = self.db_mysql.read(sql, cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        company_information_data: dict = {}
        for result in results:
            pid = result.get('pid', '')
            company_name = result.get('company_name', '')
            if not pid or not company_name:
                continue
            company_information_data[pid] = result
        return company_information_data
