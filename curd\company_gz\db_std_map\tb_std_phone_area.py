from curd.tb_base import TableBase
from typing import Optional
from .tb_area import TableArea
from settings import logger


class TableSTDPhone(TableBase):
    db = 'db_std_map'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

        self.db_mysql_mysql = db_mysql
        self.map_phone_are: Optional[dict] = None
        self.table_phone_are = f"{self.db}.std_phone_area"

        self.table_area = TableArea(db_mysql)
        self.init()

    def init(self):
        """初始化映射表"""
        if self.map_phone_are is None:
            self.map_phone_are = dict()
            # read country
            map_country = self.table_area.get_country_map()  # 需要不同的国家信息格式修改这里
            # read table_province
            map_province = self.table_area.get_province_map()
            # read table_phone_are
            sql = f"select country_code, dialing_code, area_code, province_id from {self.table_phone_are}"
            result = self.db_mysql.read(sql, return_dict=True)
            # 初始化dialing_code
            for row in result.data:
                self.map_phone_are[row['dialing_code']] = 1
            # 具体映射
            for row in result.data:
                country_code = row['country_code']
                dialing_code = row['dialing_code']
                area_code = row['area_code']
                province_id = row['province_id']
                key = dialing_code if area_code == '' else dialing_code + area_code

                info = dict()
                info['dialing_code'] = dialing_code
                info['area_code'] = area_code
                country_info = map_country.get(country_code.lower(), dict())
                info.update(country_info)
                province_list = list()
                if province_id != '':
                    for p_id in province_id.split(','):
                        if r := map_province.get(int(p_id)):
                            province_list.append(r)
                info['province_list'] = province_list

                self.map_phone_are[key] = info

    def check_number_area(self, number: str) -> dict:
        """识别纯数字号码的地区信息"""
        logger.debug(number)
        for i in range(1, len(number) + 1):
            dialing_code = number[:i]
            # print(f"cur:{cur}")
            info = self.map_phone_are.get(dialing_code)
            if info is None:
                continue
            elif info == 1:
                if dialing_code == '1':
                    return self.cut_area(dialing_code, number, 3)
                elif dialing_code == '7':
                    return self.cut_area(dialing_code, number, 1)
            elif isinstance(info, dict):
                if dialing_code == '44':
                    return self.cut_area(dialing_code, number, 4, info)
                elif dialing_code == '47':
                    return self.cut_area(dialing_code, number, 2, info)
                elif dialing_code == '290':
                    return self.cut_area(dialing_code, number, 1, info)
                else:
                    return self.cut_area(dialing_code, number, 0, info)
        return dict()

    def cut_area(self, dialing_code: str, number: str, area_len: int = 0, info: Optional[dict] = None) -> dict:
        if area_len == 0:
            cur_res = info
            key = dialing_code
        else:
            area_code = number[len(dialing_code):].lstrip('0')[:area_len]
            key = dialing_code + area_code
            cur_res = self.map_phone_are.get(key)
            if cur_res is None:
                if info is None:
                    return dict()
                else:
                    cur_res = info
        if isinstance(cur_res, dict):
            cur_res['short_number'] = number[len(key):]
        else:
            cur_res = dict()
        return cur_res
