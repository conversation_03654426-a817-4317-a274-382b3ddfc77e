# 日志
log:
  level: info
  # logging
  only_stdout: true
  # logger
  record_warning: false  # 是否记录warning日志到文件
  pid: true  # 是否打印进程号
  tid: false  # 是否打印线程号

# mysql地址
mysql:  # 内网
  customs_gz:  # 名字全局唯一，不要修改
    host: *********
    port: 3306
    user: data_server
    passwd: 7VSZ~a5qQ@mjsE
    charset: utf8mb4
  company_gz:  # 名字全局唯一，不要修改
    host: *********
    port: 3306
    user: data_server
    passwd: 7VSZ~a5qQ@mjsE
    charset: utf8mb4
  # customs_new_gz:  # 名字全局唯一，不要修改
  #   host: ********
  #   port: 3306
  #   user: data_server
  #   passwd: 7VSZ~a5qQ@mjsE
  #   charset: utf8mb4
  sp02:
    host: *********
    port: 3306
    user: spider
    passwd: spider
    charset: utf8mb4
  # company_new_gz: # 名字全局唯一，不要修改
  #   host: *********
  #   port: 3306
  #   user: data_server
  #   passwd: 7VSZ~a5qQ@mjsE
  #   charset: utf8mb4

mysql_out:  # 外网
  customs_gz: # 名字全局唯一，不要修改
    host: gz-tdsql-cynmis5n.sql.tencentcdb.com
    port: 23149
    user: data_server
    passwd: 7VSZ~a5qQ@mjsE
    charset: utf8mb4
  company_gz: # 名字全局唯一，不要修改
    host: gz-cynosdbmysql-grp-2fvhoit5.sql.tencentcdb.com
    port: 26702
    user: data_server
    passwd: 7VSZ~a5qQ@mjsE
    charset: utf8mb4
  # customs_new_gz: # 名字全局唯一，不要修改
  #   host: gz-tdsql-8ojrgffr.sql.tencentcdb.com
  #   port: 70
  #   user: data_server
  #   passwd: 7VSZ~a5qQ@mjsE
  #   charset: utf8mb4
  sp02:
    host: ***************
    port: 3306
    user: spider
    passwd: spider
    charset: utf8mb4

tables:
  record:   # 记录总表 company_gz db_source_statistics
    name: company_gz
    db: db_source_statistics
    table: record_data_process
  company:   # 公司总表
    name: company_new_gz
    db:
    table:
  person:   # 人物总表
    name: company_new_gz
    db:
    table:
  trade:   # 贸易总表
    name: customs_gz
    db: db_customers
    table:

# redis地址
redis:
  local:
    url: redis://:test@**********:6379/0

split_size: 100000  # 任务切片大小，弃用

# 预留参数
use_proxy: false
workers: 1
queue_max_size: 20
timeout: 15
