# 数据全流程架构
## 框架
- /bin # 框架启动任务
- /common # 工具箱
- /curd # 数据库读写操作
  - \_\_init__.py
  - tb_base.py # 数据库读写操作继承类
  - /company_gz # 公司广州mysql连接实例
    - /db_company_new # 公司广州mysql连接实例下的db_company_new库
  - /customs_gz # 海关广州mysql连接实例
    - /db_customs # 海关广州mysql连接实例下的db_customs库
- /exceptions # 自定义异常
- /preprocess # 各预处理项目
  - \_\_init__.py
  - \_init.py # 预处理方法管理器;自动注册当前目录下所有包含preprocess_name属性值的类
  - base.py # 预处理方法继承类
  - /spider # 爬虫预处理项目
  - /sync_normalization # 数据同步预处理项目
- /models # 各数据模型
  - \_\_init__.py
  - db_company.py # 公司库模型
- /storage # 存储模块
  - \_\_init__.py
  - public_storage.py # 公共存储模块
  - company_storage.py # 公司库存储模块
- connection_instantiation.py # 服务初始化模块
- public_funcs.py # 公共方法模块
