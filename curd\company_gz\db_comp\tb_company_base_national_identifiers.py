#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_base_national_identifiers.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List


class TableCompanyBaseNationalIdentifiers(TableBase):
    db = 'db_comp'
    table = 'company_base_national_identifiers'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_register_number(self, cids: list, nat_label_ids: tuple = None) -> List[dict]:
        """
        获取公司注册号
        :param cids: 公司ID列表
        :param nat_label_ids: 注册号类型列表
        :return:
        """
        if not cids:
            return []
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select pid,nat_id,nat_label_id from {self.db}.{self.table} where pid in ({bfhs_str})
        '''
        if nat_label_ids:
            sql += f''' and nat_label_id in {nat_label_ids} '''
        sql += f''' order by nat_label_id '''
        data_src = self.db_mysql.read(sql, cids, return_dict=True)
        results: List[dict] = data_src.data
        return results

