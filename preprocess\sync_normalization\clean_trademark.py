#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: clean_trademark.py
@Date: 2025-01-28
@Desc: 清洗商标数据
@Server: 
"""
import os
import sys
import time
import json
from typing import List, Dict, Tuple, Union, Optional
from dataclasses import asdict

# 添加项目根目录到路径
sys.path.append('../../')

from settings import *
from preprocess import PreprocessBase
from models.preprocess_model.trademark import *
from models.preprocess_model.company import PreprocessCompanyMain
from models.preprocess_model.human import PreprocessPeopleMain
from curd.trademark_gz.db_trademark_staging import TableTrademark, TableTrademarkOwner, TableTrademarkClass, TableTrademarkDedup, TableTrademarkTaskRecord
from common.database import MysqlConnectionManager
from common.utils import clean_key_fields, clean_key_fields_hashdata


class CleanTrademark(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        
        # 初始化数据库连接
        self.mysql_obj_trademark_gz = mysql_manager.get_pool('trademark_gz')
        self.mysql_obj_spider_gz = mysql_manager.get_pool('spider_gz')  # 源数据连接
        
        # 初始化表操作对象
        self.table_trademark = TableTrademark(self.mysql_obj_trademark_gz)
        self.table_trademark_owner = TableTrademarkOwner(self.mysql_obj_trademark_gz)
        self.table_trademark_class = TableTrademarkClass(self.mysql_obj_trademark_gz)
        self.table_trademark_dedup = TableTrademarkDedup(self.mysql_obj_trademark_gz)
        self.table_trademark_task_record = TableTrademarkTaskRecord(self.mysql_obj_trademark_gz)
        
        # 任务记录ID
        self.current_task_id = None

    def clean_date_format(self, date_str: str) -> str:
        """
        清洗日期格式
        """
        if not date_str:
            return ''
        
        # 处理常见的日期格式
        date_str = date_str.strip()
        # 将 DD.MM.YYYY 格式转换为 YYYY-MM-DD
        if '.' in date_str and len(date_str) == 10:
            parts = date_str.split('.')
            if len(parts) == 3:
                day, month, year = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        return date_str

    def determine_filing_country(self, data: dict) -> str:
        """
        确定备案国家代码
        """
        # 根据数据源或其他信息确定备案国家
        # WIPO数据通常是国际商标
        return 'WO'  # World Intellectual Property Organization

    def determine_owner_type(self, name: str, address: str = '') -> str:
        """
        根据名称和地址判断是公司还是个人
        """
        if not name:
            return 'company'  # 默认为公司
        
        name_lower = name.lower()
        
        # 公司关键词
        company_keywords = [
            'ltd', 'limited', 'inc', 'incorporated', 'corp', 'corporation',
            'llc', 'gmbh', 'ag', 'sa', 'bv', 'nv', 'ab', 'oy', 'as',
            'company', 'co.', 'group', 'holdings', 'enterprises'
        ]
        
        # 检查是否包含公司关键词
        for keyword in company_keywords:
            if keyword in name_lower:
                return 'company'
        
        # 检查是否包含个人姓名特征（简单判断）
        if ',' in name and len(name.split(',')) == 2:
            # 格式如 "Smith, John" 可能是个人
            return 'human'
        
        # 默认为公司
        return 'company'

    def deal_trademark_info_data(self, data: dict) -> Optional[PreprocessTrademarkMain]:
        """
        处理商标基础信息数据
        """
        try:
            # 提取基础字段
            source_id = str(data.get('id', ''))
            if not source_id:
                return None
            
            trademark_name = clean_key_fields(data.get('brand_name', ''))
            if not trademark_name:
                return None
            
            application_date = self.clean_date_format(data.get('application_date', ''))
            registration_date = self.clean_date_format(data.get('registration_date', ''))
            publication_date = self.clean_date_format(data.get('publication_date', ''))
            expiry_date = self.clean_date_format(data.get('expiry_date', ''))
            
            # 确定备案国家
            filing_country = self.determine_filing_country(data)
            
            # 创建预处理模型
            trademark_obj = PreprocessTrademarkMain(
                source_type=1,  # wipo
                source_id=source_id,
                trademark_name=trademark_name,
                logo=data.get('logo', ''),
                category=data.get('category', ''),
                status=data.get('status', ''),
                score=float(data.get('score', 0.0)),
                trademark_type=data.get('trademark_type', ''),
                office_status=data.get('office_status', ''),
                gbd_status=data.get('gbd_status', ''),
                filing_country=filing_country,
                application_number=data.get('application_number', ''),
                application_date=application_date,
                registration_number=data.get('registration_number', ''),
                registration_date=registration_date,
                publication_date=publication_date,
                expiry_date=expiry_date,
                status_date=self.clean_date_format(data.get('status_date', '')),
                application_language=data.get('application_language', ''),
                introduction=data.get('introduction', '')
            )
            
            return trademark_obj
            
        except Exception as e:
            logger.error(f"处理商标基础信息数据失败: {e}, data: {data}")
            return None

    def deal_trademark_owner_data(self, data: dict, t_id: str) -> Optional[PreprocessTrademarkOwner]:
        """
        处理商标所属关系数据
        """
        try:
            owner_id = str(data.get('id', ''))
            if not owner_id or not t_id:
                return None
            
            owner_name = clean_key_fields(data.get('name', ''))
            if not owner_name:
                return None
            
            # 判断是公司还是个人
            owner_type = self.determine_owner_type(owner_name, data.get('address', ''))
            
            # 创建预处理模型
            owner_obj = PreprocessTrademarkOwner(
                t_id=t_id,
                owner_id=owner_id,
                owner_type=owner_type,
                owner_name=owner_name,
                relationship_type=data.get('kind', ''),
                address=data.get('address', ''),
                house_number=data.get('house_number', ''),
                zip_code=data.get('zip_code', ''),
                country=data.get('country', ''),
                province=data.get('province', ''),
                city=data.get('city', ''),
                title=data.get('title', ''),
                email=data.get('email', ''),
                phone=data.get('phone', ''),
                fax=data.get('fax', ''),
                website=data.get('website', ''),
                language=data.get('language', ''),
                modification=data.get('modification', '')
            )
            
            return owner_obj

        except Exception as e:
            logger.error(f"处理商标所属关系数据失败: {e}, data: {data}")
            return None

    def deal_trademark_class_data(self, data: dict, t_id: str) -> Optional[PreprocessTrademarkClass]:
        """
        处理商标分类数据
        """
        try:
            class_code = clean_key_fields(data.get('class_code', ''))
            if not class_code or not t_id:
                return None

            # 生成分类ID
            class_id = clean_key_fields_hashdata(f"{t_id}+{class_code}")

            # 创建预处理模型
            class_obj = PreprocessTrademarkClass(
                t_id=t_id,
                class_id=class_id,
                class_system=data.get('class_system', 'NICE'),
                class_kind=data.get('class_kind', ''),
                class_version=data.get('class_version', ''),
                class_code=class_code,
                class_number=class_code,  # 冗余字段
                description=data.get('description', ''),
                parent_class_code=data.get('parent_class_code', ''),
                level_depth=int(data.get('level_depth', 1)),
                status=data.get('status', 'active')
            )

            return class_obj

        except Exception as e:
            logger.error(f"处理商标分类数据失败: {e}, data: {data}")
            return None

    def convert_to_company_human(self, owner_obj: PreprocessTrademarkOwner) -> Tuple[Optional[PreprocessCompanyMain], Optional[PreprocessPeopleMain]]:
        """
        将商标所属关系转换为公司或个人模型
        """
        try:
            if owner_obj.owner_type == 'company':
                # 转换为公司模型
                company_obj = PreprocessCompanyMain(
                    source_name='trademark_wipo',
                    company_name=owner_obj.owner_name,
                    country=owner_obj.country,
                    province=owner_obj.province,
                    city=owner_obj.city,
                    bus_id_old=owner_obj.owner_id,
                    company_type='',
                    company_status='',
                    company_code='',
                    business_scope='',
                    overview='',
                    agent_name='',
                    revenue_usd=0,
                    employee=0,
                    company_logo_url='',
                    latitude='',
                    longitude='',
                    opgname=''
                )
                return company_obj, None

            elif owner_obj.owner_type == 'human':
                # 转换为个人模型
                people_obj = PreprocessPeopleMain(
                    source_name='trademark_wipo',
                    name=owner_obj.owner_name,
                    country=owner_obj.country,
                    province=owner_obj.province,
                    city=owner_obj.city,
                    bus_id_old=owner_obj.owner_id,
                    gender='',
                    age=0,
                    birthday='',
                    education='',
                    occupation='Trademark Owner',
                    company_name='',
                    position=owner_obj.title,
                    salary_usd=0,
                    experience_years=0,
                    skills='',
                    bio='',
                    avatar_url='',
                    latitude='',
                    longitude=''
                )
                return None, people_obj

        except Exception as e:
            logger.error(f"转换公司/个人模型失败: {e}, owner: {owner_obj}")

        return None, None

    def get_related_data_by_source_ids(self, source_ids: List[str]) -> Dict[str, Dict]:
        """
        根据source_id获取关联数据
        :param source_ids: source_id列表
        :return: 关联数据字典
        """
        if not source_ids:
            return {}

        logger.info(f"开始获取商标关联数据，source_id数量: {len(source_ids)}")

        # 去重source_ids
        source_ids = list(set(source_ids))
        placeholders = ','.join(['%s'] * len(source_ids))

        related_data = {}

        try:
            # 获取商标所属关系数据
            owner_sql = f'''
                SELECT id, brand_id, name, kind, address, house_number, zip_code,
                       country, province, city, title, email, phone, fax,
                       website, language, modification
                FROM db_spider_wipo.applicants_new
                WHERE brand_id IN ({placeholders})
            '''
            owner_data_src = self.mysql_obj_spider_gz.read(owner_sql, source_ids, return_dict=True)
            owner_results = owner_data_src.data

            # 获取商标分类数据（如果有分类表）
            # 这里假设有一个分类表，如果没有可以注释掉
            try:
                class_sql = f'''
                    SELECT brand_id, class_code, class_system, class_kind,
                           class_version, description, parent_class_code,
                           level_depth, status
                    FROM db_spider_wipo.brand_classes
                    WHERE brand_id IN ({placeholders})
                '''
                class_data_src = self.mysql_obj_spider_gz.read(class_sql, source_ids, return_dict=True)
                class_results = class_data_src.data
            except:
                # 如果没有分类表，使用空列表
                class_results = []

            # 组织关联数据
            for source_id in source_ids:
                related_data[source_id] = {
                    'owners': [],
                    'classes': []
                }

            # 填充所属关系数据
            for owner in owner_results:
                source_id = str(owner['brand_id'])
                if source_id in related_data:
                    related_data[source_id]['owners'].append(owner)

            # 填充分类数据
            for class_info in class_results:
                source_id = str(class_info['brand_id'])
                if source_id in related_data:
                    related_data[source_id]['classes'].append(class_info)

            logger.info(f"商标关联数据获取完成，所属关系: {len(owner_results)}, "
                       f"分类信息: {len(class_results)}")

        except Exception as e:
            logger.error(f"获取关联数据失败: {e}")

        return related_data

    def filter_duplicates(self, trademark_list: List[PreprocessTrademarkMain]) -> Tuple[List[PreprocessTrademarkMain], List[str]]:
        """
        过滤重复的商标数据
        :param trademark_list: 商标列表
        :return: (去重后的商标列表, 重复的商标ID列表)
        """
        if not trademark_list:
            return [], []

        # 提取所有商标ID
        t_id_list = [trademark.t_id for trademark in trademark_list if trademark.t_id]

        # 检查重复
        duplicate_t_ids = self.table_trademark_dedup.check_duplicate_by_t_ids(t_id_list)

        # 过滤重复数据
        filtered_trademarks = []
        duplicate_trademarks = []

        for trademark in trademark_list:
            if trademark.t_id in duplicate_t_ids:
                duplicate_trademarks.append(trademark.t_id)
            else:
                filtered_trademarks.append(trademark)

        # 记录去重信息
        if filtered_trademarks:
            dedup_records = []
            for trademark in filtered_trademarks:
                dedup_record = {
                    't_id': trademark.t_id,
                    'source_type': trademark.source_type,
                    'source_id': trademark.source_id,
                    'trademark_name': trademark.trademark_name,
                    'filing_country': trademark.filing_country
                }
                dedup_records.append(dedup_record)

            # 批量插入去重记录
            self.table_trademark_dedup.batch_insert_dedup_records(dedup_records)

        logger.info(f"去重完成：原始 {len(trademark_list)} 条，过滤后 {len(filtered_trademarks)} 条，重复 {len(duplicate_trademarks)} 条")

        return filtered_trademarks, duplicate_trademarks

    def deal_datalist(self, datalist: List[dict]):
        """
        处理批量数据（集成去重和任务记录功能）
        这是PreprocessBase要求的主要入口方法

        处理流程：
        1. 从brand_info_new表获取主数据（datalist）
        2. 根据source_id获取关联数据（owners, classes）
        3. 整合数据并进行清洗处理
        4. 保存到目标数据库
        """
        logger.info(f'开始处理商标数据批次，数量: {len(datalist)}')
        start_time = time.time()

        # 统计计数器
        success_count = 0
        failed_count = 0
        duplicate_count = 0

        # 初始化结果列表
        trademark_list = []
        owner_list = []
        class_list = []
        company_list = []
        people_list = []

        # 提取所有source_id
        source_ids = []
        trademark_info_map = {}

        for data in datalist:
            source_id = str(data.get('id', ''))
            if source_id:
                source_ids.append(source_id)
                trademark_info_map[source_id] = data

        # 获取关联数据
        related_data = self.get_related_data_by_source_ids(source_ids)

        # 处理每条商标信息数据
        for source_id, trademark_info in trademark_info_map.items():
            try:
                # 处理商标基础信息
                trademark_obj = self.deal_trademark_info_data(trademark_info)
                if trademark_obj:
                    trademark_list.append(trademark_obj)
                    t_id = trademark_obj.t_id

                    # 获取该商标的关联数据
                    trademark_related = related_data.get(source_id, {})

                    # 处理商标所属关系
                    owners = trademark_related.get('owners', [])
                    for owner_data in owners:
                        owner_obj = self.deal_trademark_owner_data(owner_data, t_id)
                        if owner_obj:
                            owner_list.append(owner_obj)

                            # 转换为公司或个人模型
                            company_obj, people_obj = self.convert_to_company_human(owner_obj)
                            if company_obj:
                                company_list.append(company_obj)
                            if people_obj:
                                people_list.append(people_obj)

                    # 处理商标分类
                    classes = trademark_related.get('classes', [])
                    for class_data in classes:
                        class_obj = self.deal_trademark_class_data(class_data, t_id)
                        if class_obj:
                            class_list.append(class_obj)

                    success_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                logger.error(f"处理商标数据失败: {e}, source_id: {source_id}")
                failed_count += 1
                continue

        # 对商标数据进行去重
        if trademark_list:
            filtered_trademarks, duplicate_t_ids = self.filter_duplicates(trademark_list)
            trademark_list = filtered_trademarks
            duplicate_count = len(duplicate_t_ids)

        # 批量保存数据到数据库
        self.save_processed_data(trademark_list, owner_list, class_list, company_list, people_list)

        # 更新任务记录
        if self.current_task_id:
            self.complete_task_record(success_count, failed_count, duplicate_count)

        end_time = time.time()
        logger.info(f'商标数据处理完成，耗时: {end_time - start_time:.2f}s, '
                   f'商标: {len(trademark_list)}, 所属: {len(owner_list)}, 分类: {len(class_list)}, '
                   f'公司: {len(company_list)}, 个人: {len(people_list)}, '
                   f'成功: {success_count}, 失败: {failed_count}, 重复: {duplicate_count}')

    def save_processed_data(self, trademark_list: List[PreprocessTrademarkMain],
                           owner_list: List[PreprocessTrademarkOwner],
                           class_list: List[PreprocessTrademarkClass],
                           company_list: List[PreprocessCompanyMain],
                           people_list: List[PreprocessPeopleMain]):
        """
        批量保存处理后的数据到数据库
        """
        logger.info("开始保存商标数据到数据库...")
        save_start_time = time.time()

        # 保存商标基础信息
        if trademark_list:
            trademark_data_list = [asdict(trademark) for trademark in trademark_list]
            success = self.table_trademark.batch_insert_trademarks(trademark_data_list)
            logger.info(f"保存商标基础信息: {len(trademark_data_list)} 条, {'成功' if success else '失败'}")

        # 保存商标所属关系
        if owner_list:
            owner_data_list = [asdict(owner) for owner in owner_list]
            success = self.table_trademark_owner.batch_insert_owners(owner_data_list)
            logger.info(f"保存商标所属关系: {len(owner_data_list)} 条, {'成功' if success else '失败'}")

        # 保存商标分类信息
        if class_list:
            class_data_list = [asdict(class_obj) for class_obj in class_list]
            success = self.table_trademark_class.batch_insert_classes(class_data_list)
            logger.info(f"保存商标分类信息: {len(class_data_list)} 条, {'成功' if success else '失败'}")

        # 保存公司信息（如果有公司清洗模块）
        if company_list:
            logger.info(f"需要保存公司信息: {len(company_list)} 条")
            # 这里可以调用公司清洗模块的保存方法
            # self.save_company_data(company_list)

        # 保存个人信息（如果有个人清洗模块）
        if people_list:
            logger.info(f"需要保存个人信息: {len(people_list)} 条")
            # 这里可以调用个人清洗模块的保存方法
            # self.save_people_data(people_list)

        save_end_time = time.time()
        logger.info(f"商标数据保存完成，耗时: {save_end_time - save_start_time:.2f}s")

    def test_unit(self):
        """
        单元测试方法
        """
        logger.info("开始商标数据清洗单元测试")

        # 使用测试数据
        test_data = [
            {
                "id": "12345",
                "brand_name": "APPLE",
                "category": "Technology",
                "status": "Active",
                "score": 95.5,
                "application_date": "01.01.2020",
                "registration_date": "01.06.2020"
            }
        ]

        # 调用主处理方法（会自动保存到数据库）
        self.deal_datalist(test_data)

        logger.info("商标单元测试完成")
