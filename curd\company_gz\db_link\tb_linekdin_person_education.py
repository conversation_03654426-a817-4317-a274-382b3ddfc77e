#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_linekdin_person_education.py
@Date: 2024/5/9
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence, Dict
from public.public_funcs import public_funcs_obj


class TableLinkPersonEducation(TableBase):
    db = 'db_link'
    table = 'linekdin_person_education'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_person_education(self, hids: list = None, batch_size: int = None) -> Dict[str, List]:
        """
        获取人物教育经历信息
        :param hids: 人物ID列表
        :param batch_size: 防止 hids过大，分批次查询
        :return: {人物ID: []}
        """
        if not hids:
            return {}
        if not batch_size:
            batch_size = len(hids)
        person_education_data: dict = {}
        for i in range(0, len(hids), batch_size):
            hids_batch = hids[i:i + batch_size]  # 切片获取当前批次
            bfhs_str = ','.join(['%s'] * len(hids_batch))
            sql = f'''
                select lid,sid,full_name,school_name,school_type,
                country_iso_code,location_country,location_region,location_locality,
                linkedin_url,facebook_url,twitter_url,website,
                start_date,end_date,degrees,majors,minors,gpa,
                summary
                from {self.db}.{self.table} where lid in ({bfhs_str})
            '''
            data_src = self.db_mysql.read(sql, value=hids_batch, return_dict=True)
            results: Sequence[dict] = data_src.data
            for result in results:
                hid_source = result.get('lid', '')
                sid_source = result.get('sid', '')
                full_name = result.get('full_name', '')
                school_name = result.get('school_name', '')
                school_type = result.get('school_type', '')
                school_country_code = result.get('country_iso_code', '')
                school_country = result.get('location_country', '')
                school_province = result.get('location_region', '')
                school_city = result.get('location_locality', '')
                linkedin_url = result.get('linkedin_url', '').strip()
                facebook_url = result.get('facebook_url', '').strip()
                twitter_url = result.get('twitter_url', '').strip()
                website = result.get('website', '')
                start_date = result.get('start_date', '')
                end_date = result.get('end_date', '')
                degrees = result.get('degrees', '')
                majors = result.get('majors', '')
                minors = result.get('minors', '')
                gpa = result.get('gpa', '')
                summary = result.get('summary', '')
                if not hid_source or not school_name:
                    continue
                human_name = full_name.lower().strip()
                human_name_sp = human_name.split(' ')
                if human_name_sp[0] in ['mr', 'mrs', 'mr.', 'mrs.']:
                    human_name_sp = human_name_sp[1:]
                if human_name_sp and human_name_sp[-1] in ['n/a']:
                    human_name_sp = human_name_sp[:-1]
                if not human_name_sp:
                    continue
                human_name = ' '.join(human_name_sp).strip()
                school_name = school_name.lower().strip() if school_name else ''
                school_type = school_type.lower().strip() if school_type else ''
                # start_date、end_date: 2017 | 2008-04 | 2013-11-01
                start_date_str = ''
                end_date_str = ''
                if start_date:
                    start_date_str = public_funcs_obj.normalize_date(start_date)
                if end_date:
                    end_date_str = public_funcs_obj.normalize_date(end_date)

                if facebook_url:
                    facebook_url = f'https://www.{facebook_url}'
                if linkedin_url:
                    linkedin_url = f'https://www.{linkedin_url}'
                if twitter_url:
                    twitter_url = f'https://{twitter_url}'

                data = {'hid_source': hid_source, 'sid_source': sid_source, 'human_name': human_name,
                        'school_name': school_name, 'school_type': school_type, 'school_country_code': school_country_code,
                        'school_country': school_country, 'school_province': school_province, 'school_city': school_city,
                        'start_date_str': start_date_str, 'end_date_str': end_date_str, 'website': website,
                        'linkedin_url': linkedin_url, 'facebook_url': facebook_url, 'twitter_url': twitter_url,
                        'degrees': degrees, 'majors': majors, 'minors': minors, 'gpa': gpa,
                        'summary': summary}
                if hid_source not in person_education_data:
                    person_education_data[hid_source] = []
                person_education_data[hid_source].append(data)
        return person_education_data
