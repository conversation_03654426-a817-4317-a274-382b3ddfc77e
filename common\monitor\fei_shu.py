import httpx
from settings import *


class FeiShuBot:
    def __init__(self, test=True):
        if test:
            # 个人测试群
            self.bot_api = "https://open.feishu.cn/open-apis/bot/v2/hook/1b336a7d-9eec-444e-a7b1-fd1e883d9b33"
        else:
            # 告警通知群
            self.bot_api = "https://open.feishu.cn/open-apis/bot/v2/hook/64115779-bf59-4e07-b91e-a43b331901fa"

        self.headers = {
            "Content-Type": "application/json; charset=utf-8",
        }

    def send_text(self, content):

        payload_message = {
            "msg_type": "text",
            "content": {
                # @ 单个用户 <at user_id="ou_xxx">名字</at>
                # "text": "流量监控\n"+content + "<at user_id=\"bf888888\">name</at>"
                # @ 所有人 <at user_id="all">所有人</at>
                "text": content
            }
        }
        response = httpx.post(url=self.bot_api, json=payload_message, headers=self.headers, timeout=10)
        return response.json

    def send_rich_text(self, title, msg_data_list: list, msg_type='post'):
        """
        发送飞书机器人消息
        :param title:
        :param msg_data_list:
            [[{"tag": "text", "text": "项目变更"},
            {"tag": "a", "text": "请查看", "href": "​https://www.baidu.com/"},
            {"tag": "at", "user_id": "xxxxx", "user_name": ""}]]
        :param msg_type:
        :return:
        """
        for i in range(3):
            try:
                json_data = {
                    'msg_type': msg_type,
                    'content': {
                        msg_type: {
                            "zh_cn": {
                                "title": title,
                                "content": msg_data_list
                            }
                        }
                    }
                }
                res = httpx.post(self.bot_api, json=json_data, headers=self.headers, timeout=10, verify=False)
                res_json = res.json()
                code = res_json.get('StatusCode', 0)
                msg = res_json.get('StatusMessage', '')
                return code, msg
            except Exception as e:
                logger.error({"info": "推送飞书消息异常", "e": e, "msg_data_list": msg_data_list})
        return 1, '异常'

    def test_unit(self):
        title = '测试'
        text = "当前为@测试，请忽略该告警"
        msg_data_list = [[
            {"tag": "text", "text": text},
            # {"tag": "at", "user_id": "ou_b05e6f743c92ee4face4a8bd898b76de", "user_name": "所有人"}
        ]]
        code, msg = self.send_rich_text(title, msg_data_list)
        logger.info({"code": code, "msg": msg, "title": title, "text": text})


if __name__ == '__main__':
    # send_msg_to_fei_shu('测试监控接口')
    FeiShuBot().test_unit()
