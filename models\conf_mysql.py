from dataclasses import dataclass
from typing import Union, Sequence, Optional, List
from urllib.parse import quote_plus


@dataclass
class ConfMySQL:
    """mysql的基本配置"""
    name: str  # 配置名字，通过settings.config.yml查询具体的配置
    db: Optional[str] = None  # 数据库的名字
    table: Optional[str] = None
    max_connections: int = 1

    def __post_init__(self):
        self.db = self.db or None
        self.table = self.table or None
        self.max_connections = self.max_connections or 1


@dataclass
class ConfMysqlInfo:
    """mysql的连接配置"""
    host: str = "127.0.0.1"
    port: int = 3306
    user: str = "root"
    passwd: str = "root"
    db: Optional[str] = None
    charset: str = "utf8mb4"

    def info(self):
        info = {
            "host": self.host,
            "port": self.port,
            "user": self.user,
            "passwd": self.passwd,
            "db": self.db,
            "charset": self.charset,
        }
        return info

    def get_url(self, module: str = "asyncmy"):
        """生成mysql url连接，默认异步aiomysql"""
        url = f"mysql+{module}://{self.user}:{quote_plus(self.passwd)}@{self.host}:{self.port}"
        if self.db is not None:
            url += f"/{self.db}"
        return url


@dataclass
class ConfMySQLMenu:
    customs_gz: ConfMysqlInfo
    company_gz: ConfMysqlInfo


@dataclass
class MysqlResult:
    """读取mysql返回对象"""
    sql: str
    status: bool = True  # 是否执行成功
    data: Union[Sequence[Sequence], List[dict], Sequence] = tuple()  # 查询的数据
    length: int = 0  # 查询数据的长度
    lastrowid: int = 0  # 最后的自增id
    rowcount: int = 0  # 影响的行数

    def __post_init__(self):
        self.length = len(self.data)

    def __str__(self):
        return self.__repr__()

    def __repr__(self):
        """限制默认打印的长度"""
        limit_str = 50
        limit_list = 2
        attrs = dict()
        for k, v in self.__dict__.items():
            if k == 'sql' and len(v) > limit_str:
                v = v[:limit_str] + '...'
            elif k == 'data' and self.length > limit_list:
                v = f'[{v[0]}, ..., {v[-1]}]'
            attrs[k] = v

        # attrs_str = ', '.join(f"{k}={v!r}" for k, v in attrs.items())
        attrs_str = ', '.join(f"{k}={v}" for k, v in attrs.items())
        return f"{self.__class__.__name__}({attrs_str})"


if __name__ == '__main__':
    sql = f"select xxx from xxx"
    data = [(1, 1), (2, 2), (3, 3)]
    p = MysqlResult(sql=sql * 10, data=data)
    print(p)
