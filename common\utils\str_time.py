import time
from typing import Union, Optional
from datetime import datetime, timedelta


def timestamp2str(timestamp: Union[int, float], fmt: str = '%F %T') -> str:
    """时间戳转时间字符串"""
    if isinstance(timestamp, int) and len(f"{timestamp}") == 13:
        timestamp /= 1000
    return time.strftime(fmt, time.localtime(timestamp))  # %Y-%m-%d %H:%M:%S


def str2timestamp(time_str: str, ms=False, fmt: str = '%Y-%m-%d %H:%M:%S') -> int:
    """时间字符串转时间戳"""
    struct_time = time.strptime(time_str, fmt)
    f = time.mktime(struct_time)
    return int(f * 1000) if ms else int(f)


def str2datetime(time_str: str, fmt="%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """检查字符串是否符合指定的日期时间格式"""
    try:
        return datetime.strptime(time_str, fmt)
    except ValueError:
        return


def now():
    return time.strftime('%F %T', time.localtime())  # %Y-%m-%d %H:%M:%S


def today():
    return time.strftime('%F', time.localtime())  # %Y-%m-%d %H:%M:%S


def datetime2str_plus(time_obj: Optional[datetime], seconds: int = 0) -> str:
    # 将datetime对象转换为字符串
    if time_obj is None:
        return ""
    if seconds > 0:
        # 添加一秒
        time_obj = time_obj + timedelta(seconds=seconds)
    # 将结果转换回字符串
    result_str = time_obj.strftime('%Y-%m-%d %H:%M:%S')
    return result_str
