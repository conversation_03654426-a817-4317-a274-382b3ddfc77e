#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: tb_trademark_owner.py
@Date: 2025-01-28
@Desc: 商标所属信息表操作类
@Server: 
"""
from typing import List, Sequence, Dict, Any
from curd.tb_base import TableBase


class TableTrademarkOwner(TableBase):
    db = 'db_trademark_staging'
    table = 'trademark_owner'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_exist_by_owner_id(self, t_id: str, owner_id_list: List[str]) -> Dict[str, Any]:
        """
        校验商标所属关系是否存在
        :param t_id: 商标ID
        :param owner_id_list: 所属关系ID列表
        :return: 存在的所属关系ID映射
        """
        if not t_id or not owner_id_list:
            return {}
        
        owner_id_list = list(set(owner_id_list))
        placeholders = ','.join(['%s'] * len(owner_id_list))
        sql = f'''
            SELECT owner_id, id FROM {self.db}.{self.table} 
            WHERE t_id = %s AND owner_id IN ({placeholders})
        '''
        params = [t_id] + owner_id_list
        data_src = self.db_mysql.read(sql, params, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        owner_map = {}
        for result in results:
            owner_map[result['owner_id']] = result['id']
        return owner_map

    def get_owners_by_t_id(self, t_id_list: List[str]) -> Dict[str, List[dict]]:
        """
        根据商标ID获取所属关系信息
        :param t_id_list: 商标ID列表
        :return: 商标ID对应的所属关系列表
        """
        if not t_id_list:
            return {}
        
        t_id_list = list(set(t_id_list))
        placeholders = ','.join(['%s'] * len(t_id_list))
        sql = f'''
            SELECT t_id, owner_id, owner_type, owner_name, country 
            FROM {self.db}.{self.table} 
            WHERE t_id IN ({placeholders})
            ORDER BY t_id, is_primary DESC
        '''
        data_src = self.db_mysql.read(sql, t_id_list, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        owner_map = {}
        for result in results:
            t_id = result['t_id']
            if t_id not in owner_map:
                owner_map[t_id] = []
            owner_map[t_id].append(result)
        return owner_map

    def get_owners_by_entity_type(self, entity_type: str, limit: int = 1000) -> List[dict]:
        """
        根据实体类型获取所属关系信息
        :param entity_type: 实体类型 (company/human)
        :param limit: 限制数量
        :return: 所属关系列表
        """
        if not entity_type:
            return []
        
        sql = f'''
            SELECT t_id, owner_id, owner_name, country, owner_type
            FROM {self.db}.{self.table} 
            WHERE owner_type = %s
            ORDER BY id DESC
            LIMIT %s
        '''
        data_src = self.db_mysql.read(sql, [entity_type, limit], return_dict=True)
        results: Sequence[dict] = data_src.data
        return list(results)

    def batch_insert_owners(self, owner_data_list: List[dict]) -> bool:
        """
        批量插入商标所属关系数据
        :param owner_data_list: 所属关系数据列表
        :return: 是否成功
        """
        if not owner_data_list:
            return True
        
        try:
            result = self.db_mysql.save(
                table=f"{self.db}.{self.table}",
                items=owner_data_list,
                ignore=True
            )
            return result is not None
        except Exception as e:
            print(f"批量插入商标所属关系数据失败: {e}")
            return False

    def update_owner_by_owner_id(self, t_id: str, owner_id: str, update_data: dict) -> bool:
        """
        根据所属关系ID更新信息
        :param t_id: 商标ID
        :param owner_id: 所属关系ID
        :param update_data: 更新数据
        :return: 是否成功
        """
        if not t_id or not owner_id or not update_data:
            return False
        
        try:
            set_clause = ', '.join([f"{k} = %s" for k in update_data.keys()])
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET {set_clause} 
                WHERE t_id = %s AND owner_id = %s
            '''
            params = list(update_data.values()) + [t_id, owner_id]
            result = self.db_mysql.execute(sql, params)
            return result.affected_rows > 0
        except Exception as e:
            print(f"更新商标所属关系信息失败: {e}")
            return False
