#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: trademark.py
@Date: 2025-01-28
@Desc: 商标数据预处理模型
@Server: 
"""
from dataclasses import dataclass
from typing import List, Optional
from common.utils import clean_key_fields, clean_key_fields_hashdata
from .public import check_attributes


@dataclass
class PreprocessTrademarkMain:
    """
    预处理方法 商标基础信息 模型
    """
    source_type: int  # 数据源类型, 1:wipo, 2:swissreg
    source_id: str  # 原始数据源中的商标ID
    trademark_name: str = ''  # 商标名称/品牌名称
    logo: str = ''  # 商标标识
    category: str = ''  # 商标类别
    status: str = ''  # 商标状态
    score: float = 0.0  # 评分
    trademark_type: str = ''  # 商标类型（文字/图形/组合等）
    office_status: str = ''  # 官方状态
    gbd_status: str = ''  # 官方公报状态
    filing_country: str = ''  # 备案国家二字码
    application_number: str = ''  # 申请号
    application_date: str = ''  # 申请日期
    registration_number: str = ''  # 注册号
    registration_date: str = ''  # 注册日期
    publication_date: str = ''  # 公布日期
    expiry_date: str = ''  # 到期日期
    status_date: str = ''  # 状态更新日期
    application_language: str = ''  # 申请语言代码
    introduction: str = ''  # 商标描述
    t_id: str = ''  # 统一商标ID（全局唯一）MD5(trademark_name+filing_country)

    def __post_init__(self):
        # 清洗关键字段
        self.source_id = clean_key_fields(self.source_id)
        self.trademark_name = clean_key_fields(self.trademark_name)
        self.application_number = clean_key_fields(self.application_number)
        self.registration_number = clean_key_fields(self.registration_number)
        self.filing_country = clean_key_fields(self.filing_country)
        
        # 生成统一商标ID
        if self.trademark_name and self.filing_country:
            self.t_id = clean_key_fields_hashdata(f"{self.trademark_name}+{self.filing_country}")
        elif self.application_number and self.filing_country:
            self.t_id = clean_key_fields_hashdata(f"{self.application_number}+{self.filing_country}")

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessTrademarkOwner:
    """
    预处理方法 商标所属关系 模型
    """
    t_id: str  # 统一商标ID（全局唯一）
    owner_id: str  # 所属关系ID（全局唯一）
    owner_type: str = ''  # 商标所属实体类型: company or human
    owner_name: str = ''  # 姓名/公司名称
    relationship_type: str = ''  # 关系类型
    address: str = ''  # 所属信息地址
    house_number: str = ''  # 门牌号
    zip_code: str = ''  # 邮政编码
    country: str = ''  # 国家二字码
    province: str = ''  # 省份二字码
    city: str = ''  # 城市二字码
    title: str = ''  # 职称
    email: str = ''  # 电子邮箱
    phone: str = ''  # 电话号码
    fax: str = ''  # 传真号码
    website: str = ''  # 网站地址
    language: str = ''  # 语言
    modification: str = ''  # 修改消息
    is_primary: int = 0  # 是否为主要联系人
    effective_date: str = ''  # 生效日期
    expiry_date: str = ''  # 失效日期

    def __post_init__(self):
        # 清洗关键字段
        self.t_id = clean_key_fields(self.t_id)
        self.owner_id = clean_key_fields(self.owner_id)
        self.owner_name = clean_key_fields(self.owner_name)
        self.country = clean_key_fields(self.country)
        
        # 根据kind字段确定owner_type
        # 如果没有明确的owner_type，可以根据其他字段推断

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessTrademarkClass:
    """
    预处理方法 商标分类 模型
    """
    t_id: str  # 统一商标ID（全局唯一）
    class_id: str  # 分类关系ID
    class_system: str = ''  # 分类体系类型
    class_kind: str = ''  # 分类种类
    class_version: str = ''  # 分类版本
    class_code: str = ''  # 分类代码/号码
    class_number: str = ''  # 分类号（冗余字段，便于查询）
    description: str = ''  # 描述
    parent_class_code: str = ''  # 父级分类代码
    level_depth: int = 1  # 分类层级深度
    status: str = ''  # 分类状态
    is_active: int = 1  # 是否有效

    def __post_init__(self):
        # 清洗关键字段
        self.t_id = clean_key_fields(self.t_id)
        self.class_id = clean_key_fields(self.class_id)
        self.class_code = clean_key_fields(self.class_code)
        self.class_number = clean_key_fields(self.class_number)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass
