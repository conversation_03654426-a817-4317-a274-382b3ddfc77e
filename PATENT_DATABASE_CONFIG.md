# 专利数据库连接配置说明

## 概述
本文档说明如何为专利数据清洗功能配置数据库连接。专利数据清洗需要连接到专利数据库（`patent_gz`），您需要在现有的数据库配置中添加此连接。

## 数据库连接配置

### 1. 在 settings 配置中添加专利数据库连接

请在您的数据库配置文件中添加以下连接配置：

```python
# 专利数据库连接配置
patent_gz = ConfMysqlInfo(
    host='your_patent_db_host',      # 专利数据库主机地址
    port=3306,                       # 数据库端口
    user='your_patent_db_user',      # 数据库用户名
    password='your_patent_db_password', # 数据库密码
    db='db_patent_staging',          # 专利数据库名
    charset='utf8mb4'                # 字符集
)
```

### 2. 数据库表结构

专利数据清洗功能使用以下五个主要表：

#### 2.1 专利基础信息表 (patent)
```sql
-- 已提供的DDL文件：test_data/patent.ddl.sql
-- 表名：db_patent_staging.patent
-- 主要字段：p_id, source_type, source_id, patent_number, filing_country 等
```

#### 2.2 专利所属信息表 (patent_owner)
```sql
-- 已提供的DDL文件：test_data/patent_onwer.ddl.sql
-- 表名：db_patent_staging.patent_owner
-- 主要字段：p_id, owner_id, entity_type, owner_type, name 等
```

#### 2.3 专利分类信息表 (patent_class)
```sql
-- 已提供的DDL文件：test_data/patent_class.ddl.sql
-- 表名：db_patent_staging.patent_class
-- 主要字段：p_id, class_id, class_system, class_symbol 等
```

#### 2.4 专利去重表 (patent_dedup) **[新增]**
```sql
-- 已提供的DDL文件：test_data/patent_dedup.ddl.sql
-- 表名：db_patent_staging.patent_dedup
-- 主要字段：p_id, source_type, source_id, patent_number, process_count 等
-- 功能：防止重复处理相同的专利数据
```

#### 2.5 专利任务记录表 (patent_task_record) **[新增]**
```sql
-- 已提供的DDL文件：test_data/patent_dedup.ddl.sql
-- 表名：db_patent_staging.patent_task_record
-- 主要字段：task_date, task_name, start_id, end_id, task_status 等
-- 功能：记录每天的处理进度和任务状态
```

#### 2.6 专利任务详细记录表 (patent_task_detail) **[可选]**
```sql
-- 已提供的DDL文件：test_data/patent_dedup.ddl.sql
-- 表名：db_patent_staging.patent_task_detail
-- 主要字段：task_record_id, batch_no, batch_start_id, batch_end_id 等
-- 功能：记录每个批次的详细处理信息
```

### 3. 配置文件更新

#### 3.1 更新 reader/patent.yml
配置文件已创建，包含以下关键配置：
- 数据源连接：`spider_gz`（源数据）
- 目标连接：`patent_gz`（专利库）
- 预处理方法：`clean_patent`
- 批量大小：1000
- 并发数：8

#### 3.2 连接池配置示例
```python
# 在您的连接池配置中添加
mysql_connections = {
    'spider_gz': {
        'host': 'source_db_host',
        'port': 3306,
        'user': 'source_user', 
        'password': 'source_password',
        'db': 'db_spider_swissreg',
        'charset': 'utf8mb4'
    },
    'patent_gz': {
        'host': 'patent_db_host',
        'port': 3306,
        'user': 'patent_user',
        'password': 'patent_password', 
        'db': 'db_patent_staging',
        'charset': 'utf8mb4'
    }
}
```

## 使用方法

### 1. 启动专利数据清洗
```bash
# 使用项目的启动脚本
./up.sh patent

# 或者直接使用 datak.py
python datak.py --reader patent.yml
```

### 2. 测试数据清洗功能
```bash
# 运行简化测试脚本（不需要数据库连接）
python test_patent_simple.py

# 运行完整测试脚本（需要数据库连接）
python test_patent_clean.py
```

## 数据流程

### 1. 数据源
- 源表：`db_spider_swissreg.patent_info`（专利基础信息）
- 源表：`db_spider_swissreg.patent_registeradressen`（专利所属关系）
- 源表：`db_spider_swissreg.patent_ipc`（IPC分类）
- 源表：`db_spider_swissreg.patent_cpc`（CPC分类）

### 2. 增量处理流程 **[新增]**
1. **获取处理范围**：根据上次处理的ID确定本次处理的数据范围
2. **创建任务记录**：在`patent_task_record`表中创建每日任务记录
3. **获取主数据**：从`patent_info`表按ID范围获取专利基础信息
4. **获取关联数据**：根据`open_id`从关联表获取所属关系和分类数据
   - `patent_registeradressen`表：专利所属关系数据
   - `patent_ipc`表：IPC分类数据
   - `patent_cpc`表：CPC分类数据
5. **数据整合**：将主数据和关联数据整合为完整的专利信息
6. **执行去重检查**：在`patent_dedup`表中检查是否已处理过
7. **处理新数据**：只处理未重复的数据
8. **批量保存**：将清洗后的数据批量保存到目标表
9. **更新进度**：实时更新任务处理进度
10. **完成任务**：更新任务状态和统计信息

### 3. 数据清洗
- 专利基础信息清洗和统一ID生成
- 所属关系处理，区分公司（INHABER）和个人（ERFINDER）
- 分类信息标准化处理
- 日期格式转换（DD.MM.YYYY → YYYY-MM-DD）
- **去重处理**：基于`p_id`进行去重，避免重复处理
- **任务跟踪**：记录每天的处理进度和结果

### 4. 目标表
- `db_patent_staging.patent`（专利基础信息）
- `db_patent_staging.patent_owner`（专利所属关系）
- `db_patent_staging.patent_class`（专利分类信息）
- `db_patent_staging.patent_dedup`（去重记录）**[新增]**
- `db_patent_staging.patent_task_record`（任务记录）**[新增]**

## 实体类型映射

### 公司实体 (INHABER)
- `owner_type`: "INHABER"
- `entity_type`: "company"
- 转换为：`PreprocessCompanyMain` 模型
- 入库到：公司相关表

### 个人实体 (ERFINDER)  
- `owner_type`: "ERFINDER"
- `entity_type`: "human"
- 转换为：`PreprocessPeopleMain` 模型
- 入库到：个人相关表

## 注意事项

1. **数据库权限**：确保专利数据库用户具有 SELECT, INSERT, UPDATE 权限
2. **字符集**：建议使用 utf8mb4 字符集支持完整的 Unicode 字符
3. **连接池**：根据并发需求调整最大连接数
4. **监控**：建议监控数据清洗进度和错误日志
5. **备份**：在生产环境中运行前请备份目标数据库

## 文件清单

### 已创建的文件
- `models/preprocess_model/patent.py` - 专利预处理模型
- `models/db_patent.py` - 专利数据库模型
- `curd/patent_gz/` - 专利数据库操作类目录
  - `tb_patent.py` - 专利表操作类
  - `tb_patent_owner.py` - 专利所属表操作类
  - `tb_patent_class.py` - 专利分类表操作类
  - `tb_patent_dedup.py` - 专利去重表操作类 **[新增]**
  - `tb_patent_task_record.py` - 任务记录表操作类 **[新增]**
- `preprocess/sync_normalization/clean_patent.py` - 专利预处理方法（集成去重和任务记录）
- `reader/patent.yml` - 专利配置文件
- `common/patent_incremental_manager.py` - 增量处理管理器 **[新增]**
- `test_data/patent_dedup.ddl.sql` - 去重和任务记录表DDL **[新增]**
- `test_patent_simple.py` - 简化测试脚本
- `test_patent_dedup_task.py` - 去重和任务记录测试脚本 **[新增]**
- `test_patent_integration.py` - 数据集成处理测试脚本 **[新增]**

### 需要您配置的
- 数据库连接信息（host, user, password等）
- 根据实际环境调整配置参数
- 创建专利数据库和表结构（使用提供的DDL文件）
- **执行新增的DDL文件创建去重和任务记录表**

## 新增功能说明

### 去重机制
- **去重表**：`patent_dedup`表记录所有已处理的专利ID
- **去重逻辑**：处理前检查`p_id`是否已存在，避免重复处理
- **去重统计**：记录处理次数和时间，支持统计分析
- **性能优化**：使用批量查询和索引优化，支持大量数据去重

### 任务记录机制
- **每日任务**：每天创建独立的任务记录，记录处理范围
- **进度跟踪**：实时更新处理进度，包括成功、失败、重复数量
- **状态管理**：支持待处理、处理中、已完成、失败等状态
- **增量处理**：基于上次处理位置，自动计算下次处理范围
- **监控统计**：提供详细的处理统计和性能分析

### 使用示例

```python
# 使用增量管理器
from common.patent_incremental_manager import PatentIncrementalManager

manager = PatentIncrementalManager(mysql_manager)

# 获取下一个处理范围
range_info = manager.get_next_processing_range(
    'clean_patent',
    'db_spider_swissreg.patent_info',
    batch_size=10000
)

if range_info:
    start_id, end_id = range_info
    # 创建每日任务
    task_id = manager.create_daily_task(
        'clean_patent',
        'db_spider_swissreg.patent_info',
        start_id, end_id
    )

    # 开始处理...
    # 完成任务
    manager.complete_task(task_id, success_count, failed_count, duplicate_count)
```

## 支持

如有问题，请检查：
1. 数据库连接配置是否正确
2. 表结构是否已创建（包括新增的去重和任务记录表）
3. 数据库用户权限是否充足
4. 日志文件中的错误信息
5. **新增表的索引是否正确创建**
6. **去重表的数据是否正常**
