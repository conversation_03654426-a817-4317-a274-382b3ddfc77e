#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: clean_depth_company.py
@Date: 2023/12/28
@Desc: 
@Server: 
"""
import json
import re
import time
import arrow

from settings import *
from typing import Optional, Tuple, Union
from models.preprocess_model import *
from common.database import MysqlConnectionManager
from curd import TableDimWorldLocationCountry
from curd import TableIndustryMap
from curd import TableCompanyBaseNationalIdentifiers
from curd import TableCompanyBaseInformationV2
from curd import TableCompanyPersonDirector
from curd import TableCompanyShareholdersBase
from curd import TableCompanySubsidiaryBase
from curd import TableCompanyBaseInformation
from curd import TableBusinessEmailsAll
from curd import TableBusinessPhonesAll
from curd import TableBusinessSocialsAll
from curd import TableBusinessWebsitesAll
from public.public_clean_main import PublicCleanMain
from preprocess import PreprocessBase
from preprocess.sync_normalization.clean_funcs import preprocess_clean_funcs_obj


class CleanDepthCompany(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.mysql_obj_company_gz = mysql_manager.get_pool("company_gz")
        self.source_name = 'depth_company'
        # 注册号类型ID
        self.nat_label_ids = (
            "100115", "100131", "100263", "200003", "200007",
            "200008", "200013", "200015", "200021", "200023",
            "200025", "200026", "200027", "200030", "200035",
            "200036", "200037", "200039", "200042", "200043",
            "200049", "200051", "200056", "200058", "200064",
            "200066", "200069", "200070", "200074", "200075",
            "200076", "200077", "200078", "200085", "200093",
            "200094", "200098", "200101", "200103", "200104",
            "200106", "200107", "200108", "200112", "200113",
            "200114", "200116", "200118", "200121", "200126",
            "200130", "200133", "200136", "200142", "200144",
            "200146", "200149", "200150", "200151", "200152",
            "200153", "200154", "200158", "200159", "200162",
            "200166", "200167", "200171", "200178", "200179",
            "200180", "200182", "200190", "200191", "200193",
            "200200", "200201", "200203", "200205", "200206",
            "200208", "200210", "200211", "200212", "200215",
            "200216", "200218", "200220", "200223", "200225",
            "200226", "200230", "200236", "200239", "200240",
            "200249", "200250", "200251", "200253", "200255",
            "200259", "200262", "200267", "200272", "200281",
            "200287", "200289", "200291", "200294", "200295",
            "300441", "300443", "300469"
        )
        self.table_dim_world_location_country_obj = TableDimWorldLocationCountry(self.mysql_obj_company_gz)
        self.table_industry_map_obj = TableIndustryMap(self.mysql_obj_company_gz)
        self.table_company_base_national_identifiers_obj = TableCompanyBaseNationalIdentifiers(
            self.mysql_obj_company_gz)
        self.table_company_base_information_v2_obj = TableCompanyBaseInformationV2(self.mysql_obj_company_gz)
        self.table_company_person_director_obj = TableCompanyPersonDirector(self.mysql_obj_company_gz)
        self.table_company_shareholders_base_obj = TableCompanyShareholdersBase(self.mysql_obj_company_gz)
        self.table_company_subsidiary_base_obj = TableCompanySubsidiaryBase(self.mysql_obj_company_gz)
        self.table_company_base_information_obj = TableCompanyBaseInformation(self.mysql_obj_company_gz)
        self.table_business_emails_all_obj = TableBusinessEmailsAll(self.mysql_obj_company_gz)
        self.table_business_phones_all_obj = TableBusinessPhonesAll(self.mysql_obj_company_gz)
        self.table_business_socials_all_obj = TableBusinessSocialsAll(self.mysql_obj_company_gz)
        self.table_business_websites_all_obj = TableBusinessWebsitesAll(self.mysql_obj_company_gz)
        # 国家二字码与国家名称映射
        self.country_code_map = self.table_dim_world_location_country_obj.get_country_code_map()
        # 行业代码与行业映射
        self.industry_map = self.table_industry_map_obj.get_industry_map()
        self.public_clean_main_obj = PublicCleanMain(mysql_manager)

        self.regex_time = re.compile(r'^\d{4}-\d{2}-\d{2}$')

    def get_register_number(self, cids: list, nat_label_ids: tuple = None) -> list:
        """
        获取公司注册号
        :param cids: 公司ID列表
        :param nat_label_ids: ID类型列表
        :return:
        """
        if not cids:
            return []
        cid_code_datalist = self.table_company_base_national_identifiers_obj.get_register_number(cids, nat_label_ids)
        return cid_code_datalist

    def get_overview_business_scope(self, cids: list) -> dict:
        """
        获取 公司 简介与经营范围
        :param cids:
        :return:
        """
        if not cids:
            return {}
        cid_obs_map = self.table_company_base_information_v2_obj.get_overview_business_scope(cids)
        return cid_obs_map

    def clean_province_city(self, province: str, city: str) -> tuple[str, str]:
        """
        清洗 公司 省份与城市
        """
        if city and 'province' in city.lower() and not province:
            province = city.lower().replace('province', '').strip()
            city = ''
        return province, city

    # def merge_preprocess_company_main_obj(self, item_data: dict, **kwargs) -> Optional[PreprocessCompanyMain]:
    #     """
    #     合并 预处理公司 模型
    #     :param item_data: PreprocessCompanyMain 模块 映射好的字段
    #     :return:
    #     """
    #     try:
    #         preprocess_company_main_obj = PreprocessCompanyMain(
    #             **{f.name: item_data.get(f.name, None) for f in fields(PreprocessCompanyMain)})
    #     except TypeError:
    #         preprocess_company_main_obj = None
    #         logging.warning(f'缺少实例化必要参数')
    #     except Exception as e:
    #         preprocess_company_main_obj = None
    #         logging.warning(f'其他异常')
    #     return preprocess_company_main_obj

    def format_human_experience(self, person_director_datalist: list, company_info_data_map: dict) -> dict:
        """
        格式化 人物任职经历 数据
        :param person_director_datalist: 单人物的所有任职经历数据
        :param company_info_data_map: 人物任职经历数据中的公司照面信息
        :return:
        """
        _human_address_obj_list = []
        _human_experience_obj_list = []
        if not company_info_data_map:
            return {}
        for cpd_data in person_director_datalist:
            cpd_cid_source = cpd_data.get('cid_source', '')
            cpd_human_name = cpd_data.get('human_name', '')
            cpd_human_type = cpd_data.get('human_type', 0)
            cpd_bus_id_old = cpd_data.get('hid_source', '')
            cpd_gender = cpd_data.get('gender', '')
            cpd_title_name = cpd_data.get('title_name', '')
            cpd_start_date_str = cpd_data.get('start_date_str', '')
            cpd_end_date_str = cpd_data.get('end_date_str', '')
            cpd_address = cpd_data.get('address', '')
            cpd_department = cpd_data.get('department', '')
            cpd_title_levels = cpd_data.get('title_levels', '')
            cpd_post_status = cpd_data.get('post_status', '')
            if not cpd_bus_id_old or not cpd_human_name or cpd_human_type != 1 or not cpd_cid_source:
                continue
            company_info_data = company_info_data_map.get(cpd_cid_source, {})
            if not company_info_data:
                continue
            cpd_title_levels = json.dumps(cpd_title_levels, ensure_ascii=False) if cpd_title_levels else ''
            ci_pid = company_info_data.get('pid', '')
            ci_company_name = company_info_data.get('company_name', '')
            ci_country_iso_code = company_info_data.get('country_iso_code', '')
            ci_country = self.country_code_map.get(ci_country_iso_code.lower(), {}).get('country_en', '')
            ci_province = company_info_data.get('us_state', '')
            ci_city = company_info_data.get('city', '')
            ci_province, ci_city = self.clean_province_city(ci_province, ci_city)
            if cpd_start_date_str and self.regex_time.match(cpd_start_date_str):
                # start_date = int(time.mktime(time.strptime(cpd_start_date_str, '%Y-%m-%d')))
                start_date = int(arrow.get(cpd_start_date_str, 'YYYY-MM-DD').timestamp())
            else:
                start_date = 0
            if cpd_end_date_str and self.regex_time.match(cpd_end_date_str):
                # end_date = int(time.mktime(time.strptime(cpd_end_date_str, '%Y-%m-%d')))
                end_date = int(arrow.get(cpd_end_date_str, 'YYYY-MM-DD').timestamp())
            else:
                end_date = 0
            if cpd_address:
                human_address_obj = PreprocessAddress(
                    address=cpd_address,
                    address_type_id=7
                )
                _human_address_obj_list.append(human_address_obj)
            human_experience_obj = PreprocessExperience(
                company_name=ci_company_name,
                country=ci_country,
                country_code=ci_country_iso_code,
                province=ci_province,
                city=ci_city,
                bus_id_old=ci_pid,
                title_name=cpd_title_name,
                title_levels=cpd_title_levels,
                department=cpd_department,
                start_date=start_date,
                end_date=end_date,
                post_status=cpd_post_status)
            _human_experience_obj_list.append(human_experience_obj)
        return {'human_address_obj_list': _human_address_obj_list,
                'human_experience_obj_list': _human_experience_obj_list}

    def deal_data(
            self,
            data: dict,
            cid_code_map: dict,
            cid_obs_map: dict,
            **kwargs
    ) -> Tuple[Union[PreprocessCompanyMain, None], Union[List[PreprocessPeopleMain], None]]:
        """
        处理单条数据
        :param data:
        :param cid_code_map: 公司ID 与 注册号 的映射关系
        :param cid_obs_map: 公司ID 与 简介经营范围 的映射关系
        :return: 公司 公共清洗方法 模型
        """
        company_name = data.get('company_name', '')
        if not company_name:
            return None, None
        bus_id_old = data.get('pid', '')
        company_local_name = data.get('company_local_name', '')
        revenue_usd = data.get('revenue_usd', 0)
        revenue_usd = revenue_usd if revenue_usd else 0
        employee = data.get('employee', 0)
        company_logo_url = data.get('logo', '') or ''
        country_iso_code = data.get('country_iso_code', '')
        province = data.get('us_state', '')
        city = data.get('city', '')
        incorp_date = data.get('incorp_date', 0)
        latitude = data.get('latitude', '')
        longitude = data.get('longitude', '')
        opgname = data.get('opgname', '')
        address = data.get('address', '')
        postcode = data.get('postcode', '')
        ussic_core_code = data.get('ussic_core_code', '')
        product = data.get('product', '')
        stock_code = data.get('ticker', '')
        company_status = data.get('status', '')
        company_type = data.get('entity_type', '')
        country_en = data.get('country_en', '')
        province, city = self.clean_province_city(province, city)
        company_code = cid_code_map.get(bus_id_old, '')
        country = self.country_code_map.get(country_iso_code.lower(), {}).get('country_en',
                                                                              '') if not country_en else country_en
        business_scope = cid_obs_map.get(bus_id_old, {}).get('business_scope', '')
        overview = cid_obs_map.get(bus_id_old, {}).get('overview', '')
        industry_class = 'USIC'
        industry = self.industry_map.get(industry_class, {}).get(ussic_core_code, {}).get('industry_en', '')
        industry_id = self.industry_map.get(industry_class, {}).get(ussic_core_code, {}).get('industry_id', 0)
        product_list = product.split(',') if product else []
        company_logo_obj_list = []
        company_address_obj_list = []
        company_industry_obj_list = []
        company_names_obj_list = []
        company_products_obj_list = []
        company_stock_obj_list = []
        company_shareholders_obj_list = []
        company_subsidiary_obj_list = []
        preprocess_people_main_obj_list = []
        if company_local_name:
            company_names_obj = PreprocessCompanyNames(
                company_name=company_local_name,
                company_name_type_id=7
            )
            company_names_obj.check_attrs(['*'])
            company_names_obj_list = [company_names_obj]
        if company_logo_url:
            company_logo_obj = PreprocessLogo(
                logo_url=company_logo_url,
            )
            company_logo_obj_list.append(company_logo_obj)
        if address:
            company_address_obj = PreprocessAddress(
                address=address,
                postal_code=postcode
            )
            company_address_obj.check_attrs(['*'])
            company_address_obj_list = [company_address_obj]
        if industry:
            company_industry_obj = PreprocessCompanyIndustry(
                industry=industry,
                industry_id=industry_id,
                industry_class=industry_class,
                industry_code=ussic_core_code
            )
            company_industry_obj.check_attrs(['*'])
            company_industry_obj_list = [company_industry_obj]
        for product_name in product_list:
            product_name = product_name.strip().lower()
            if not product_name or len(product_name) <= 1:
                continue
            company_products_obj = PreprocessCompanyProducts(product_name=product_name)
            company_products_obj.check_attrs(['*'])
            company_products_obj_list.append(company_products_obj)
        if stock_code:
            company_stock_obj = PreprocessCompanyStock(stock_code=stock_code)
            company_stock_obj.check_attrs(['*'])
            company_stock_obj_list = [company_stock_obj]
        bus_emails_data_gs = kwargs.get('bus_emails_data_gs', {})
        bus_phones_data_gs = kwargs.get('bus_phones_data_gs', {})
        bus_socials_data_gs = kwargs.get('bus_socials_data_gs', {})
        bus_websites_data_gs = kwargs.get('bus_websites_data_gs', {})
        company_shareholders_data = kwargs.get('company_shareholders_data', {})
        company_subsidiary_data = kwargs.get('company_subsidiary_data', {})
        person_director_data = kwargs.get('person_director_data', {})
        cid_code_map_data = kwargs.get('cid_code_map_data', {})

        be_datalist = bus_emails_data_gs.get(bus_id_old, [])
        company_email_obj_list = preprocess_clean_funcs_obj.create_preprocess_email(be_datalist)
        bp_datalist = bus_phones_data_gs.get(bus_id_old, [])
        company_phone_obj_list = preprocess_clean_funcs_obj.create_preprocess_phone(bp_datalist)
        bs_datalist = bus_socials_data_gs.get(bus_id_old, [])
        company_social_obj_list = preprocess_clean_funcs_obj.create_preprocess_social(bs_datalist)
        bw_datalist = bus_websites_data_gs.get(bus_id_old, [])
        company_website_obj_list = preprocess_clean_funcs_obj.create_preprocess_website(bw_datalist)

        cs_datalist = company_shareholders_data.get(bus_id_old, [])
        cs_cids = set()  # 股东为公司的队列
        cs_hids = set()  # 股东为人物的队列
        for cs_data in cs_datalist:
            cs_bus_id_old = cs_data.get('bus_id_old', '')  # 股东ID
            shareholder_type = cs_data.get('shareholder_type', 0)
            if not cs_bus_id_old:
                continue
            if shareholder_type == 1:
                # 人物
                # 判断该人物是否在任职信息中
                if cs_bus_id_old not in person_director_data['human']:
                    cs_hids.add(cs_bus_id_old)
            elif shareholder_type == 2:
                # 公司
                cs_cids.add(cs_bus_id_old)
        # 获取 股东 照面信息
        cs_cids = list(cs_cids)
        cs_hids = list(cs_hids)
        if cs_hids:
            cs_hid_data = self.table_company_person_director_obj.get_person_director(
                ucis=cs_hids
            )
        else:
            cs_hid_data = {}
        if cs_cids:
            cs_cid_data_map = self.table_company_base_information_obj.get_company_information_old(
                cids=cs_cids,
                cols=['pid', 'company_name', 'country_iso_code', 'us_state', 'city']
            )
        else:
            cs_cid_data_map = {}
        # 关联 股东照面信息 与 补充 任职信息
        for cs_data in cs_datalist:
            cs_bus_id_old = cs_data.get('bus_id_old', '')  # 股东ID
            shareholder_type = cs_data.get('shareholder_type', 0)
            shareholder_direct = cs_data.get('shareholder_direct', '')
            shareholder_total = cs_data.get('shareholder_total', '')
            if not cs_bus_id_old:
                continue
            if shareholder_type == 1:
                # 人物
                # 判断该人物是否在任职信息中
                if cs_bus_id_old not in person_director_data['human']:
                    if cs_bus_id_old in cs_hid_data['human']:
                        person_director_data['human'][cs_bus_id_old] = cs_hid_data['human'][cs_bus_id_old]
                    else:
                        continue
                shareholder_name = person_director_data['human'][cs_bus_id_old][0]['human_name']
                company_shareholders_obj = PreprocessCompanyShareholders(
                    shareholder_name=shareholder_name,
                    country='',
                    province='',
                    country_code='',
                    bus_id_old=cs_bus_id_old,
                    shareholder_type=shareholder_type,
                    shareholder_direct=shareholder_direct,
                    shareholder_total=shareholder_total
                )
                company_shareholders_obj.check_attrs(['*'])
                # 人物类型也加入人物队列
                company_people_obj = PreprocessPeopleMain(
                    source_name=self.source_name,
                    company_bus_id_old=bus_id_old,
                    human_name=shareholder_name,
                    human_type=shareholder_type,
                    bus_id_old=cs_bus_id_old
                )
                preprocess_people_main_obj_list.append(company_people_obj)
            else:
                # 公司
                cs_cid_data = cs_cid_data_map.get(cs_bus_id_old, {})
                if not cs_cid_data:
                    continue
                shareholder_name = cs_cid_data.get('company_name', '')
                cs_country_iso_code = cs_cid_data.get('country_iso_code', '')
                cs_province = cs_cid_data.get('us_state', '')
                company_shareholders_obj = PreprocessCompanyShareholders(
                    shareholder_name=shareholder_name,
                    country='',
                    province=cs_province,
                    country_code=cs_country_iso_code,
                    bus_id_old=cs_bus_id_old,
                    shareholder_type=shareholder_type,
                    shareholder_direct=shareholder_direct,
                    shareholder_total=shareholder_total
                )
                company_shareholders_obj.check_attrs(['*'])
            company_shareholders_obj_list.append(company_shareholders_obj)
        csu_datalist = company_subsidiary_data.get(bus_id_old, [])
        for csu in csu_datalist:
            subsidiary_name = csu.get('subsidiary_name', '')
            csu_country_iso_code = csu.get('country_iso_code', '')
            csu_province = csu.get('province', '')
            csu_city = csu.get('city', '')
            csu_bus_id_old = csu.get('bus_id_old', '')
            shareholder_direct = csu.get('shareholder_direct', '')
            shareholder_total = csu.get('shareholder_total', '')
            if not subsidiary_name:
                continue
            csu_province, csu_city = self.clean_province_city(csu_province, csu_city)
            company_subsidiary_obj = PreprocessCompanySubsidiary(
                subsidiary_name=subsidiary_name,
                country='',
                province=csu_province,
                country_code=csu_country_iso_code,
                bus_id_old=csu_bus_id_old,
                shareholder_direct=shareholder_direct,
                shareholder_total=shareholder_total
            )
            company_subsidiary_obj.check_attrs(['*'])
            company_subsidiary_obj_list.append(company_subsidiary_obj)
        cpd_datalist = person_director_data['company'].get(bus_id_old, [])
        cpd_hids = set()  # 该公司下所有员工列表
        for cpd_data in cpd_datalist:
            cpd_human_name = cpd_data.get('human_name', '')
            cpd_human_type = cpd_data.get('human_type', 0)
            cpd_bus_id_old = cpd_data.get('hid_source', '')
            cpd_gender = cpd_data.get('gender', '')
            if not cpd_bus_id_old or not cpd_human_name or cpd_human_type != 1:
                continue
            cpd_hids.add(cpd_bus_id_old)
        if cpd_hids:
            # 获取该公司下所有员工的所有任职信息
            cpd_person_director_data = self.table_company_person_director_obj.get_person_director(
                ucis=list(cpd_hids)
            )
            cpd_cids = list(cpd_person_director_data['company'].keys())
            if cpd_cids:
                # 获取所有涉及公司的照面信息
                cpd_company_info_data_map = self.table_company_base_information_obj.get_company_information_old(
                    cids=cpd_cids,
                    cols=['pid', 'company_name', 'country_iso_code', 'us_state', 'city']
                )
                for uci, _datalist in cpd_person_director_data['human'].items():
                    _human_name = _datalist[0].get('human_name', '')
                    _human_type = _datalist[0].get('human_type', 0)
                    _bus_id_old = _datalist[0].get('hid_source', '')
                    _gender = _datalist[0].get('gender', '')
                    if not _bus_id_old or not _human_name or _human_type != 1:
                        continue
                    _he_data = self.format_human_experience(
                        person_director_datalist=_datalist,
                        company_info_data_map=cpd_company_info_data_map
                    )
                    if not _he_data:
                        continue
                    human_address_obj_list = _he_data.get('human_address_obj_list', [])
                    human_experience_obj_list = _he_data.get('human_experience_obj_list', [])
                    company_people_obj = PreprocessPeopleMain(
                        source_name=self.source_name,
                        company_bus_id_old=bus_id_old,
                        human_name=_human_name,
                        human_type=_human_type,
                        bus_id_old=_bus_id_old,
                        gender=_gender,
                        human_address_obj_list=human_address_obj_list,
                        human_experience_obj_list=human_experience_obj_list
                    )
                    preprocess_people_main_obj_list.append(company_people_obj)
        # 公司各注册码数据
        ccm_datalist = cid_code_map_data.get(bus_id_old, [])
        company_national_identifiers_obj_list = []
        for ccm_data in ccm_datalist:
            nat_id = ccm_data.get('nat_id', '')
            nat_label_id = ccm_data.get('nat_label_id', '')
            if not nat_id:
                continue
            company_national_identifiers_obj = PreprocessCompanyNationalIdentifiers(
                nat_id=nat_id,
                nat_label_id=nat_label_id,
            )
            company_national_identifiers_obj_list.append(company_national_identifiers_obj)

        # 联系方式
        preprocess_concat_main_obj = PreprocessConcatMain(
            email_obj_list=company_email_obj_list,
            phone_obj_list=company_phone_obj_list,
            social_obj_list=company_social_obj_list,
            website_obj_list=company_website_obj_list,
        )
        preprocess_company_main_obj: PreprocessCompanyMain = PreprocessCompanyMain(
            source_name=self.source_name, company_name=company_name,
            country=country, province=province,
            country_code=country_iso_code, city=city,
            bus_id_old=bus_id_old, company_status=company_status,
            company_type=company_type, company_code=company_code,
            inc_date=incorp_date, latitude=latitude,
            longitude=longitude, opgname=opgname,
            business_scope=business_scope, overview=overview,
            revenue_usd=revenue_usd, company_size=employee,
            company_logo_obj_list=company_logo_obj_list,
            company_address_obj_list=company_address_obj_list,
            company_industry_obj_list=company_industry_obj_list,
            company_names_obj_list=company_names_obj_list,
            company_products_obj_list=company_products_obj_list,
            company_stock_obj_list=company_stock_obj_list,
            company_shareholders_obj_list=company_shareholders_obj_list,
            company_subsidiary_obj_list=company_subsidiary_obj_list,
            company_national_identifiers_obj_list=company_national_identifiers_obj_list,
            concat_obj=preprocess_concat_main_obj,
        )
        return preprocess_company_main_obj, preprocess_people_main_obj_list

    def deal_datalist(self, datalist: List[dict]):
        """
        处理批量数据
        :param datalist: 
        :return: 
        """
        logger.info(f'start preprocess deal datalist ...')
        s_t = time.time()
        cids = []
        for data in datalist:
            pid = data.get('pid', '')
            cids.append(pid)
        cids = list(set(cids))
        # 获取 所有 注册号
        cid_code_datalist = self.get_register_number(cids)
        # 获取 公司ID 与 注册号 的映射关系
        cid_code_map = {}  # 指定注册类型注册号映射，用于放在照面信息页，一般是公司注册号、企业统一社会信用代码
        cid_code_map_data = {}
        for cid_code_data in cid_code_datalist:
            _pid = cid_code_data.get('pid', '')
            _nat_id = cid_code_data.get('nat_id', '')
            _nat_label_id = cid_code_data.get('nat_label_id', '')
            if not _pid or not _nat_id:
                continue
            if _nat_label_id in self.nat_label_ids:
                cid_code_map[_pid] = _nat_id
            if _pid not in cid_code_map_data:
                cid_code_map_data[_pid] = []
            cid_code_map_data[_pid].append(cid_code_data)

        # 获取 公司ID 与 简介经营范围 的映射关系
        cid_obs_map = self.get_overview_business_scope(cids)
        # 获取 所有 邮箱
        bus_emails_data_gs = self.table_business_emails_all_obj.get_bus_emails(cids, bus_type='深度公司')
        # 获取 所有 电话
        bus_phones_data_gs = self.table_business_phones_all_obj.get_bus_phones(cids, bus_type='深度公司')
        # 获取 所有 社媒
        bus_socials_data_gs = self.table_business_socials_all_obj.get_bus_socials(cids, bus_type='深度公司')
        # 获取 所有 网址
        bus_websites_data_gs = self.table_business_websites_all_obj.get_bus_websites(cids, bus_type='深度公司')
        # 获取 股东 信息
        company_shareholders_data = self.table_company_shareholders_base_obj.get_shareholders(cids)
        # 获取 子公司 信息
        company_subsidiary_data = self.table_company_subsidiary_base_obj.get_company_subsidiary(cids)
        # 获取 员工人物-任职 信息
        person_director_data = self.table_company_person_director_obj.get_person_director(cids=cids)

        preprocess_company_main_obj_list = []
        preprocess_people_main_obj_list = []
        for data in datalist:
            preprocess_company_main_obj, _preprocess_people_main_obj_list = self.deal_data(
                data, cid_code_map, cid_obs_map,
                bus_emails_data_gs=bus_emails_data_gs,
                bus_phones_data_gs=bus_phones_data_gs,
                bus_socials_data_gs=bus_socials_data_gs,
                bus_websites_data_gs=bus_websites_data_gs,
                company_shareholders_data=company_shareholders_data,
                company_subsidiary_data=company_subsidiary_data,
                person_director_data=person_director_data,
                cid_code_map_data=cid_code_map_data
            )
            if preprocess_company_main_obj:
                preprocess_company_main_obj_list.append(preprocess_company_main_obj)
            if _preprocess_people_main_obj_list:
                preprocess_people_main_obj_list += _preprocess_people_main_obj_list
        logger.info(f'end preprocess deal datalist === hs: {time.time() - s_t:.2f} s')
        # 执行公共清洗 - 公司
        public_clean_company_main_obj_list = (
            self.public_clean_main_obj.main_clean_company(preprocess_company_main_obj_list))
        # 所有新旧 ID映射
        company_bus_id_map_obj_list = []
        for public_clean_company_main_obj in public_clean_company_main_obj_list:
            _bus_id_map_obj_list = public_clean_company_main_obj.bus_id_map_obj_list
            if _bus_id_map_obj_list:
                company_bus_id_map_obj_list += _bus_id_map_obj_list
        # 执行公共清洗 - 人物
        self.public_clean_main_obj.main_clean_human(
            preprocess_people_main_obj_list=preprocess_people_main_obj_list,
            company_bus_id_map_obj_list=company_bus_id_map_obj_list
        )

    def test_unit(self):
        """
        单元测试
        :return: 
        """
        from typing import List
        sql = f'''
            select * from db_comp.company_base_information_old where id>=32691001 and id <=32692000
        '''
        sql = f'''
            select * from db_comp.company_base_information_old where pid="INF4C0B8142"
        '''
        data_src = self.mysql_obj_company_gz.read(sql, return_dict=True)
        results: List[dict] = data_src.data
        self.deal_datalist(results)


if __name__ == '__main__':
    from common.database import MysqlConnectionManager
    from models import ConfMySQL

    conf_list = [
        ConfMySQL(name='company_gz', max_connections=1),
        ConfMySQL(name='company_new_gz', max_connections=1),
    ]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    # mysql_obj_company_new_gz = mysql_manager.get_pool("company_new_gz")
    # data_src = mysql_obj_company_new_gz.read('select * from db_company_new.company limit 5')
    # print(data_src.data)
    CleanDepthCompany(mysql_manager).test_unit()
