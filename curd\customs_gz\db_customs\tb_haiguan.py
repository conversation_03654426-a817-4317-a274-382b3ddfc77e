#!/usr/bin/env python
# -*- coding: utf-8 -*-

from curd.tb_base import TableBase
from typing import List, Sequence, Dict, Any


class TableHaiGuan(TableBase):
    db = 'db_customs'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def read_mapping_unit(self):
        table_quantity = "db_customs.haiguan_mapping_quantity_unit"
        table_weight = "db_customs.haiguan_mapping_weight_unit"
        sql_quantity = f"select src, res from {table_quantity};"
        sql_weight = f"select src, res from {table_weight};"
        data_quantity = self.db_mysql.read(sql_quantity, return_dict=False).data
        data_weight = self.db_mysql.read(sql_weight, return_dict=False).data
        mapping_quantity = {k: v for k, v in data_quantity}
        mapping_weight = {k: v for k, v in data_weight}
        return mapping_quantity, mapping_weight

    def read_customs_port_map(self):
        sql = f"select port_raw, port_new from db_customs.haiguan_port where flag in (1, 2, 3)"
        data_raw = self.db_mysql.read(sql, return_dict=False).data
        if data_raw is None or len(data_raw) == 0 or data_raw[0] is None:
            raise ValueError("读取港口映射失败！")
        mapping = {port_raw.upper(): port_new.upper() for port_raw, port_new in data_raw}
        return mapping