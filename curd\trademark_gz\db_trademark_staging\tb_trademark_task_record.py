#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: tb_trademark_task_record.py
@Date: 2025-01-28
@Desc: 商标任务记录表操作类
@Server: 
"""
import json
from datetime import datetime, date
from typing import List, Sequence, Dict, Any, Optional
from curd.tb_base import TableBase


class TableTrademarkTaskRecord(TableBase):
    db = 'db_trademark_staging'
    table = 'trademark_task_record'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def create_task_record(self, task_data: dict) -> Optional[int]:
        """
        创建任务记录
        :param task_data: 任务数据
        :return: 任务记录ID
        """
        try:
            # 设置默认值
            task_data.setdefault('task_date', date.today())
            task_data.setdefault('task_status', 0)  # 待处理
            task_data.setdefault('total_count', 0)
            task_data.setdefault('processed_count', 0)
            task_data.setdefault('success_count', 0)
            task_data.setdefault('failed_count', 0)
            task_data.setdefault('duplicate_count', 0)
            task_data.setdefault('duration_seconds', 0)
            task_data.setdefault('worker_count', 1)
            task_data.setdefault('batch_size', 1000)
            
            # 如果config_info是字典，转换为JSON字符串
            if 'config_info' in task_data and isinstance(task_data['config_info'], dict):
                task_data['config_info'] = json.dumps(task_data['config_info'], ensure_ascii=False)
            
            result = self.db_mysql.save(
                table=f"{self.db}.{self.table}",
                items=task_data
            )
            
            if result and hasattr(result, 'lastrowid'):
                return result.lastrowid
            return None
            
        except Exception as e:
            print(f"创建任务记录失败: {e}")
            return None

    def update_task_record(self, task_id: int, update_data: dict) -> bool:
        """
        更新任务记录
        :param task_id: 任务ID
        :param update_data: 更新数据
        :return: 是否成功
        """
        if not task_id or not update_data:
            return False
        
        try:
            # 如果config_info是字典，转换为JSON字符串
            if 'config_info' in update_data and isinstance(update_data['config_info'], dict):
                update_data['config_info'] = json.dumps(update_data['config_info'], ensure_ascii=False)
            
            set_clause = ', '.join([f"{k} = %s" for k in update_data.keys()])
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET {set_clause} 
                WHERE id = %s
            '''
            params = list(update_data.values()) + [task_id]
            result = self.db_mysql.execute(sql, params)
            return result.affected_rows > 0
        except Exception as e:
            print(f"更新任务记录失败: {e}")
            return False

    def start_task(self, task_id: int) -> bool:
        """
        开始任务
        :param task_id: 任务ID
        :return: 是否成功
        """
        update_data = {
            'task_status': 1,  # 处理中
            'start_time': datetime.now()
        }
        return self.update_task_record(task_id, update_data)

    def complete_task(self, task_id: int, success_count: int = 0, failed_count: int = 0, 
                     duplicate_count: int = 0, error_message: str = None) -> bool:
        """
        完成任务
        :param task_id: 任务ID
        :param success_count: 成功数量
        :param failed_count: 失败数量
        :param duplicate_count: 重复数量
        :param error_message: 错误信息
        :return: 是否成功
        """
        # 获取任务开始时间计算耗时
        task_info = self.get_task_record(task_id)
        duration_seconds = 0
        if task_info and task_info.get('start_time'):
            start_time = task_info['start_time']
            if isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time)
            duration_seconds = int((datetime.now() - start_time).total_seconds())
        
        update_data = {
            'task_status': 2 if not error_message else 3,  # 已完成或失败
            'end_time': datetime.now(),
            'success_count': success_count,
            'failed_count': failed_count,
            'duplicate_count': duplicate_count,
            'processed_count': success_count + failed_count + duplicate_count,
            'duration_seconds': duration_seconds
        }
        
        if error_message:
            update_data['error_message'] = error_message
        
        return self.update_task_record(task_id, update_data)

    def get_task_record(self, task_id: int) -> Optional[dict]:
        """
        获取任务记录
        :param task_id: 任务ID
        :return: 任务记录
        """
        sql = f'''
            SELECT * FROM {self.db}.{self.table} 
            WHERE id = %s
        '''
        data_src = self.db_mysql.read(sql, [task_id], return_dict=True)
        results: Sequence[dict] = data_src.data
        
        if results:
            task_record = dict(results[0])
            # 解析JSON字段
            if task_record.get('config_info'):
                try:
                    task_record['config_info'] = json.loads(task_record['config_info'])
                except:
                    pass
            return task_record
        return None

    def get_today_task_record(self, task_name: str, source_table: str) -> Optional[dict]:
        """
        获取今天的任务记录
        :param task_name: 任务名称
        :param source_table: 源表名称
        :return: 任务记录
        """
        sql = f'''
            SELECT * FROM {self.db}.{self.table} 
            WHERE task_date = CURDATE() 
            AND task_name = %s 
            AND source_table = %s
            ORDER BY id DESC
            LIMIT 1
        '''
        data_src = self.db_mysql.read(sql, [task_name, source_table], return_dict=True)
        results: Sequence[dict] = data_src.data
        
        if results:
            task_record = dict(results[0])
            # 解析JSON字段
            if task_record.get('config_info'):
                try:
                    task_record['config_info'] = json.loads(task_record['config_info'])
                except:
                    pass
            return task_record
        return None

    def get_last_processed_id(self, task_name: str, source_table: str) -> Optional[int]:
        """
        获取最后处理的ID
        :param task_name: 任务名称
        :param source_table: 源表名称
        :return: 最后处理的ID
        """
        sql = f'''
            SELECT end_id FROM {self.db}.{self.table} 
            WHERE task_name = %s 
            AND source_table = %s 
            AND task_status = 2
            ORDER BY task_date DESC, id DESC
            LIMIT 1
        '''
        data_src = self.db_mysql.read(sql, [task_name, source_table], return_dict=True)
        results: Sequence[dict] = data_src.data
        
        return results[0]['end_id'] if results else None
