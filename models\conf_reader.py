from dataclasses import dataclass
from typing import List, Union
from enum import Enum
from .conf_mysql import ConfMySQL


@dataclass
class ConfQuery:
    """查询参数"""
    sql: str = ''
    table: str = ''
    select: str = ''
    where: Union[None, str] = None


class ConfSplitType(Enum):
    """分片类型枚举值"""
    id = 1  # 通过自增编号来切片
    time = 2  # 通过时间字段来切片


@dataclass
class ConfSplitField:
    """分割字段名"""
    id: str
    time: str


@dataclass
class ConfSplit:
    """分片类型和字段"""
    type: Union[str, id]
    field: ConfSplitField
    batch_size: int = 5000

    def __post_init__(self):
        """校验类型"""
        if isinstance(self.type, int):
            self.type = ConfSplitType(self.type)
        elif isinstance(self.type, str):
            self.type = ConfSplitType[self.type]
        if isinstance(self.type, ConfSplitType):
            self.type = self.type.name
        else:
            info = f"Invalid type: {self.type}，Use case: {'|'.join([f'{x.name}:{x.value}' for x in ConfSplitType])}"
            raise ValueError(info)


@dataclass
class ConfReader:
    """读取任务的所有参数"""
    name: str  # 任务名
    source: str  # 业务名
    conn_inner: bool
    mysql: ConfMySQL  # 读取连接
    mysql_other: List[ConfMySQL]
    query: ConfQuery
    split: ConfSplit
    preprocess: str
    process: List[str]
    worker: int = 1
