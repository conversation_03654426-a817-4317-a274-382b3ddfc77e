#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_business_phones_all.py
@Date: 2024/2/26
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableBusinessPhonesAll(TableBase):
    db = 'db_concat'
    table = 'business_phones_all'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_bus_phones(self, bus_ids: list, bus_type: str, batch_size: int = None) -> dict[str, Sequence[dict]]:
        """
        获取 各维度 电话 信息
        """
        if not bus_ids:
            return {}
        if not batch_size:
            batch_size = len(bus_ids)
        bus_phones_data: dict = {}
        for i in range(0, len(bus_ids), batch_size):
            bus_ids_batch = bus_ids[i:i + batch_size]  # 切片获取当前批次
            bfhs_str = ','.join(['%s'] * len(bus_ids_batch))
            sql = f'''
                select bus_id,bus_type,phone,phone_raw,phone_type,country_code,area_code,telephone,is_valid,is_ws,
                national_number,international_number,source_name from {self.db}.{self.table}
                where bus_id in ({bfhs_str}) and is_deleted=0
            '''
            if bus_type:
                sql += f' and bus_type="{bus_type}" '
            data_src = self.db_mysql.read(sql, bus_ids_batch, return_dict=True)
            results: Sequence[dict] = data_src.data

            for result in results:
                bus_id = result.get('bus_id', '')
                bus_type = result.get('bus_type', '')
                phone_raw = result.get('phone_raw', '')
                phone = result.get('phone', '')
                phone_type = result.get('phone_type', 0)
                dialing_code = result.get('country_code', '')
                area_code = result.get('area_code', '')
                telephone = result.get('telephone', '')
                is_valid = result.get('is_valid', 0)
                is_ws = result.get('is_ws', 0)
                national_number = result.get('national_number', '')
                international_number = result.get('international_number', '')
                source_name = result.get('source_name', '')
                country_code = ''
                data = {
                    'bus_id': bus_id,
                    'bus_type': bus_type,
                    'phone_raw': phone_raw,
                    'phone': phone,
                    'phone_type': phone_type,
                    'country_code': country_code,
                    'dialing_code': dialing_code,
                    'area_code': area_code,
                    'telephone': telephone,
                    'is_valid': is_valid,
                    'is_ws': is_ws,
                    'national_number': national_number,
                    'international_number': international_number,
                    'source_name': source_name,
                }
                if bus_id not in bus_phones_data:
                    bus_phones_data[bus_id] = []
                bus_phones_data[bus_id].append(data)
        return bus_phones_data


