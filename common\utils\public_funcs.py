#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public_funcs.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
import re
import hashlib
import requests
import logging
import traceback


def clean_key_fields(key_col_val: str):
    """
    关键字段(参与去重字段)值清理
    :param key_col_val: 关键字段值
    :return:
    """
    if not key_col_val:
        return ''
    key_col_val = str(key_col_val)
    # 全角括号转半角
    key_col_val = key_col_val.replace('（', '(').replace('）', ')').strip()
    # 大写转小写
    key_col_val = key_col_val.lower()
    # 多空格转单空格
    key_col_val = re.sub(" {2,}", " ", key_col_val)
    return key_col_val


def hashdata(text: str) -> str:
    """
    统一MD5方法
    :param text:
    :return:
    """
    hash_md5 = hashlib.md5()
    hash_md5.update(text.encode('utf-8'))
    md5_data = hash_md5.hexdigest()
    return md5_data


def clean_key_fields_hashdata(key_val: str, preprocess: bool = True) -> str:
    """
    去重因子字段值清洗后再MD5
    :param key_val: 去重因子字段值
    :return:
    """
    if preprocess:
        key_val = clean_key_fields(key_val)
    md5_data = hashdata(key_val)
    return md5_data


def keep_alnum_chars(text: str) -> str:
    """
    保留字符串中的数字、字母、中文字符
    :param text:
    :return:
    """
    if not text:
        return ''
    return ''.join([c for c in text if c.isalnum()])


def get_address_region(address):
    """
    地址解析
    """
    if not address:
        return None
    url = '''http://*********:55005/parse/address'''
    data = {
        "address": address
    }
    for i in range(3):
        try:
            res_j = requests.post(url, json=data).json()
            if res_j["data"]["area_infos"]:
                return res_j["data"]["area_infos"][0]
            else:
                return {}
        except Exception as e:
            logging.error(traceback.format_exc())
            logging.error(f'''异常地址：{address}''')


# print(get_address_region("9295 W Flamingo Rd UNIT 130, Las Vegas, NV 89147"))