#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_linekdin_person_list.py
@Date: 2024/5/8
@Desc: 
@Server: 
"""
import json

from curd.tb_base import TableBase, MysqlResult
from typing import List, Sequence, Dict


class TableLinkPersonList(TableBase):
    db = 'db_link'
    table = 'linekdin_person_list'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_person_info(self, hids: list = None, cids: list = None) -> List[Dict]:
        """
        获取人物信息
        :param cids: 公司ID列表
        :return: []
        """
        if not hids and not cids:
            return []
        value = []
        sql = f'''
            select * from {self.db}.{self.table}
        '''
        w_sqls = []
        if hids:
            bfhs_str_h = ','.join(['%s'] * len(hids))
            value += hids
            w_sqls.append(f'lid in ({bfhs_str_h})')
        if cids:
            bfhs_str_c = ','.join(['%s'] * len(cids))
            value += cids
            w_sqls.append(f'job_company_id in ({bfhs_str_c})')
        w_sqls_str = ' and '.join(w_sqls)
        sql += f' where {w_sqls_str}'
        data_src = self.db_mysql.read(sql, value=value, return_dict=True)
        results: List[dict] = data_src.data
        return results
