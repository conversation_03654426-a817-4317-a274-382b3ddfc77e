#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: clean_patent.py
@Date: 2025-01-28
@Desc: 清洗专利数据
@Server: 
"""
import os
import sys
import time
import json
from typing import List, Dict, Tuple, Union, Optional
from dataclasses import asdict

# 添加项目根目录到路径
sys.path.append('../../')

from settings import *
from preprocess import PreprocessBase
from models.preprocess_model.patent import *
from models.preprocess_model.company import PreprocessCompanyMain
from models.preprocess_model.human import PreprocessPeopleMain
from curd.patent_gz.db_patent_staging import TablePatent, TablePatentOwner, TablePatentClass, TablePatentDedup, TablePatentTaskRecord
from common.database import MysqlConnectionManager
from common.utils import clean_key_fields, clean_key_fields_hashdata


class CleanPatent(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        
        # 初始化数据库连接
        self.mysql_obj_patent_gz = mysql_manager.get_pool('customs_gz')  # 修改为customs_gz
        self.mysql_obj_spider_gz = mysql_manager.get_pool('sp02')  # 源数据连接（使用sp02）

        # 初始化表操作对象
        self.table_patent = TablePatent(self.mysql_obj_patent_gz)
        self.table_patent_owner = TablePatentOwner(self.mysql_obj_patent_gz)
        self.table_patent_class = TablePatentClass(self.mysql_obj_patent_gz)
        self.table_patent_dedup = TablePatentDedup(self.mysql_obj_patent_gz)
        self.table_patent_task_record = TablePatentTaskRecord(self.mysql_obj_patent_gz)

        # 任务记录ID
        self.current_task_id = None

    def clean_patent_number(self, patent_number: str) -> str:
        """
        清洗专利号
        """
        if not patent_number:
            return ''
        
        # 移除多余空格和特殊字符
        patent_number = patent_number.strip()
        # 可以根据需要添加更多清洗规则
        return patent_number

    def clean_date_format(self, date_str: str) -> str:
        """
        清洗日期格式
        """
        if not date_str:
            return ''
        
        # 处理常见的日期格式
        date_str = date_str.strip()
        # 将 DD.MM.YYYY 格式转换为 YYYY-MM-DD
        if '.' in date_str and len(date_str) == 10:
            parts = date_str.split('.')
            if len(parts) == 3:
                day, month, year = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        return date_str

    def determine_filing_country(self, data: dict) -> str:
        """
        确定申请国家代码
        """
        # 根据专利号前缀或其他信息确定申请国家
        patent_id = data.get('patent_id', '')
        if patent_id.startswith('EP'):
            return 'EP'  # 欧洲专利
        elif patent_id.startswith('US'):
            return 'US'  # 美国专利
        elif patent_id.startswith('CN'):
            return 'CN'  # 中国专利
        
        # 默认返回瑞士
        return 'CH'

    def deal_patent_info_data(self, data: dict) -> Optional[PreprocessPatentMain]:
        """
        处理专利基础信息数据
        """
        try:
            # 提取基础字段
            source_id = str(data.get('open_id', ''))
            if not source_id:
                return None
            
            patent_number = self.clean_patent_number(data.get('patent_id', ''))
            application_number = self.clean_patent_number(data.get('application_number', ''))
            publication_date = self.clean_date_format(data.get('publication_date', ''))
            application_date = self.clean_date_format(data.get('application_date', ''))
            grant_date = self.clean_date_format(data.get('grant_date', ''))
            
            # 确定申请国家
            filing_country = self.determine_filing_country(data)
            
            # 创建预处理模型
            patent_obj = PreprocessPatentMain(
                source_type=1,  # swissreg
                source_id=source_id,
                patent_number=patent_number,
                application_number=application_number,
                publication_number=patent_number,  # 暂时使用专利号作为公开号
                ip_type=data.get('ip_type', ''),
                current_status=data.get('status', ''),
                application_date=application_date,
                publication_date=publication_date,
                grant_date=grant_date,
                next_renewal_date=self.clean_date_format(data.get('next_renewal', '')),
                protection_end_date=self.clean_date_format(data.get('protection_date_max', '')),
                filing_country=filing_country,
                it_registration=data.get('International_registration', ''),
                it_publication=data.get('international_publication', ''),
                priorities=json.dumps(data.get('priorities')) if data.get('priorities') else '',
                an_inheritance=int(data.get('an_inheritance', 0)),
                exhibition_immunity=data.get('exhibition_immunity', ''),
                paediatric_spc=data.get('paediatric_SPC', ''),
                cancellation=data.get('Cancellation', ''),
                patent_specifications=json.dumps(data.get('patent_specifications')) if data.get('patent_specifications') else ''
            )
            
            return patent_obj
            
        except Exception as e:
            logger.error(f"处理专利基础信息数据失败: {e}, data: {data}")
            return None

    def deal_patent_owner_data(self, data: dict, p_id: str) -> Optional[PreprocessPatentOwner]:
        """
        处理专利所属关系数据
        """
        try:
            owner_id = str(data.get('owner_id', ''))
            if not owner_id or not p_id:
                return None
            
            name = clean_key_fields(data.get('name', ''))
            if not name:
                return None
            
            owner_type = data.get('owner_type', '')
            
            # 创建预处理模型
            owner_obj = PreprocessPatentOwner(
                p_id=p_id,
                owner_id=owner_id,
                owner_type=owner_type,
                name=name,
                additional_name=data.get('additional_name', ''),
                title=data.get('title', ''),
                street=data.get('street', ''),
                additional_street=data.get('additional_street', ''),
                house_number=data.get('house_number', ''),
                zip_code=data.get('zip', ''),
                town=data.get('town', ''),
                country=data.get('country', ''),
                email=data.get('email', ''),
                phone=data.get('phone_number', ''),
                fax=data.get('fax_number', ''),
                language=data.get('language', ''),
                modification=data.get('modification', '')
            )
            
            return owner_obj

        except Exception as e:
            logger.error(f"处理专利所属关系数据失败: {e}, data: {data}")
            return None

    def deal_patent_class_data(self, data: dict, p_id: str) -> Optional[PreprocessPatentClass]:
        """
        处理专利分类数据
        """
        try:
            class_symbol = clean_key_fields(data.get('class_symbol', ''))
            if not class_symbol or not p_id:
                return None

            # 生成分类ID
            class_id = clean_key_fields_hashdata(f"{p_id}+{class_symbol}")

            # 创建预处理模型
            class_obj = PreprocessPatentClass(
                p_id=p_id,
                class_id=class_id,
                class_system=data.get('class_system', 'IPC'),
                class_version=data.get('class_version', ''),
                class_symbol=class_symbol,
                subclass=data.get('subclass', ''),
                main_group=data.get('main_group', ''),
                subgroup=data.get('subgroup', ''),
                class_date=self.clean_date_format(data.get('class_date', '')),
                effective_date=self.clean_date_format(data.get('effective_date', '')),
                class_code=data.get('class_code', ''),
                level_depth=int(data.get('level_depth', 1)),
                class_type=data.get('class_type', ''),
                class_status=data.get('class_status', ''),
                description_en=data.get('description_en', ''),
                is_primary=int(data.get('is_primary', 0)),
                priority_order=int(data.get('priority_order', 1))
            )

            return class_obj

        except Exception as e:
            logger.error(f"处理专利分类数据失败: {e}, data: {data}")
            return None

    def convert_to_company_human(self, owner_obj: PreprocessPatentOwner) -> Tuple[Optional[PreprocessCompanyMain], Optional[PreprocessPeopleMain]]:
        """
        将专利所属关系转换为公司或个人模型
        """
        try:
            if owner_obj.entity_type == 'company':
                # 转换为公司模型
                company_obj = PreprocessCompanyMain(
                    source_name='patent_swissreg',
                    company_name=owner_obj.name,
                    country=owner_obj.country,
                    province='',
                    city=owner_obj.town,
                    bus_id_old=owner_obj.owner_id,
                    company_type='',
                    company_status='',
                    company_code='',
                    business_scope='',
                    overview='',
                    agent_name='',
                    revenue_usd=0,
                    employee=0,
                    company_logo_url='',
                    latitude='',
                    longitude='',
                    opgname=''
                )
                return company_obj, None

            elif owner_obj.entity_type == 'human':
                # 转换为个人模型
                people_obj = PreprocessPeopleMain(
                    source_name='patent_swissreg',
                    name=owner_obj.name,
                    country=owner_obj.country,
                    province='',
                    city=owner_obj.town,
                    bus_id_old=owner_obj.owner_id,
                    gender='',
                    age=0,
                    birthday='',
                    education='',
                    occupation='Inventor',  # 发明者
                    company_name='',
                    position=owner_obj.title,
                    salary_usd=0,
                    experience_years=0,
                    skills='',
                    bio='',
                    avatar_url='',
                    latitude='',
                    longitude=''
                )
                return None, people_obj

        except Exception as e:
            logger.error(f"转换公司/个人模型失败: {e}, owner: {owner_obj}")

        return None, None

    def create_task_record(self, source_table: str, start_id: int, end_id: int,
                          total_count: int, config_info: dict = None) -> Optional[int]:
        """
        创建任务记录
        """
        task_data = {
            'task_name': 'clean_patent',
            'source_table': source_table,
            'start_id': start_id,
            'end_id': end_id,
            'total_count': total_count,
            'config_info': config_info or {},
            'worker_count': 1,
            'batch_size': 1000
        }

        task_id = self.table_patent_task_record.create_task_record(task_data)
        if task_id:
            self.current_task_id = task_id
            self.table_patent_task_record.start_task(task_id)
            logger.info(f"创建任务记录成功，任务ID: {task_id}")
        else:
            logger.error("创建任务记录失败")

        return task_id

    def complete_task_record(self, success_count: int = 0, failed_count: int = 0,
                           duplicate_count: int = 0, error_message: str = None):
        """
        完成任务记录
        """
        if self.current_task_id:
            success = self.table_patent_task_record.complete_task(
                self.current_task_id, success_count, failed_count, duplicate_count, error_message
            )
            if success:
                logger.info(f"任务记录完成，任务ID: {self.current_task_id}, "
                           f"成功: {success_count}, 失败: {failed_count}, 重复: {duplicate_count}")
            else:
                logger.error(f"完成任务记录失败，任务ID: {self.current_task_id}")

    def filter_duplicates(self, patent_list: List[PreprocessPatentMain]) -> Tuple[List[PreprocessPatentMain], List[str]]:
        """
        过滤重复的专利数据
        :param patent_list: 专利列表
        :return: (去重后的专利列表, 重复的专利ID列表)
        """
        if not patent_list:
            return [], []

        # 提取所有专利ID
        p_id_list = [patent.p_id for patent in patent_list if patent.p_id]

        # 检查重复
        duplicate_p_ids = self.table_patent_dedup.check_duplicate_by_p_ids(p_id_list)

        # 过滤重复数据
        filtered_patents = []
        duplicate_patents = []

        for patent in patent_list:
            if patent.p_id in duplicate_p_ids:
                duplicate_patents.append(patent.p_id)
            else:
                filtered_patents.append(patent)

        # 记录去重信息
        if filtered_patents:
            dedup_records = []
            for patent in filtered_patents:
                dedup_record = {
                    'p_id': patent.p_id,
                    'source_type': patent.source_type,
                    'source_id': patent.source_id,
                    'patent_number': patent.patent_number,
                    'filing_country': patent.filing_country
                }
                dedup_records.append(dedup_record)

            # 批量插入去重记录
            self.table_patent_dedup.batch_insert_dedup_records(dedup_records)

        logger.info(f"去重完成：原始 {len(patent_list)} 条，过滤后 {len(filtered_patents)} 条，重复 {len(duplicate_patents)} 条")

        return filtered_patents, duplicate_patents

    def get_related_data_by_source_ids(self, source_ids: List[str]) -> Dict[str, Dict]:
        """
        根据source_id获取关联数据
        :param source_ids: source_id列表
        :return: 关联数据字典
        """
        if not source_ids:
            return {}

        logger.info(f"开始获取关联数据，source_id数量: {len(source_ids)}")

        # 去重source_ids
        source_ids = list(set(source_ids))
        placeholders = ','.join(['%s'] * len(source_ids))

        related_data = {}

        try:
            # 获取专利所属关系数据
            owner_sql = f'''
                SELECT open_id, owner_id, owner_type, name, additional_name, title,
                       street, additional_street, house_number, zip, town, country,
                       email, phone_number, fax_number, language, modification
                FROM db_spider.swissreg_patent_registeradressen
                WHERE open_id IN ({placeholders})
            '''
            owner_data_src = self.mysql_obj_spider_gz.read(owner_sql, source_ids, return_dict=True)
            owner_results = owner_data_src.data

            # 获取IPC分类数据
            ipc_sql = f'''
                SELECT open_id, class_symbol, class_system, class_version,
                       subclass, main_group, subgroup, class_date, effective_date,
                       class_code, level_depth, class_type, class_status,
                       description_en, is_primary, priority_order
                FROM db_spider.swissreg_patent_ipc
                WHERE open_id IN ({placeholders})
            '''
            ipc_data_src = self.mysql_obj_spider_gz.read(ipc_sql, source_ids, return_dict=True)
            ipc_results = ipc_data_src.data

            # 获取CPC分类数据
            cpc_sql = f'''
                SELECT open_id, class_symbol, class_system, class_version,
                       subclass, main_group, subgroup, class_date, effective_date,
                       class_code, level_depth, class_type, class_status,
                       description_en, is_primary, priority_order
                FROM db_spider.swissreg_patent_cpc
                WHERE open_id IN ({placeholders})
            '''
            cpc_data_src = self.mysql_obj_spider_gz.read(cpc_sql, source_ids, return_dict=True)
            cpc_results = cpc_data_src.data

            # 组织关联数据
            for source_id in source_ids:
                related_data[source_id] = {
                    'owners': [],
                    'ipc_classes': [],
                    'cpc_classes': []
                }

            # 填充所属关系数据
            for owner in owner_results:
                source_id = str(owner['open_id'])
                if source_id in related_data:
                    related_data[source_id]['owners'].append(owner)

            # 填充IPC分类数据
            for ipc in ipc_results:
                source_id = str(ipc['open_id'])
                if source_id in related_data:
                    ipc['class_system'] = 'IPC'  # 确保分类体系正确
                    related_data[source_id]['ipc_classes'].append(ipc)

            # 填充CPC分类数据
            for cpc in cpc_results:
                source_id = str(cpc['open_id'])
                if source_id in related_data:
                    cpc['class_system'] = 'CPC'  # 确保分类体系正确
                    related_data[source_id]['cpc_classes'].append(cpc)

            logger.info(f"关联数据获取完成，所属关系: {len(owner_results)}, "
                       f"IPC分类: {len(ipc_results)}, CPC分类: {len(cpc_results)}")

        except Exception as e:
            logger.error(f"获取关联数据失败: {e}")

        return related_data

    def deal_datalist(self, datalist: List[dict]):
        """
        处理批量数据（集成去重和任务记录功能）
        这是PreprocessBase要求的主要入口方法

        处理流程：
        1. 从patent_info表获取主数据（datalist）
        2. 根据source_id获取关联数据（owners, classes）
        3. 整合数据并进行清洗处理
        4. 保存到目标数据库
        """
        logger.info(f'开始处理专利数据批次，数量: {len(datalist)}')
        start_time = time.time()

        # 统计计数器
        success_count = 0
        failed_count = 0
        duplicate_count = 0

        # 初始化结果列表
        patent_list = []
        owner_list = []
        class_list = []
        company_list = []
        people_list = []

        # 提取所有source_id
        source_ids = []
        patent_info_map = {}

        for data in datalist:
            source_id = str(data.get('open_id', ''))
            if source_id:
                source_ids.append(source_id)
                patent_info_map[source_id] = data

        # 获取关联数据
        related_data = self.get_related_data_by_source_ids(source_ids)

        # 处理每条专利信息数据
        for source_id, patent_info in patent_info_map.items():
            try:
                # 处理专利基础信息
                patent_obj = self.deal_patent_info_data(patent_info)
                if patent_obj:
                    patent_list.append(patent_obj)
                    p_id = patent_obj.p_id

                    # 获取该专利的关联数据
                    patent_related = related_data.get(source_id, {})

                    # 处理专利所属关系
                    owners = patent_related.get('owners', [])
                    for owner_data in owners:
                        owner_obj = self.deal_patent_owner_data(owner_data, p_id)
                        if owner_obj:
                            owner_list.append(owner_obj)

                            # 转换为公司或个人模型
                            company_obj, people_obj = self.convert_to_company_human(owner_obj)
                            if company_obj:
                                company_list.append(company_obj)
                            if people_obj:
                                people_list.append(people_obj)

                    # 处理IPC分类
                    ipc_classes = patent_related.get('ipc_classes', [])
                    for class_data in ipc_classes:
                        class_obj = self.deal_patent_class_data(class_data, p_id)
                        if class_obj:
                            class_list.append(class_obj)

                    # 处理CPC分类
                    cpc_classes = patent_related.get('cpc_classes', [])
                    for class_data in cpc_classes:
                        class_obj = self.deal_patent_class_data(class_data, p_id)
                        if class_obj:
                            class_list.append(class_obj)

                    success_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                logger.error(f"处理专利数据失败: {e}, source_id: {source_id}")
                failed_count += 1
                continue

        # 对专利数据进行去重
        if patent_list:
            filtered_patents, duplicate_p_ids = self.filter_duplicates(patent_list)
            patent_list = filtered_patents
            duplicate_count = len(duplicate_p_ids)

        # 批量保存数据到数据库
        self.save_processed_data(patent_list, owner_list, class_list, company_list, people_list)

        # 更新任务记录
        if self.current_task_id:
            self.complete_task_record(success_count, failed_count, duplicate_count)

        end_time = time.time()
        logger.info(f'专利数据处理完成，耗时: {end_time - start_time:.2f}s, '
                   f'专利: {len(patent_list)}, 所属: {len(owner_list)}, 分类: {len(class_list)}, '
                   f'公司: {len(company_list)}, 个人: {len(people_list)}, '
                   f'成功: {success_count}, 失败: {failed_count}, 重复: {duplicate_count}')

    def save_processed_data(self, patent_list: List[PreprocessPatentMain],
                           owner_list: List[PreprocessPatentOwner],
                           class_list: List[PreprocessPatentClass],
                           company_list: List[PreprocessCompanyMain],
                           people_list: List[PreprocessPeopleMain]):
        """
        批量保存处理后的数据到数据库
        """
        logger.info("开始保存数据到数据库...")
        save_start_time = time.time()

        # 保存专利基础信息
        if patent_list:
            patent_data_list = [asdict(patent) for patent in patent_list]
            success = self.table_patent.batch_insert_patents(patent_data_list)
            logger.info(f"保存专利基础信息: {len(patent_data_list)} 条, {'成功' if success else '失败'}")

        # 保存专利所属关系
        if owner_list:
            owner_data_list = [asdict(owner) for owner in owner_list]
            success = self.table_patent_owner.batch_insert_owners(owner_data_list)
            logger.info(f"保存专利所属关系: {len(owner_data_list)} 条, {'成功' if success else '失败'}")

        # 保存专利分类信息
        if class_list:
            class_data_list = [asdict(class_obj) for class_obj in class_list]
            success = self.table_patent_class.batch_insert_classes(class_data_list)
            logger.info(f"保存专利分类信息: {len(class_data_list)} 条, {'成功' if success else '失败'}")

        # 保存公司信息（如果有公司清洗模块）
        if company_list:
            logger.info(f"需要保存公司信息: {len(company_list)} 条")
            # 这里可以调用公司清洗模块的保存方法
            # self.save_company_data(company_list)

        # 保存个人信息（如果有个人清洗模块）
        if people_list:
            logger.info(f"需要保存个人信息: {len(people_list)} 条")
            # 这里可以调用个人清洗模块的保存方法
            # self.save_people_data(people_list)

        save_end_time = time.time()
        logger.info(f"数据保存完成，耗时: {save_end_time - save_start_time:.2f}s")

    def process_data_with_task_record(self, datalist: List[dict], source_table: str,
                                    start_id: int, end_id: int) -> Tuple[
        List[PreprocessPatentMain], List[PreprocessPatentOwner], List[PreprocessPatentClass],
        List[PreprocessCompanyMain], List[PreprocessPeopleMain]
    ]:
        """
        带任务记录的数据处理方法（用于外部调用，返回处理结果）
        """
        # 创建任务记录
        total_count = len(datalist)
        config_info = {
            'source_table': source_table,
            'start_id': start_id,
            'end_id': end_id,
            'batch_size': total_count
        }

        task_id = self.create_task_record(source_table, start_id, end_id, total_count, config_info)

        # 处理数据（但不保存到数据库）
        logger.info(f'开始处理专利数据批次，数量: {len(datalist)}')
        start_time = time.time()

        # 统计计数器
        success_count = 0
        failed_count = 0
        duplicate_count = 0

        # 初始化结果列表
        patent_list = []
        owner_list = []
        class_list = []
        company_list = []
        people_list = []

        # 处理每条数据
        for data in datalist:
            try:
                # 处理专利基础信息
                if 'patent_id' in data:  # 专利信息数据
                    patent_obj = self.deal_patent_info_data(data)
                    if patent_obj:
                        patent_list.append(patent_obj)
                        success_count += 1
                    else:
                        failed_count += 1

                # 处理专利所属关系
                elif 'owner_id' in data:  # 所属关系数据
                    # 需要先获取对应的专利ID
                    open_id = str(data.get('open_id', ''))
                    if open_id:
                        # 这里需要根据open_id查找对应的专利p_id
                        # 暂时使用简单的方式生成p_id
                        patent_number = data.get('patent_number', open_id)
                        filing_country = self.determine_filing_country(data)
                        p_id = clean_key_fields_hashdata(f"{patent_number}+{filing_country}")

                        owner_obj = self.deal_patent_owner_data(data, p_id)
                        if owner_obj:
                            owner_list.append(owner_obj)

                            # 转换为公司或个人模型
                            company_obj, people_obj = self.convert_to_company_human(owner_obj)
                            if company_obj:
                                company_list.append(company_obj)
                            if people_obj:
                                people_list.append(people_obj)

                            success_count += 1
                        else:
                            failed_count += 1

                # 处理专利分类
                elif 'class_symbol' in data:  # 分类数据
                    open_id = str(data.get('open_id', ''))
                    if open_id:
                        patent_number = data.get('patent_number', open_id)
                        filing_country = self.determine_filing_country(data)
                        p_id = clean_key_fields_hashdata(f"{patent_number}+{filing_country}")

                        class_obj = self.deal_patent_class_data(data, p_id)
                        if class_obj:
                            class_list.append(class_obj)
                            success_count += 1
                        else:
                            failed_count += 1

            except Exception as e:
                logger.error(f"处理单条数据失败: {e}, data: {data}")
                failed_count += 1
                continue

        # 对专利数据进行去重
        if patent_list:
            filtered_patents, duplicate_p_ids = self.filter_duplicates(patent_list)
            patent_list = filtered_patents
            duplicate_count = len(duplicate_p_ids)

        # 更新任务记录
        if task_id:
            self.complete_task_record(success_count, failed_count, duplicate_count)

        end_time = time.time()
        logger.info(f'专利数据处理完成，耗时: {end_time - start_time:.2f}s, '
                   f'专利: {len(patent_list)}, 所属: {len(owner_list)}, 分类: {len(class_list)}, '
                   f'公司: {len(company_list)}, 个人: {len(people_list)}, '
                   f'成功: {success_count}, 失败: {failed_count}, 重复: {duplicate_count}')

        return patent_list, owner_list, class_list, company_list, people_list

    def test_unit(self):
        """
        单元测试方法
        """
        logger.info("开始专利数据清洗单元测试")

        # 使用测试数据
        test_data = [
            {
                "open_id": "4854026",
                "patent_id": "EP3912864",
                "publication_date": "12.03.2025",
                "status": "Active",
                "ip_type": "Patent"
            }
        ]

        # 调用主处理方法（会自动保存到数据库）
        self.deal_datalist(test_data)

        logger.info("单元测试完成")

    def test_with_return(self):
        """
        测试方法（返回处理结果，不保存数据库）
        """
        logger.info("开始专利数据清洗测试（返回结果）")

        # 使用测试数据
        test_data = [
            {
                "open_id": "4854026",
                "patent_id": "EP3912864",
                "publication_date": "12.03.2025",
                "status": "Active",
                "ip_type": "Patent"
            }
        ]

        results = self.process_data_with_task_record(
            test_data,
            'test_table',
            1,
            len(test_data)
        )
        patent_list, owner_list, class_list, company_list, people_list = results

        logger.info(f"测试结果 - 专利: {len(patent_list)}, 所属: {len(owner_list)}, "
                   f"分类: {len(class_list)}, 公司: {len(company_list)}, 个人: {len(people_list)}")

        for patent in patent_list:
            logger.info(f"专利信息: {patent}")

        return results
