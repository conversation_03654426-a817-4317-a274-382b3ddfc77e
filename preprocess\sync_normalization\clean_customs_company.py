import arrow
import time
import traceback
import json
from settings import *
from typing import Optional, Sequence, Tuple, Union, Dict
from preprocess import PreprocessBase
from preprocess.sync_normalization.clean_funcs import preprocess_clean_funcs_obj
from models.preprocess_model import *
from models.public_clean_model import *
from common.database import MysqlConnectionManager
from common.database import DBPoolMysql
from public.public_clean_main import PublicCleanMain
from curd import TableBusinessEmailsAll
from curd import TableBusinessPhonesAll
from curd import TableBusinessSocialsAll
from curd import TableBusinessWebsitesAll
from curd import TableDimWorldLocationCountry
from curd.customs_gz.db_customs import tb_area


class CleanCustomsCompany(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.source_name = 'customs'
        self.mysql_obj_customs_gz: DBPoolMysql = mysql_manager.get_pool("customs_gz")
        self.mysql_obj_company_gz: DBPoolMysql = mysql_manager.get_pool("company_gz")
        self.mysql_obj_customs_new_gz: DBPoolMysql = mysql_manager.get_pool("customs_new_gz")
        self.public_clean_main_obj = PublicCleanMain(mysql_manager)
        self.table_country_obj = tb_area.TableArea(self.mysql_obj_customs_gz)

        self.table_business_emails_all_obj = TableBusinessEmailsAll(self.mysql_obj_company_gz)
        self.table_business_phones_all_obj = TableBusinessPhonesAll(self.mysql_obj_company_gz)
        self.table_business_socials_all_obj = TableBusinessSocialsAll(self.mysql_obj_company_gz)
        self.table_business_websites_all_obj = TableBusinessWebsitesAll(self.mysql_obj_company_gz)
        self.table_dim_world_location_country_obj = TableDimWorldLocationCountry(self.mysql_obj_company_gz)
        # 国家id与国家相关信息映射
        self.country_id_map = self.table_country_obj.get_country_id_map()
        # 国家二字码与国家名称映射
        self.country_code_map = self.table_dim_world_location_country_obj.get_country_code_map()

    def deal_data_company(self, company_data: dict, **kwargs) -> Union[PreprocessCompanyMain, None]:
        """
        处理公司数据
        """
        if not company_data:
            return None
        company_name = company_data.get('name', '')
        company_name_new = company_data.get('name_new', '')
        if not company_name.strip() and not company_name_new.strip():
            return None
        if company_name_new:
            company_name = company_name_new.strip()
        bus_id_old = company_data.get('company_id', 0)
        if not bus_id_old:
            return None
        bus_id_old = str(bus_id_old)
        products = company_data.get('texture', '')
        country_id = company_data.get('country_id', 0)
        country = company_data.get('country', '')
        province = company_data.get('province', '')
        city = company_data.get('city', '')
        address = company_data.get('address', '')
        postcode = company_data.get('postcode', '')

        if country_id:
            country_code = self.country_id_map.get(country_id, {}).get("country_iso_code", "")
        else:
            country_code = ''

        company_address_obj_list = []
        company_products_obj_list = []

        # 处理清洗字段
        country = self.country_code_map.get(country_code.lower(), {}).get('country_en', '') \
            if country_code else country
        postcode = postcode.strip() if postcode else ''

        if address:
            company_address_obj = PreprocessAddress(
                address=address,
                postal_code=postcode,
                country_code_iso2=country_code,
                country_en=country,
                province_en=province,
                city_en=city,
            )
            company_address_obj_list.append(company_address_obj)

        if products:
            product_list = products.lower().split(',')
            product_list = list(set(product_list))
            for product in product_list:
                product = product.strip()
                if not product:
                    continue
                company_product_obj = PreprocessCompanyProducts(
                    product_name=product
                )
                company_products_obj_list.append(company_product_obj)

        # 联系方式
        bus_emails_data_g = kwargs.get('bus_emails_data_g', {})
        bus_phones_data_g = kwargs.get('bus_phones_data_g', {})
        bus_socials_data_g = kwargs.get('bus_socials_data_g', {})
        bus_websites_data_g = kwargs.get('bus_websites_data_g', {})
        be_datalist = bus_emails_data_g.get(bus_id_old, [])
        company_email_obj_list = preprocess_clean_funcs_obj.create_preprocess_email(be_datalist)
        bp_datalist = bus_phones_data_g.get(bus_id_old, [])
        company_phone_obj_list = preprocess_clean_funcs_obj.create_preprocess_phone(bp_datalist)
        bs_datalist = bus_socials_data_g.get(bus_id_old, [])
        company_social_obj_list = preprocess_clean_funcs_obj.create_preprocess_social(bs_datalist)
        bw_datalist = bus_websites_data_g.get(bus_id_old, [])
        company_website_obj_list = preprocess_clean_funcs_obj.create_preprocess_website(bw_datalist)

        preprocess_concat_main_obj = PreprocessConcatMain(
            email_obj_list=company_email_obj_list,
            phone_obj_list=company_phone_obj_list,
            social_obj_list=company_social_obj_list,
            website_obj_list=company_website_obj_list,
        )

        preprocess_company_main_obj: PreprocessCompanyMain = PreprocessCompanyMain(
            source_name=self.source_name,
            company_name=company_name,
            country=country,
            province=province,
            country_code=country_code,
            city=city,
            bus_id_old=bus_id_old,
            company_address_obj_list=company_address_obj_list,
            company_products_obj_list=company_products_obj_list,
            concat_obj=preprocess_concat_main_obj
        )
        return preprocess_company_main_obj

    def deal_datalist(self, datalist: List[dict]) -> Tuple[
        List[PreprocessCompanyMain], List[PreprocessPeopleMain],
        List[PublicCleanCompanyMain], List[PublicCleanHumanMain],
        List[PublicCleanSchoolMain]
    ]:
        """
        处理批量数据
        :param datalist:
        :return:
        """
        (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
         public_clean_company_main_obj_list, public_clean_human_main_obj_list,
         public_clean_school_main_obj_list) = [], [], [], [], []
        logger.info(f'start preprocess deal datalist ...')
        cids = []
        for data in datalist:
            company_id = data.get('company_id', 0)
            if not company_id:
                continue
            company_id = str(company_id)
            cids.append(company_id)

        # 获取 所有 邮箱
        bus_emails_data_g = self.table_business_emails_all_obj.get_bus_emails(cids, bus_type='海关公司')
        # 获取 所有 电话
        bus_phones_data_g = self.table_business_phones_all_obj.get_bus_phones(cids, bus_type='海关公司')
        # 获取 所有 社媒
        bus_socials_data_g = self.table_business_socials_all_obj.get_bus_socials(cids, bus_type='海关公司')
        # 获取 所有 网址
        bus_websites_data_g = self.table_business_websites_all_obj.get_bus_websites(cids, bus_type='海关公司')

        # 预处理 公司信息
        for company_data in datalist:
            company_id = company_data.get('company_id', 0)
            if not company_id:
                continue
            preprocess_company_main_obj = self.deal_data_company(
                company_data=company_data,
                bus_emails_data_g=bus_emails_data_g,
                bus_phones_data_g=bus_phones_data_g,
                bus_socials_data_g=bus_socials_data_g,
                bus_websites_data_g=bus_websites_data_g,
            )
            preprocess_company_main_obj_list.append(preprocess_company_main_obj)

        # ===== 执行公共清洗 =====
        # 执行公共清洗 - 公司
        public_clean_company_main_obj_list: List[PublicCleanCompanyMain] = (
            self.public_clean_main_obj.main_clean_company(preprocess_company_main_obj_list)
        )

        return (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
                public_clean_company_main_obj_list, public_clean_human_main_obj_list,
                public_clean_school_main_obj_list)

    def test_unit(self):
        """
        单元测试
        :return:
        """
        # sql = f'''
        #     select * from db_link.linkedin_company_new_map where rate>=10 order by id limit 1
        # '''
        sql = f'''
            select * from db_customs.t_company where company_id between 20 and 40
        '''
        data_src = self.mysql_obj_customs_new_gz.read(sql, return_dict=True)
        results: List[dict] = data_src.data
        (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
         public_clean_company_main_obj_list, public_clean_human_main_obj_list,
         public_clean_school_main_obj_list) = self.deal_datalist(results)
        for _data in preprocess_company_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in preprocess_people_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in public_clean_company_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in public_clean_human_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in public_clean_school_main_obj_list:
            print(_data)


if __name__ == '__main__':
    from common.database import MysqlConnectionManager
    from models import ConfMySQL

    conf_list = [
        ConfMySQL(name='company_gz', max_connections=1),
        ConfMySQL(name='company_new_gz', max_connections=1),
        ConfMySQL(name='customs_new_gz', max_connections=1),
        ConfMySQL(name='customs_gz', max_connections=1),
    ]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    CleanCustomsCompany(mysql_manager).test_unit()