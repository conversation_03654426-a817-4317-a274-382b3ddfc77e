#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: tb_trademark_class.py
@Date: 2025-01-28
@Desc: 商标分类信息表操作类
@Server: 
"""
from typing import List, Sequence, Dict, Any
from curd.tb_base import TableBase


class TableTrademarkClass(TableBase):
    db = 'db_trademark_staging'
    table = 'trademark_class'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_exist_by_class_id(self, t_id: str, class_id_list: List[str]) -> Dict[str, Any]:
        """
        校验商标分类关系是否存在
        :param t_id: 商标ID
        :param class_id_list: 分类关系ID列表
        :return: 存在的分类关系ID映射
        """
        if not t_id or not class_id_list:
            return {}
        
        class_id_list = list(set(class_id_list))
        placeholders = ','.join(['%s'] * len(class_id_list))
        sql = f'''
            SELECT class_id, id FROM {self.db}.{self.table} 
            WHERE t_id = %s AND class_id IN ({placeholders})
        '''
        params = [t_id] + class_id_list
        data_src = self.db_mysql.read(sql, params, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        class_map = {}
        for result in results:
            class_map[result['class_id']] = result['id']
        return class_map

    def get_classes_by_t_id(self, t_id_list: List[str]) -> Dict[str, List[dict]]:
        """
        根据商标ID获取分类信息
        :param t_id_list: 商标ID列表
        :return: 商标ID对应的分类列表
        """
        if not t_id_list:
            return {}
        
        t_id_list = list(set(t_id_list))
        placeholders = ','.join(['%s'] * len(t_id_list))
        sql = f'''
            SELECT t_id, class_id, class_system, class_code, class_number 
            FROM {self.db}.{self.table} 
            WHERE t_id IN ({placeholders})
            ORDER BY t_id, class_code
        '''
        data_src = self.db_mysql.read(sql, t_id_list, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        class_map = {}
        for result in results:
            t_id = result['t_id']
            if t_id not in class_map:
                class_map[t_id] = []
            class_map[t_id].append(result)
        return class_map

    def get_classes_by_system(self, class_system: str, limit: int = 1000) -> List[dict]:
        """
        根据分类体系获取分类信息
        :param class_system: 分类体系类型
        :param limit: 限制数量
        :return: 分类列表
        """
        if not class_system:
            return []
        
        sql = f'''
            SELECT t_id, class_id, class_code, class_system, description
            FROM {self.db}.{self.table} 
            WHERE class_system = %s
            ORDER BY id DESC
            LIMIT %s
        '''
        data_src = self.db_mysql.read(sql, [class_system, limit], return_dict=True)
        results: Sequence[dict] = data_src.data
        return list(results)

    def batch_insert_classes(self, class_data_list: List[dict]) -> bool:
        """
        批量插入商标分类数据
        :param class_data_list: 分类数据列表
        :return: 是否成功
        """
        if not class_data_list:
            return True
        
        try:
            result = self.db_mysql.save(
                table=f"{self.db}.{self.table}",
                items=class_data_list,
                ignore=True
            )
            return result is not None
        except Exception as e:
            print(f"批量插入商标分类数据失败: {e}")
            return False

    def update_class_by_class_id(self, t_id: str, class_id: str, update_data: dict) -> bool:
        """
        根据分类ID更新分类信息
        :param t_id: 商标ID
        :param class_id: 分类ID
        :param update_data: 更新数据
        :return: 是否成功
        """
        if not t_id or not class_id or not update_data:
            return False
        
        try:
            set_clause = ', '.join([f"{k} = %s" for k in update_data.keys()])
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET {set_clause} 
                WHERE t_id = %s AND class_id = %s
            '''
            params = list(update_data.values()) + [t_id, class_id]
            result = self.db_mysql.execute(sql, params)
            return result.affected_rows > 0
        except Exception as e:
            print(f"更新商标分类信息失败: {e}")
            return False
