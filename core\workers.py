"""
 各类任务执行者，需要在多进程中调度，故为函数，而非类
"""
import asyncio
from .scheduler import Scheduler<PERSON>ingle


def worker_create_task(reader_file):
    """创建任务"""
    scheduler = SchedulerSingle(reader_file)
    scheduler.init()
    tasks = scheduler.reader.create_task()
    worker_num = scheduler.reader.conf.worker
    scheduler.close()
    return tasks, worker_num


def tasks_to_matrix(tasks: list, workers: int, iterator: bool = True):
    """将一维任务列表转为多维矩阵 - 迭代器"""
    total_batches = len(tasks)
    for worker in range(workers):
        if iterator:
            task = (tasks[i] for i in range(worker, total_batches, workers))
        else:
            task = [tasks[i] for i in range(worker, total_batches, workers)]
        yield task


def worker_process_data(item):
    """执行任务 - 单个分片"""
    reader_file, task = item
    p = SchedulerSingle(reader_file)
    p.init()
    p.worker(task)
    p.close()


def worker_process_datas(item):
    """执行任务 - 分片列表"""
    reader_file, tasks = item
    p = SchedulerSingle(reader_file)
    p.init()
    for task in tasks:
        p.worker(task)
    p.close()


ASYNC_WORKER_BATCH = 1000
ASYNC_WORKER = 6


def worker_process_queue_back1(reader_file, queue):
    """执行任务 - 分片列表"""
    p = SchedulerSingle(reader_file)
    p.init()
    task = 1
    while task is not None:
        tasks_async = list()
        for _ in range(ASYNC_WORKER_BATCH):
            task = queue.get()
            if task is None:
                break
            tasks_async.append(task)
        asyncio.run(async_worker_process_batch(p.worker, tasks_async))
    p.close()

def worker_process_queue_back(reader_file, queue):
    """执行任务 - 分片列表"""
    p = SchedulerSingle(reader_file)
    p.init()
    task = 1
    while task is not None:
        tasks_async = list()
        for _ in range(ASYNC_WORKER):
            task = queue.get()
            if task is None:
                break
            tasks_async.append(task)
        asyncio.run(async_worker_process(p.worker, tasks_async))
    p.close()


async def async_worker_process(fun, tasks):
    """执行任务 - 分片列表"""
    await asyncio.gather(*[asyncio.to_thread(fun, task) for task in tasks])


async def async_worker_process_batch(fun, tasks):
    """执行任务 - 分片列表"""
    semaphore = asyncio.Semaphore(ASYNC_WORKER)
    await asyncio.gather(*[async_worker_single(semaphore, fun, task) for task in tasks])


async def async_worker_single(semaphore, fun, task):
    async with semaphore:
        await asyncio.to_thread(fun, task)

def worker_process_queue(reader_file, queue):
    """执行任务 - 分片列表"""
    p = SchedulerSingle(reader_file)
    p.init()
    while True:
        task = queue.get()
        if task is None:
            break
        p.worker(task)
    p.close()
