#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2024-12-31 15:52:10
@Desc: 
@Server: 
"""
from settings import *
from typing import Optional, Union
from models.preprocess_model import *
from models.db_concat import *
from models.db_human import *
from models.db_company import *
from models.public_clean_model import PublicCleanSchoolMain
from models.public_clean_model import PublicCleanConcatMain


class PublicFuncCleanSchoolMain:
    def __init__(self):
        pass

    def deal_clean_school(
            self,
            source_name: str,
            preprocess_education_obj: PreprocessEducation
    ) -> Union[PublicCleanSchoolMain, None]:
        """
        清洗 人物数据 执行逻辑
        :param source_name: 来源名称
        :param preprocess_education_obj: 教育经历 预处理模型数据
        :return:
        """
        socials_obj_list: List[SocialsAll] = []
        websites_obj_list: List[WebsitesAll] = []
        if preprocess_education_obj.concat_obj:
            school_website_obj_list = d if (d := preprocess_education_obj.concat_obj.website_obj_list) else []
            school_social_obj_list = d if (d := preprocess_education_obj.concat_obj.social_obj_list) else []
        else:
            school_website_obj_list = []
            school_social_obj_list = []
        _school_name = preprocess_education_obj.school_name
        _school_bus_id_old = preprocess_education_obj.bus_id_old
        _school_type = preprocess_education_obj.school_type
        _country = preprocess_education_obj.country
        _country_code = preprocess_education_obj.country_code
        _province = preprocess_education_obj.province
        _city = preprocess_education_obj.city
        _start_date = preprocess_education_obj.start_date
        _end_date = preprocess_education_obj.end_date
        _degrees = preprocess_education_obj.degrees
        _majors = preprocess_education_obj.majors
        _minors = preprocess_education_obj.minors
        _gpa = preprocess_education_obj.gpa
        _summary = preprocess_education_obj.summary
        if not _school_name:
            return None
        school_websites = set()
        for school_website_obj in school_website_obj_list:
            _website = school_website_obj.website
            _domain = school_website_obj.domain
            _is_valid = school_website_obj.is_valid
            _is_sensitive = school_website_obj.is_sensitive
            _detail_reason = school_website_obj.detail_reason
            _reason = school_website_obj.reason
            _source_name = school_website_obj.source_name
            if not _website:
                logger.warning(f'缺少实例化必要参数 -> 【WebsitesAll】')
                continue
            school_websites.add(_website)
            _school_websites_obj = WebsitesAll(
                website=_website, domain=_domain, is_valid=_is_valid,
                is_sensitive=_is_sensitive, reason=_reason, source_name=_source_name
            )
            if _school_websites_obj:
                websites_obj_list.append(_school_websites_obj)

        school_linkedin_url = set()
        school_facebook_url = set()
        school_twitter_url = set()
        for school_social_obj in school_social_obj_list:
            _social_url = school_social_obj.social_url
            _social_type = school_social_obj.social_type
            _domain = school_social_obj.domain
            _is_valid = school_social_obj.is_valid
            _source_name = school_social_obj.source_name
            if not _social_url:
                logger.warning(f'缺少实例化必要参数 -> 【SocialsAll】')
                continue
            if _social_type == 'linkedin':
                school_linkedin_url.add(_social_url)
            elif _social_type == 'facebook':
                school_facebook_url.add(_social_url)
            elif _social_type == 'twitter':
                school_twitter_url.add(_social_url)
            _school_social_obj = SocialsAll(
                social_url=_social_url, social_type=_social_type, domain=_domain,
                is_valid=_is_valid, source_name=_source_name
            )
            if _school_social_obj:
                socials_obj_list.append(_school_social_obj)

        school_obj: School = School(
            source_name=source_name, school_name=_school_name,
            school_type=_school_type, country=_country,
            country_code=_country_code, province=_province,
            city=_city,
            websites=','.join(school_websites),
            linkedin_url=','.join(school_linkedin_url),
            facebook_url=','.join(school_facebook_url),
            twitter_url=','.join(school_twitter_url)
        )
        _sid = school_obj.sid
        if not _sid:
            logger.warning(f'缺少实例化必要参数 -> 【HumanEducation】')
            return None
        if _school_bus_id_old:
            bus_id_map_obj = BusIdMapSchool(
                source_name, _school_bus_id_old, _sid,
            )
        else:
            bus_id_map_obj = None

        concat_obj = PublicCleanConcatMain(
            websites_obj_list=websites_obj_list,
            socials_obj_list=socials_obj_list,
        )
        public_clean_school_obj: PublicCleanSchoolMain = PublicCleanSchoolMain(
            school_obj=school_obj,
            bus_id_map_obj=bus_id_map_obj,
            concat_obj=concat_obj
        )
        return public_clean_school_obj
