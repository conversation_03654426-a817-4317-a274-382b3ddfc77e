#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-01-07 11:54:35
@Desc: 
@Server: 
"""
import time
import datetime
from models.preprocess_model import *
from settings import *


class PreprocessCleanFuncs:
    def __init__(self):
        pass

    def date_str_to_timestamp(self, date_str: str, date_format="%Y-%m-%d") -> int:
        if not date_str:
            return 0
        try:
            dt = datetime.datetime.strptime(date_str, date_format)
            # 转换为时间戳
            timestamp = int(dt.timestamp())
            return timestamp
        except Exception as e:
            logging.error(f'{date_str=}; exception: {e}')
            return 0

    def create_preprocess_email(self, bus_emails_datalist: list) -> List[PreprocessEmail]:
        """
        构建 预处理 邮件对象
        """
        company_email_obj_list = []
        if not bus_emails_datalist:
            return company_email_obj_list
        for _data in bus_emails_datalist:
            be_source_name = _data.get('source_name', '')
            be_email = _data.get('email', '')
            be_domain = _data.get('domain', '')
            be_is_mx = _data.get('is_mx', 0)
            be_is_valid = _data.get('is_valid', 0)
            be_reason = _data.get('reason', '')
            be_score = _data.get('score', 0)
            if not be_email:
                continue
            company_email_obj = PreprocessEmail(
                email=be_email,
                domain=be_domain,
                is_mx=be_is_mx,
                is_valid=be_is_valid,
                score=be_score,
                reason=be_reason,
                source_name=be_source_name
            )
            company_email_obj.check_attrs(['*'])
            company_email_obj_list.append(company_email_obj)
        return company_email_obj_list

    def create_preprocess_phone(self, bus_phones_datalist: list) -> List[PreprocessPhone]:
        """
        构建 预处理 电话对象
        """
        company_phone_obj_list = []
        if not bus_phones_datalist:
            return company_phone_obj_list
        for _data in bus_phones_datalist:
            bp_source_name = _data.get('source_name', '')
            bp_phone_raw = _data.get('phone_raw', '')
            bp_phone = _data.get('phone', '')
            bp_phone_type = _data.get('phone_type', 0)
            bp_country_code = _data.get('country_code', '')
            bp_dialing_code = _data.get('dialing_code', '')
            bp_area_code = _data.get('area_code', '')
            bp_telephone = _data.get('telephone', '')
            bp_is_valid = _data.get('is_valid', 0)
            bp_is_ws = _data.get('is_ws', 0)
            bp_national_number = _data.get('national_number', '')
            bp_international_number = _data.get('international_number', '')
            company_phone_obj = PreprocessPhone(
                phone_raw=bp_phone_raw,
                phone=bp_phone,
                phone_type=bp_phone_type,
                country_code=bp_country_code,
                dialing_code=bp_dialing_code,
                area_code=bp_area_code,
                telephone=bp_telephone,
                national_number=bp_national_number,
                international_number=bp_international_number,
                is_valid=bp_is_valid,
                is_ws=bp_is_ws,
                source_name=bp_source_name
            )
            company_phone_obj.check_attrs(['*'])
            company_phone_obj_list.append(company_phone_obj)
        return company_phone_obj_list

    def create_preprocess_social(self, bus_social_datalist: list) -> List[PreprocessSocial]:
        """
        构建 预处理 社媒对象
        """
        company_social_obj_list = []
        if not bus_social_datalist:
            return company_social_obj_list
        for _data in bus_social_datalist:
            bs_source_name = _data.get('source_name', '')
            bs_social_url = _data.get('social_url', '')
            bs_social_type = _data.get('social_type', '')
            company_social_obj = PreprocessSocial(
                social_url=bs_social_url,
                social_type=bs_social_type,
                source_name=bs_source_name
            )
            company_social_obj.check_attrs(['*'])
            company_social_obj_list.append(company_social_obj)
        return company_social_obj_list

    def create_preprocess_website(self, bus_website_datalist: list) -> List[PreprocessWebsite]:
        """
        构建 预处理 网址对象
        """
        company_website_obj_list = []
        if not bus_website_datalist:
            return company_website_obj_list
        for _data in bus_website_datalist:
            bw_source_name = _data.get('source_name', '')
            bw_domain = _data.get('domain', '')
            bw_website = _data.get('website', '')
            bw_detail_reason = _data.get('detail_reason', '')
            bw_reason = _data.get('reason', '')
            bw_is_valid = _data.get('is_valid', 0)
            bw_is_sensitive = _data.get('is_sensitive', 0)
            if 'linkedin.com' in bw_website:
                continue
            company_website_obj = PreprocessWebsite(
                website=bw_website,
                domain=bw_domain,
                is_valid=bw_is_valid,
                is_sensitive=bw_is_sensitive,
                detail_reason=bw_detail_reason,
                reason=bw_reason,
                source_name=bw_source_name
            )
            company_website_obj.check_attrs(['*'])
            company_website_obj_list.append(company_website_obj)
        return company_website_obj_list

    def format_address(self, country_name=None, state_name=None, city_name=None, street1=None, street2=None, postal_code=None, location_name=None):
        """
        构建标准地址的拼接
        :param country_name: 国家名称
        :param state_name: 州省名称
        :param city_name: 城市名称
        :param street1: 街道 1
        :param street2: 街道 2
        :param postal_code: 邮政编码
        :param location_name: 只包含国省市的，但未处理的地址，填该字段则可不必填前三字段
        """
        if not street1:
            return ''
        street1 = street1.strip()
        # 拼接街道地址部分
        address_parts = [street1]
        if street2:  # 如果有街道地址 2，添加
            address_parts.append(street2.strip())

        # 添加市区、州省、国家和邮政编码
        if city_name:
            address_parts.append(city_name.strip())
        if state_name:
            address_parts.append(state_name.strip())
        if country_name:
            # 添加国家名称
            address_parts.append(country_name.strip())
        if location_name and not country_name and not state_name and not city_name:
            address_parts.append(location_name.strip())
        if postal_code:
            address_parts.append(postal_code.strip())

        formatted_address = ", ".join(address_parts)

        return formatted_address


preprocess_clean_funcs_obj = PreprocessCleanFuncs()
