#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: Su<PERSON>u
@File: db_human.py
@Date: 2024/2/23
@Desc: 
@Server: 
"""
from dataclasses import dataclass
from common.utils import clean_key_fields, clean_key_fields_hashdata, keep_alnum_chars


@dataclass
class BusIdMapHuman:
    """
    新旧ID映射表 - 人物
    """
    source_name: str
    bus_id_old: str     # 业务唯一标识(旧)
    bus_id_new: str     # 业务唯一标识(新)

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, bus_id_old: {self.bus_id_old}, bus_id_new: {self.bus_id_new}")


@dataclass
class BusIdMapSchool:
    """
    新旧ID映射表 - 学校
    """
    source_name: str
    bus_id_old: str     # 业务唯一标识(旧)
    bus_id_new: str     # 业务唯一标识(新)

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, bus_id_old: {self.bus_id_old}, bus_id_new: {self.bus_id_new}")


@dataclass
class School:
    """
    学校 模型
    """
    source_name: str
    school_name: str    # 学校名称
    school_type: str = ''  # 学校类型
    country: str = ''  # 学校所属国家
    sid: str = ''  # 学校ID；md5(school_name+country)
    country_code: str = ''  # 学校所属国家二字码
    province: str = ''  # 学校所属州省
    city: str = ''  # 市名
    websites: str = ''  # 学校网址
    linkedin_url: str = ''  # 学校领英
    facebook_url: str = ''  # 学校脸书
    twitter_url: str = ''  # 学校推特

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, school_name: {self.school_name}, country: {self.country}")

    def __post_init__(self):
        self.school_name = clean_key_fields(self.school_name)
        self.school_type = clean_key_fields(self.school_type)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        self.city = clean_key_fields(self.city)

        hash_data_str = self.school_name + self.country
        # 仅保留数字字母
        hash_data_str = keep_alnum_chars(hash_data_str)
        hash_data_str = clean_key_fields(hash_data_str)
        self.sid = clean_key_fields_hashdata(hash_data_str)


@dataclass
class Human:
    """
    人物 照面信息 模型
    """
    source_name: str
    human_name: str       # 人物名称
    pid: str = ''  # 直属公司ID
    hid: str = ''   # 人物ID；md5(pid+human_name)
    human_type: int = 0          # 个人或公司;0-未检测，1-个人，2-公司，3-不确定
    country: str = ''    # 人物所属国家
    country_code: str = ''    # 人物所属国家二字码
    province: str = ''    # 人物所属州省
    city: str = ''    # 人物所属城市
    gender: str = ''    # 性别;F-女性(female),M-男性(male)
    birth_year: str = ''    # 出生年;YYYY
    birth_date: str = ''    # 出生日期;YYYY-MM-DD
    industry: str = ''    # 人物所属行业
    interests: str = ''    # 兴趣爱好; []
    skills: str = ''    # 技能; []
    profiles: str = ''    # 简介
    languages: str = ''    # 语言; []
    certifications: str = ''    # 证书

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, human_name: {self.human_name}, country: {self.country}, pid: {self.pid}")

    def __post_init__(self):
        self.human_name = clean_key_fields(self.human_name)
        self.gender = clean_key_fields(self.gender)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        self.city = clean_key_fields(self.city)
        if self.pid:
            self.hid = clean_key_fields_hashdata(self.pid+self.human_name)
        else:
            self.hid = clean_key_fields_hashdata(self.pid+self.human_name+self.gender+self.country+self.province+self.city)
        if self.birth_date and not self.birth_year:
            self.birth_year = self.birth_date.split('-')[0]

    # def check_hid(self, checker, bus_id_old):
    #     """
    #     校验 数据源ID的已存在映射，优先使用映射表对应的新IDD
    #     """
    #     if not bus_id_old:
    #         return False
    #     bus_id_new = checker.get_bus_id_new(self.source_name, bus_type=1, bus_id_old=bus_id_old)
    #     if bus_id_new and bus_id_new != self.hid:
    #         self.hid = bus_id_new


@dataclass
class HumanAddresses:
    """
    人物 地址信息 模型
    """
    source_name: str
    hid: str         # 人物ID
    address: str    # 地址
    uuid: str = ''  # 业务去重字段；md5(hid+address)
    postal_code: str = ''  # 邮编
    address_type_id: int = 0  # 地址类型映射表ID
    start_date: int = 0  # 地址使用起始日期；精确到年月日
    end_date: int = 0  # 地址使用截止日期；精确到年月日
    country_code_iso2: str = ''  # 地址所属-国家二字码
    country_en: str = ''  # 地址所属-国家名称
    province_en: str = ''  # 地址所属-州省名称
    city_en: str = ''  # 地址所属-城市名称
    street: str = ''  # 地址所属-街道
    province_id: int = 0  # 地址所属-州省ID
    city_id: int = 0  # 地址所属-城市ID

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, address_type_id: {self.address_type_id}, hid: {self.hid}")

    def __post_init__(self):
        self.address = clean_key_fields(self.address)
        self.uuid = clean_key_fields_hashdata(self.hid+self.address)


@dataclass
class HumanEducation:
    """
    人物 教育经历 模型
    """
    source_name: str
    hid: str         # 人物ID
    sid: str    # 学校ID
    start_date: int = 0  # 起始日期；精确到年月日
    end_date: int = 0  # 截止日期；精确到年月日
    degrees: str = ''  # 学业程度
    majors: str = ''  # 专业
    minors: str = ''  # 辅修科目
    summary: str = ''  # 总结
    gpa: str = ''  # 平均学分绩点

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, hid: {self.hid}, sid: {self.sid}")

    def __post_init__(self):
        pass


@dataclass
class HumanExperience:
    """
    人物 任职经历 模型
    """
    source_name: str
    hid: str         # 人物ID
    pid: str    # 公司ID
    title_name: str  # 担任职位
    uuid: str = ''  # 业务去重字段；md5(hid+pid+title_name)
    title_role: str = ''  # 担任职务角色
    title_sub_role: str = ''  # 担任职务子角色
    title_levels: str = ''  # 职级
    department: str = ''  # 理事会，委员会或部门
    start_date: int = 0  # 起始日期；精确到年月日
    end_date: int = 0  # 截止日期；精确到年月日
    post_status: int = 0  # 任职状态;0-未检测，1-在职，2-离职，3-不确定
    summary: str = ''  # 经历总结

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, title_name: {self.title_name}, pid: {self.pid}, hid: {self.hid}")

    def __post_init__(self):
        self.title_name = clean_key_fields(self.title_name)
        self.uuid = clean_key_fields_hashdata(self.hid + self.pid + self.title_name)


@dataclass
class HumanLogo:
    """
    人物 logo信息 模型
    """
    source_name: str
    hid: str  # 人物ID
    logo_url: str   # 人物logo链接
    logo_url_local: str = ''   # 人物本地logo链接

    def display_info(self) -> None:
        print(f"source_name: {self.source_name}, logo_url: {self.logo_url}, hid: {self.hid}")

    def __post_init__(self):
        # TODO: 如果 logo_url_local 值不存在 则 将 logo_url 图片存入cos，并将地址存入logo_url_local
        pass
