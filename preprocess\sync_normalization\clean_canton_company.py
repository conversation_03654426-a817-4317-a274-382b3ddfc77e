#!/usr/bin/env python
# -*- coding: utf-8 -*-
import time
from settings import *
from typing import Optional
from models.preprocess_model import *
from dataclasses import fields
from common.database import MysqlConnectionManager
from public.public_clean_main import PublicCleanMain
from preprocess import PreprocessBase
from curd.tb_base import TableBase
from curd.company_gz.db_std_map import tb_area
import re
from curd.company_gz.db_concat.tb_business_phones_all import TableBusinessPhonesAll
from curd.company_gz.db_concat.tb_business_emails_all import TableBusinessEmailsAll
from curd.company_gz.db_concat.tb_business_websites_all import TableBusinessWebsitesAll


# 使用装饰器，记录方法执行耗时
def timer(method_name):
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 开始计时
            start_time = time.time()

            # 执行方法
            result = func(*args, **kwargs)

            # 停止计时
            end_time = time.time()

            # 统计耗时并输出
            execution_time = end_time - start_time
            log = "{} 执行耗时: {:.2f} 秒".format(method_name, execution_time)
            print(log)
            return result

        return wrapper

    return decorator


class CleanApolloCompany(PreprocessBase, TableBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.mysql_obj_company_gz = mysql_manager.get_pool("company_gz")
        self.source_name = 'canton'
        self.db = "db_expo"
        self.table = "canton_fair_info"
        # self.table_dim_world_location_country_obj = TableDimWorldLocationCountry(self.mysql_obj_company_gz)
        self.table_country_obj = tb_area.TableArea(self.mysql_obj_company_gz)
        # 国家名称与国家名称映射
        self.code_country_map = self.table_country_obj.get_country_map()
        self.public_clean_main_obj = PublicCleanMain(mysql_manager)
        # 电话与bus_id映射
        self.bus_id_phone_dict = {}
        self.phone_all_obj = TableBusinessPhonesAll(self.mysql_obj_company_gz)
        # 邮箱与bus_id映射
        self.bus_id_email_dict = {}
        self.email_all_obj = TableBusinessEmailsAll(self.mysql_obj_company_gz)
        # 网址与与bus_id映射
        self.bus_id_website_dict = {}
        self.web_all_obj = TableBusinessWebsitesAll(self.mysql_obj_company_gz)

    @staticmethod
    def merge_preprocess_company_main_obj(item_data: dict, **kwargs) -> Optional[PreprocessCompanyMain]:
        """
        合并 预处理公司 模型
        :param item_data: PreprocessCompanyMain 模块 映射好的字段
        :return:
        """
        try:
            preprocess_company_main_obj = PreprocessCompanyMain(
                **{f.name: item_data.get(f.name, None) for f in fields(PreprocessCompanyMain)})
        except TypeError:
            preprocess_company_main_obj = None
            logging.warning(f'缺少实例化必要参数')
        except Exception as e:
            preprocess_company_main_obj = None
            logging.warning(f'其他异常')
        return preprocess_company_main_obj

    def get_domain(self, web_url):
        pattern = r'(?:https?://)?([^/?]+)'
        match = re.match(pattern, web_url)
        if match:
            domain = match.group(1)
            # print(domain)
            return domain
        else:
            print("没有匹配到域名。")
            return ''

    def country_clean(self, country, country_iso_code):
        """
        国家清洗
        """
        if not country_iso_code:
            return "", ""
        if country_iso_code in self.code_country_map.keys():
            country_name = self.code_country_map[country_iso_code.lower()]['en']
        else:
            if country:
                logger.warning(f'''没有匹配到的国家名：{country}''')
            country_name = ""
            country_iso_code = ""
        return country_name, country_iso_code

    @staticmethod
    def get_bus_id_list(data_list):
        bus_id_list = set()
        for one in data_list:
            bus_id_list.add(one["md5_id"])
        return list(bus_id_list)

    def deal_data(self, data: dict) -> Optional[PreprocessCompanyMain]:
        """
        处理单条数据
        :param data:
        :return: 公司 公共清洗方法  模型
        """
        company_name = data.get('company_name', '')
        if not company_name:
            return None
        cid = data.get('md5_id', '')
        country_iso_code = data.get('country_iso_code', '')
        country = data.get('country', '')
        country, country_iso_code = self.country_clean(country, country_iso_code)
        address = data.get('address', '')
        industries = data.get('category2', '')

        company_phone_obj_list = []
        company_email_obj_list = []
        company_address_obj_list = []
        company_website_obj_list = []
        company_industry_obj_list = []

        if address:
            company_address_obj_list = [PreprocessAddress(address=address)]
        if data["md5_id"] in self.bus_id_phone_dict.keys():
            for p_d in self.bus_id_phone_dict[data["md5_id"]]:
                company_phone_obj_list.append(PreprocessPhone(source_name=self.source_name, phone_raw=p_d["phone_raw"],
                                                              phone=p_d["phone"], country_code=p_d["country_code"],
                                                              area_code=p_d["area_code"], telephone=p_d["telephone"],
                                                              is_valid=p_d["is_valid"], is_ws=p_d["is_ws"]))
        if data["md5_id"] in self.bus_id_email_dict.keys():
            for p_d in self.bus_id_email_dict[data["md5_id"]]:
                company_email_obj_list.append(PreprocessEmail(source_name=self.source_name, email=p_d["email"],
                                                              domain=p_d["domain"], is_mx=p_d["is_mx"],
                                                              is_valid=p_d["is_valid"], score=p_d["score"],
                                                              reason=p_d["reason"]))
        if data["md5_id"] in self.bus_id_website_dict.keys():
            for p_d in self.bus_id_website_dict[data["md5_id"]]:
                company_website_obj_list.append(PreprocessWebsite(source_name=self.source_name, website=p_d["website"],
                                                                  domain=p_d["domain"], is_sensitive=p_d["is_sensitive"],
                                                                  is_valid=p_d["is_valid"], reason=p_d["reason"],
                                                                  detail_reason=p_d["detail_reason"],))
        if industries:
            company_industry_obj_list = [PreprocessCompanyIndustry(industry=industries)]

        item_data = {
            'source_name': self.source_name,
            'company_name': company_name,
            'country': country,
            'country_code': country_iso_code,
            'bus_id_old': cid,
            'company_address_obj_list': company_address_obj_list,
            'company_phone_obj_list': company_phone_obj_list,
            'company_email_obj_list': company_email_obj_list,
            'company_website_obj_list': company_website_obj_list,
            'company_industry_obj_list': company_industry_obj_list,
        }
        preprocess_company_main_obj = self.merge_preprocess_company_main_obj(item_data)
        return preprocess_company_main_obj

    def deal_datalist(self, datalist: List[dict]):
        """
        处理批量数据
        :param datalist:
        :return:
        """
        preprocess_company_main_obj_list = []
        for data in datalist:
            preprocess_company_main_obj = self.deal_data(data)
            if preprocess_company_main_obj:
                preprocess_company_main_obj_list.append(preprocess_company_main_obj)
        # 执行公共清洗
        self.public_clean_main_obj.main_clean_company(preprocess_company_main_obj_list)

    def test_unit(self):
        """
        单元测试
        :return:
        """
        mysql_obj = self.mysql_obj_company_gz
        sql = f'''select * from {self.db}.{self.table} limit 1000'''
        data_src = mysql_obj.read(sql, return_dict=True)
        results: List[dict] = data_src.data
        bus_id_list = self.get_bus_id_list(results)
        self.bus_id_phone_dict = self.phone_all_obj.get_bus_phones(bus_id_list, "展会")
        self.bus_id_email_dict = self.email_all_obj.get_bus_emails(bus_id_list, "展会")
        self.bus_id_website_dict = self.web_all_obj.get_bus_websites(bus_id_list, "展会")
        self.deal_datalist(results)

    def test_unit2(self):
        print(self.code_country_map)


if __name__ == '__main__':
    from models import ConfMySQL
    conf_list = [ConfMySQL(name='company_gz', max_connections=1)]
    m_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    m_manager.init()
    obj_ = CleanApolloCompany(m_manager)
    obj_.test_unit()
    # obj_.test_unit2()
