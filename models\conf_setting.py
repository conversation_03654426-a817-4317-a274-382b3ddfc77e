from dataclasses import dataclass
from typing import Dict

from .conf_mysql import ConfMySQL, ConfMysqlInfo
from .conf_redis import ConfRedis


@dataclass
class ConfLog:
    level: str
    only_stdout: bool
    record_warning: bool = False
    pid: bool = False  # 是否打印进程号
    tid: bool = False  # 是否打印线程号


@dataclass
class ConfTables:
    record: ConfMySQL
    company: ConfMySQL
    person: ConfMySQL
    trade: ConfMySQL


@dataclass
class ConfGlobal:
    log: ConfLog
    mysql: Dict[str, ConfMysqlInfo]
    mysql_out: Dict[str, ConfMysqlInfo]
    tables: ConfTables
    redis: ConfRedis
    split_size: int
    use_proxy: bool
    workers: int
    queue_max_size: int
    timeout: int
