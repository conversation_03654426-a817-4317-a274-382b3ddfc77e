#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: clean_customs_trade.py
@Date: 2024/5/24
@Desc: 清洗同步海关数据
@Server: 
"""
import sys
sys.path.append('../../')
import time
from typing import Union
from common.database import MysqlConnectionManager
from preprocess import PreprocessBase
from models.preprocess_model.customs import *
from curd.customs_gz.db_customs import tb_area, t_company
from settings import *
from public.public_clean_main import PublicCleanMain


class CleanCustomsTrade(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.mysql_obj_customs_gz = mysql_manager.get_pool("customs_gz")
        self.mysql_obj_company_gz = mysql_manager.get_pool("company_gz")
        self.mysql_obj_customs_new_gz = mysql_manager.get_pool("customs_new_gz")
        self.table_country_obj = tb_area.TableArea(self.mysql_obj_customs_gz)
        self.table_company_obj = t_company.TableCompany(self.mysql_obj_customs_gz)
        # 国家id与国家相关信息映射
        self.country_id_map = self.table_country_obj.get_country_id_map()
        self.public_clean_main_obj = PublicCleanMain(mysql_manager)

    def deal_data(self, data: dict, **kwargs) -> Union[PreprocessCustomsTradeMain, None]:
        """
        处理单条数据
        :param data: 
        :return: 
        """
        trade_id = data.get('trade_id', 0)
        trade_date = data.get('trade_date', 0)
        company_info_data_map = kwargs.get('company_info_data_map', {})
        company_identify_info_map = kwargs.get('company_identify_info_map', {})
        # 过滤贸易日期小于 2000-01-01 的数据
        if not trade_date or trade_date < 946656000000:
            return None
        if data["source"] == 4 and data["product_tag"] == data["product_desc"]:
            data["product_tag"] = ""
        trade_date = trade_date / 1000
        seller_id = seller_id if (seller_id := data.get('seller_id', 0)) in company_info_data_map else 0
        buyer_id = buyer_id if (buyer_id := data.get('buyer_id', 0)) in company_info_data_map else 0
        origin_country = data.get('seller_country', '')
        arrival_country = data.get('buyer_country', '')
        seller_country_id = data.get('seller_country_id', 0)
        buyer_country_id = data.get('buyer_country_id', 0)
        product_country_id = data.get('product_country_id', 0)
        product_country_code = self.country_id_map.get(product_country_id, {}).get("country_iso_code", "")
        origin_country_code = self.country_id_map.get(seller_country_id, {}).get("country_iso_code", "")
        arrival_country_code = self.country_id_map.get(buyer_country_id, {}).get("country_iso_code", "")
        seller_country_code = self.country_id_map[company_info_data_map.get(seller_id, {}).get("country_id", 0)]["country_iso_code"] if seller_id else ""
        buyer_country_code = self.country_id_map[company_info_data_map.get(buyer_id, {}).get("country_id", 0)]["country_iso_code"] if buyer_id else ""
        seller_country_code = company_identify_info_map.get(seller_id, {}).get("identify_country_code_iso2", "") if not seller_country_code else seller_country_code
        buyer_country_code = company_identify_info_map.get(buyer_id, {}).get("identify_country_code_iso2", "") if not buyer_country_code else buyer_country_code

        return PreprocessCustomsTradeMain(
            trade_id=trade_id,
            trade_date=trade_date,
            source=data.get('source', 0),
            openid=data.get('openid', 0),
            trade_code=data.get('trade_code', ''),
            weight=data.get('weight', 0),
            weight_unit=data.get('weight_unit'),
            quantity=data.get('quantity', 0),
            quantity_unit=data.get('quantity_unit', ''),
            price=data.get('price', 0),
            amount=data.get('amount', 0),
            amount_unit=data.get('amount_unit', ''),
            product_country=data.get('product_country', ''),
            product_country_code=product_country_code,
            product_desc=data.get('product_desc', ''),
            product_tag=data.get('product_tag', ''),
            hscode=data.get('product_hscode', ''),
            seller_id=seller_id,
            seller=company_info_data_map.get(seller_id, {}).get('name', ''),
            seller_country=self.country_id_map.get(seller_country_code.lower(), {}).get("country_en", ""),
            seller_country_code=seller_country_code,
            origin_country=origin_country,
            origin_country_code=origin_country_code,
            origin_port=data.get('seller_port', ''),
            buyer_id=buyer_id,
            buyer=company_info_data_map.get(buyer_id, {}).get('name', ''),
            buyer_country=self.country_id_map.get(buyer_country_code.lower(), {}).get("country_en", ""),
            buyer_country_code=buyer_country_code,
            arrival_country=arrival_country,
            arrival_country_code=arrival_country_code,
            arrival_port=data.get('buyer_port', ''),
            notifier=data.get('notifier', ''),
            container=data.get('container', ''),
            transport=data.get('transport', ''),
            trade_type=data.get('trade_type', 4),
            create_time=str(data.get('create_time', "")),
            update_time=str(data.get('update_time', "")),
        )

    def deal_datalist(self, datalist: list):
        """
        处理批量数据
        :param datalist: 
        :return: 
        """
        logger.info(f'start preprocess deal datalist ...')
        s_t = time.time()
        cids = set()
        for data in datalist:
            seller_id = data.get('seller_id', 0)
            buyer_id = data.get('buyer_id', 0)
            if seller_id:
                cids.add(seller_id)
            if buyer_id:
                cids.add(buyer_id)
        # 获取公司基本信息
        company_info_data_map = self.table_company_obj.get_company_info(list(cids))

        # 公司地址解析
        company_identify_info_map = self.table_company_obj.get_company_address_info(list(cids))

        preprocess_customs_trade_main_obj_list = []
        for data in datalist:
            preprocess_custom_trade_data = self.deal_data(data, company_info_data_map=company_info_data_map,
                                                          company_identify_info_map=company_identify_info_map)
            if not preprocess_custom_trade_data:
                continue
            preprocess_customs_trade_main_obj_list.append(preprocess_custom_trade_data)
        logger.info(f'end preprocess deal datalist === hs: {time.time() - s_t:.2f} s')
        # 执行公共清洗
        self.public_clean_main_obj.main_clean_customs_trade(preprocess_customs_trade_main_obj_list)

    def main_run(self):
        """
        当前文件执行入口
        """
        from public_utils_configs.util.mysql_func_public_v2_util import SyncPublicFunc
        sync_public_func_obj = SyncPublicFunc(executor_num=10)
        table_name = 't_trade'
        db = 'db_customs'
        df_start_id = 3432080001
        df_end_id = None
        select_cols = []
        count_num = 10000
        condition_sql = ''
        sync_public_func_obj.sync_data_public_func_by_range_id(
            table_name,
            'trade_id',
            self.deal_datalist,
            self.mysql_obj_customs_gz,
            count_num=count_num,
            db=db,
            select_cols=select_cols,
            df_start_id=df_start_id,
            condition_sql=condition_sql,
            df_end_id=df_end_id
        )

    def test_unit(self):
        # sql = f'''select * from {self.customs_db}.{self.customs_trade_table} order by trade_id limit 1000'''
        sql = f'''select * from db_customs.t_trade where trade_id in (64554, 64649, 230364, 230367, 230368, 230369, 230371)'''
        # sql = f'''select * from {self.customs_db}.{self.customs_trade_table} where trade_id=1763113529'''
        data_src = self.mysql_obj_customs_gz.read(sql, return_dict=True)
        results: List[dict] = data_src.data
        self.deal_datalist(results)


if __name__ == '__main__':
    from models import ConfMySQL

    executor_num = 10
    conf_list = [
        ConfMySQL(name='customs_new_gz', max_connections=executor_num),
        ConfMySQL(name='customs_gz', max_connections=executor_num),
        ConfMySQL(name='company_gz', max_connections=executor_num),
    ]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    clean_customs_trade_obj = CleanCustomsTrade(mysql_manager)
    clean_customs_trade_obj.main_run()
    # clean_customs_trade_obj.test_unit()
