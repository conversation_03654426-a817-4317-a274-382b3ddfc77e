# 临时添加环境变量
import sys
from pathlib import Path

# 添加当前目录到临时环境变量
if (r := Path(__file__).parent.as_posix()) not in sys.path:
    sys.path.append(r)

import importlib
import pkgutil
from pathlib import Path


class PreprocessManager:
    """
    预处理方法管理器
    自动注册当前目录下所有包含preprocess_name属性值的类
    但是为了其他模块成功调用，应该将目标class导入__init__.py中
    """
    dir_cur = Path(__file__).parent

    def __init__(self, directory=None):
        self.directory = self.dir_cur if directory is None else directory
        self.registry = {}

    def init(self):
        modules = self.load_modules_from_dir(self.directory)
        self.registry = self.find_classes_with_attribute(modules, 'preprocess_name')

    def get_preprocessor(self, name):
        return self.registry.get(name)

    @staticmethod
    def load_modules_from_dir(directory):
        modules = []
        for (finder, name, ispkg) in pkgutil.iter_modules([directory]):
            module = importlib.import_module(name)
            modules.append(module)
        return modules

    @staticmethod
    def find_classes_with_attribute(modules, attribute):
        class_registry = {}
        for module in modules:
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if isinstance(attr, type):  # 检查是否为类
                    if hasattr(attr, attribute) and getattr(attr, attribute) is not None:
                        class_registry[getattr(attr, attribute)] = attr
        return class_registry
