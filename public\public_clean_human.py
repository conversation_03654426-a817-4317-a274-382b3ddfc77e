#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2024-12-27 14:21:32
@Desc: 
@Server: 
"""
import time
from settings import *
from typing import Tuple, Union
from models.db_company import *
from models.db_human import *
from models.preprocess_model import *
from models.db_concat import *
from models.public_clean_model import PublicCleanHumanMain
from models.public_clean_model import PublicCleanConcatMain
from models.public_clean_model import PublicCleanSchoolMain
from common.database import MysqlConnectionManager
from public.public_clean_concats import PublicFuncCleanConcatsMain
from public.public_clean_school import PublicFuncCleanSchoolMain
from public.public_save import PublicSaveMain
from common.database import DBPoolMysql
from curd import TableArea


class PublicFuncCleanHumanMain:
    def __init__(self, mysql_manager: MysqlConnectionManager):
        self.mysql_obj_company_gz: DBPoolMysql = mysql_manager.get_pool("company_gz")
        self.public_func_clean_concats_main_obj = PublicFuncCleanConcatsMain(mysql_manager)
        self.public_func_clean_school_main_obj = PublicFuncCleanSchoolMain()
        self.public_save_main_obj = PublicSaveMain(mysql_manager)
        self.table_area_obj = TableArea(self.mysql_obj_company_gz)
        self.area_map = self.table_area_obj.get_area_map()

    def create_human_logo(self, human_obj: Human, preprocess_people_main_obj: PreprocessPeopleMain) -> List[HumanLogo]:
        """
        创建人物标志对象
        """
        p_human_logo_obj_list = d if (d := preprocess_people_main_obj.human_logo_obj_list) else []
        human_logo_obj_list = []
        for p_human_logo_obj in p_human_logo_obj_list:
            _logo_url = p_human_logo_obj.logo_url
            _logo_url_local = p_human_logo_obj.logo_url_local
            if _logo_url or _logo_url_local:
                human_logo_obj = HumanLogo(
                    human_obj.source_name, human_obj.hid,
                    logo_url=_logo_url,
                    logo_url_local=_logo_url_local
                )
                human_logo_obj_list.append(human_logo_obj)
        return human_logo_obj_list

    def create_human_addresses(self, human_obj: Human, preprocess_people_main_obj: PreprocessPeopleMain) -> Union[
        List[HumanAddresses], None]:
        """
        创建人物地址对象列表
        """
        p_human_address_obj_list: List[PreprocessAddress] = d if (
            d := preprocess_people_main_obj.human_address_obj_list) else []
        human_addresses_obj_list: Union[List[HumanAddresses], None] = []
        for p_human_address_obj in p_human_address_obj_list:
            _address = p_human_address_obj.address
            _address_type_id = p_human_address_obj.address_type_id
            _postal_code = p_human_address_obj.postal_code
            _start_date = p_human_address_obj.start_date
            _end_date = p_human_address_obj.end_date
            _country_code_iso2 = p_human_address_obj.country_code_iso2
            _country_en = p_human_address_obj.country_en
            _province_en = p_human_address_obj.province_en
            _city_en = p_human_address_obj.city_en
            _street = p_human_address_obj.street
            _province_id = p_human_address_obj.province_id
            _city_id = p_human_address_obj.city_id
            if not _address:
                logger.warning(f'缺少实例化必要参数 -> 【HumanAddresses】')
                continue
            human_addresses_obj: HumanAddresses = HumanAddresses(
                source_name=human_obj.source_name, hid=human_obj.hid, address=_address,
                postal_code=_postal_code, address_type_id=_address_type_id, start_date=_start_date,
                end_date=_end_date, country_code_iso2=_country_code_iso2, country_en=_country_en,
                province_en=_province_en, city_en=_city_en, street=_street,
                province_id=_province_id, city_id=_city_id
            )
            human_addresses_obj_list.append(human_addresses_obj)
        return human_addresses_obj_list

    def deal_clean_human(
            self,
            preprocess_people_main_obj_list: List[PreprocessPeopleMain],
            company_bus_id_map: dict = None,
    ) -> Tuple[List[PublicCleanHumanMain], List[PublicCleanSchoolMain]]:
        """
        清洗 当前公司下所有人物数据 执行逻辑
        :param company_bus_id_map: 人物当前所属公司ID(公司处理模型生产的 公司ID)映射
        :param preprocess_people_main_obj_list: 人物 预处理模型数据
        :return:
        """
        bus_id_map_obj = None
        if not preprocess_people_main_obj_list:
            return [], []
        if not company_bus_id_map:
            company_bus_id_map = {}
        public_clean_human_main_obj_list = []
        school_obj_dict: dict[str, PublicCleanSchoolMain] = {}
        for preprocess_people_main_obj in preprocess_people_main_obj_list:
            company_bus_id_old = preprocess_people_main_obj.company_bus_id_old
            source_name = preprocess_people_main_obj.source_name
            human_name = preprocess_people_main_obj.human_name
            if not human_name:
                logger.warning(f'缺少实例化必要参数 -> 【Human】')
                continue
            pid = company_bus_id_map.get(company_bus_id_old, '') if company_bus_id_old else ''
            human_type = preprocess_people_main_obj.human_type
            country = preprocess_people_main_obj.country
            country_code = preprocess_people_main_obj.country_code
            province = preprocess_people_main_obj.province
            city = preprocess_people_main_obj.city
            bus_id_old = preprocess_people_main_obj.bus_id_old
            gender = preprocess_people_main_obj.gender
            birth_year = preprocess_people_main_obj.birth_year
            birth_date = preprocess_people_main_obj.birth_date
            industry = preprocess_people_main_obj.industry
            interests = preprocess_people_main_obj.interests
            skills = preprocess_people_main_obj.skills
            profiles = preprocess_people_main_obj.profiles
            languages = preprocess_people_main_obj.languages
            certifications = preprocess_people_main_obj.certifications

            # 匹配标准地区
            area_data = public_funcs_obj.mate_area_data(self.area_map, country_code, country, province, city)
            country_code = area_data.get('country_iso_code', '')
            province_id = area_data.get('province_id', 0)
            city_id = area_data.get('city_id', 0)
            country = area_data.get('country_en', '') if country_code else country
            province = area_data.get('province_en', '') if province_id else province
            city = area_data.get('city_en', '') if city_id else city

            human_obj: Human = Human(
                source_name=source_name, pid=pid, human_name=human_name,
                human_type=human_type, country=country, country_code=country_code,
                province=province, city=city, gender=gender,
                birth_year=birth_year, birth_date=birth_date, industry=industry,
                interests=interests, skills=skills, profiles=profiles,
                languages=languages, certifications=certifications
            )
            # human_obj.check_hid(self.table_bus_id_map_obj, bus_id_old)
            hid = human_obj.hid
            if not hid:
                logger.warning(f'缺少重要参数 -> 【hid】')
                continue
            if not human_obj.human_name:
                logger.warning(f'缺少重要参数 -> 【human_name】')
                continue
            if preprocess_people_main_obj.concat_obj:
                p_human_email_obj_list = d if (d := preprocess_people_main_obj.concat_obj.email_obj_list) else []
                p_human_phone_obj_list = d if (d := preprocess_people_main_obj.concat_obj.phone_obj_list) else []
                p_human_social_obj_list = d if (d := preprocess_people_main_obj.concat_obj.social_obj_list) else []
                p_human_website_obj_list = d if (d := preprocess_people_main_obj.concat_obj.website_obj_list) else []
            else:
                p_human_email_obj_list = []
                p_human_phone_obj_list = []
                p_human_social_obj_list = []
                p_human_website_obj_list = []

            p_human_education_obj_list = d if (d := preprocess_people_main_obj.human_education_obj_list) else []
            p_human_experience_obj_list = d if (d := preprocess_people_main_obj.human_experience_obj_list) else []

            if bus_id_old:
                bus_id_map_obj = BusIdMapHuman(
                    source_name, bus_id_old, hid
                )

            human_logo_obj_list = self.create_human_logo(
                human_obj=human_obj,
                preprocess_people_main_obj=preprocess_people_main_obj
            )

            human_addresses_obj_list: Union[List[HumanAddresses], None] = self.create_human_addresses(
                human_obj=human_obj,
                preprocess_people_main_obj=preprocess_people_main_obj
            )

            emails_obj_list: List[EmailsAll] = self.public_func_clean_concats_main_obj.create_email_all(
                obj_list=p_human_email_obj_list
            )

            phones_obj_list: List[PhonesAll] = self.public_func_clean_concats_main_obj.create_phone_all(
                obj_list=p_human_phone_obj_list
            )

            socials_obj_list: List[SocialsAll] = self.public_func_clean_concats_main_obj.create_social_all(
                obj_list=p_human_social_obj_list
            )

            websites_obj_list: List[WebsitesAll] = self.public_func_clean_concats_main_obj.create_website_all(
                obj_list=p_human_website_obj_list
            )

            business_emails_obj_list: List[BusinessEmailsAll] = self.public_func_clean_concats_main_obj.create_email_bus(
                public_main_obj=human_obj,
                obj_list=p_human_email_obj_list,
                bus_type=2
            )

            business_phones_obj_list: List[BusinessPhonesAll] = self.public_func_clean_concats_main_obj.create_phone_bus(
                public_main_obj=human_obj,
                obj_list=p_human_phone_obj_list,
                bus_type=2
            )

            business_socials_obj_list: List[BusinessSocialsAll] = self.public_func_clean_concats_main_obj.create_social_bus(
                public_main_obj=human_obj,
                obj_list=p_human_social_obj_list,
                bus_type=2
            )

            business_websites_obj_list: List[BusinessWebsitesAll] = self.public_func_clean_concats_main_obj.create_website_bus(
                public_main_obj=human_obj,
                obj_list=p_human_website_obj_list,
                bus_type=2
            )

            human_education_obj_list = []
            p_human_education_obj_list = p_human_education_obj_list if p_human_education_obj_list else []
            for p_human_education_obj in p_human_education_obj_list:
                _start_date = p_human_education_obj.start_date
                _end_date = p_human_education_obj.end_date
                _degrees = p_human_education_obj.degrees
                _majors = p_human_education_obj.majors
                _minors = p_human_education_obj.minors
                _gpa = p_human_education_obj.gpa
                _summary = p_human_education_obj.summary
                public_clean_school_obj = self.public_func_clean_school_main_obj.deal_clean_school(
                    source_name=source_name,
                    preprocess_education_obj=p_human_education_obj
                )
                if not public_clean_school_obj:
                    continue
                school_obj = public_clean_school_obj.school_obj
                _sid = school_obj.sid
                human_education_obj: HumanEducation = HumanEducation(
                    source_name=source_name, hid=hid, sid=_sid,
                    start_date=_start_date,
                    end_date=_end_date, degrees=_degrees, majors=_majors,
                    minors=_minors, summary=_summary, gpa=_gpa
                )
                human_education_obj_list.append(human_education_obj)
                school_obj_dict[_sid] = public_clean_school_obj

            human_experience_obj_list: Union[List[HumanExperience], None] = []
            for p_human_experience_obj in p_human_experience_obj_list:
                _title_name = p_human_experience_obj.title_name
                _title_role = p_human_experience_obj.title_role
                _title_sub_role = p_human_experience_obj.title_sub_role
                _title_levels = p_human_experience_obj.title_levels
                _department = p_human_experience_obj.department
                _start_date = p_human_experience_obj.start_date
                _end_date = p_human_experience_obj.end_date
                _post_status = p_human_experience_obj.post_status
                _summary = p_human_experience_obj.summary
                if not _title_name:
                    logger.warning(f'缺少实例化必要参数: title_name -> 【HumanExperience】=== {bus_id_old}')
                    continue
                human_experience_obj: HumanExperience = HumanExperience(
                    source_name=source_name, hid=hid, pid=pid,
                    title_name=_title_name, title_role=_title_role, title_sub_role=_title_sub_role,
                    title_levels=_title_levels, department=_department, start_date=_start_date,
                    end_date=_end_date, post_status=_post_status, summary=_summary
                )
                human_experience_obj_list.append(human_experience_obj)
            # 联系方式模型
            concat_obj = PublicCleanConcatMain(
                emails_obj_list=emails_obj_list,
                phones_obj_list=phones_obj_list,
                socials_obj_list=socials_obj_list,
                websites_obj_list=websites_obj_list,
                business_emails_obj_list=business_emails_obj_list,
                business_phones_obj_list=business_phones_obj_list,
                business_socials_obj_list=business_socials_obj_list,
                business_websites_obj_list=business_websites_obj_list,
            )

            public_clean_human_main_obj = PublicCleanHumanMain(
                human_obj=human_obj,
                human_logo_obj_list=human_logo_obj_list,
                human_addresses_obj_list=human_addresses_obj_list,
                human_education_obj_list=human_education_obj_list,
                human_experience_obj_list=human_experience_obj_list,
                concat_obj=concat_obj,
                bus_id_map_obj=bus_id_map_obj,
            )
            public_clean_human_main_obj_list.append(public_clean_human_main_obj)

        public_clean_school_main_obj_list = list(school_obj_dict.values())
        return public_clean_human_main_obj_list, public_clean_school_main_obj_list

    def main_clean_human(
            self,
            preprocess_people_main_obj_list: List[PreprocessPeopleMain],
            company_bus_id_map_obj_list: Union[List[BusIdMapCompany], None] = None
    ) -> Tuple[List[PublicCleanHumanMain], List[PublicCleanSchoolMain]]:
        """
        清洗 人物数据 执行入口
        :param preprocess_people_main_obj_list: 预处理模型数据 列表
        :param company_bus_id_map_obj_list: 当前批次数据涉及到的公司 ID新旧映射列表
        :return:
        """
        if not preprocess_people_main_obj_list:
            return [], []
        company_bus_id_map = {}
        if company_bus_id_map_obj_list:
            for company_bus_id_map_obj in company_bus_id_map_obj_list:
                _bus_id_old = company_bus_id_map_obj.bus_id_old
                _bus_id_new = company_bus_id_map_obj.bus_id_new
                company_bus_id_map[_bus_id_old] = _bus_id_new
        public_clean_human_main_obj_list, public_clean_school_main_obj_list = (
            self.deal_clean_human(preprocess_people_main_obj_list, company_bus_id_map))
        # 数据存储至数据库
        save_stime = time.time()
        logging.info(f'start to save mysql === '
                     f'human: {len(public_clean_human_main_obj_list)}; '
                     f'school: {len(public_clean_school_main_obj_list)}')
        self.public_save_main_obj.save_to_mysql(
            public_clean_human_main_obj_list=public_clean_human_main_obj_list,
            public_clean_school_main_obj_list=public_clean_school_main_obj_list,
        )
        save_etime = time.time()
        logging.info(
            f'end to save mysql === '
            f'human: {len(public_clean_human_main_obj_list)}; '
            f'school: {len(public_clean_school_main_obj_list)} === '
            f'hs: {save_etime - save_stime:.2f} s')
        return public_clean_human_main_obj_list, public_clean_school_main_obj_list




