#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public_storage.py
@Date: 2023/12/22
@Desc: 数据存储共有模块
@Server: 
"""
import pymysql
from public.public_funcs import public_funcs_obj

class Storage:
    """
    数据存储
    """

    def __init__(self, table_name: str, db: str, mysql_obj: pymysql.connections.Connection, duplicate_cols: list):
        """

        :param table_name: 存储表名
        :param db: 存储表所在数据库
        :param mysql_obj: 存储表所处实例
        :param duplicate_cols: 存储表数据唯一标识字段
        """
        self.table_name = table_name
        self.db = db
        self.mysql_obj = mysql_obj
        self.duplicate_cols = duplicate_cols

    def check_data(self, data: dict):
        """
        校检数据
        :param data:
        :return:
        """
        for duplicate_col in self.duplicate_cols:
            duplicate_val = data.get(duplicate_col, '')
            if not duplicate_val and duplicate_val != 0:
                return False
        return True

    def main(self, datalist: list, **kwargs):
        """
        执行入口
        :param datalist: 要存储的数据
        :param kwargs:
        :return:
        """
        if not datalist or not self.duplicate_cols:
            return 0
        new_datalist = []
        for data in datalist:
            if not self.check_data(data):
                continue
            new_datalist.append(data)
        # 存储数据
        res = public_funcs_obj.save_data_to_mysql(datalist, self.table_name, self.db, self.duplicate_cols,
                                                  self.mysql_obj, **kwargs)
        return res
