#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: tb_patent_class.py
@Date: 2025-01-28
@Desc: 专利分类信息表操作类
@Server: 
"""
from typing import List, Sequence, Dict, Any
from curd.tb_base import TableBase


class TablePatentClass(TableBase):
    db = 'db_patent_staging'
    table = 'patent_class'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_exist_by_class_id(self, p_id: str, class_id_list: List[str]) -> Dict[str, Any]:
        """
        校验专利分类关系是否存在
        :param p_id: 专利ID
        :param class_id_list: 分类关系ID列表
        :return: 存在的分类关系ID映射
        """
        if not p_id or not class_id_list:
            return {}
        
        class_id_list = list(set(class_id_list))
        placeholders = ','.join(['%s'] * len(class_id_list))
        sql = f'''
            SELECT class_id, id FROM {self.db}.{self.table} 
            WHERE p_id = %s AND class_id IN ({placeholders})
        '''
        params = [p_id] + class_id_list
        data_src = self.db_mysql.read(sql, params, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        class_map = {}
        for result in results:
            class_map[result['class_id']] = result['id']
        return class_map

    def get_classes_by_p_id(self, p_id_list: List[str]) -> Dict[str, List[dict]]:
        """
        根据专利ID获取分类信息
        :param p_id_list: 专利ID列表
        :return: 专利ID对应的分类列表
        """
        if not p_id_list:
            return {}
        
        p_id_list = list(set(p_id_list))
        placeholders = ','.join(['%s'] * len(p_id_list))
        sql = f'''
            SELECT p_id, class_id, class_system, class_symbol, is_primary 
            FROM {self.db}.{self.table} 
            WHERE p_id IN ({placeholders})
            ORDER BY p_id, priority_order
        '''
        data_src = self.db_mysql.read(sql, p_id_list, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        class_map = {}
        for result in results:
            p_id = result['p_id']
            if p_id not in class_map:
                class_map[p_id] = []
            class_map[p_id].append(result)
        return class_map

    def get_classes_by_system(self, class_system: str, limit: int = 1000) -> List[dict]:
        """
        根据分类体系获取分类信息
        :param class_system: 分类体系类型 (IPC/CPC)
        :param limit: 限制数量
        :return: 分类列表
        """
        if not class_system:
            return []
        
        sql = f'''
            SELECT p_id, class_id, class_symbol, class_system, is_primary
            FROM {self.db}.{self.table} 
            WHERE class_system = %s
            ORDER BY id DESC
            LIMIT %s
        '''
        data_src = self.db_mysql.read(sql, [class_system, limit], return_dict=True)
        results: Sequence[dict] = data_src.data
        return list(results)

    def batch_insert_classes(self, class_data_list: List[dict]) -> bool:
        """
        批量插入专利分类数据
        :param class_data_list: 分类数据列表
        :return: 是否成功
        """
        if not class_data_list:
            return True
        
        try:
            result = self.db_mysql.save(
                table=f"{self.db}.{self.table}",
                items=class_data_list,
                ignore=True
            )
            return result is not None
        except Exception as e:
            print(f"批量插入专利分类数据失败: {e}")
            return False

    def update_class_by_class_id(self, p_id: str, class_id: str, update_data: dict) -> bool:
        """
        根据分类ID更新分类信息
        :param p_id: 专利ID
        :param class_id: 分类ID
        :param update_data: 更新数据
        :return: 是否成功
        """
        if not p_id or not class_id or not update_data:
            return False
        
        try:
            set_clause = ', '.join([f"{k} = %s" for k in update_data.keys()])
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET {set_clause} 
                WHERE p_id = %s AND class_id = %s
            '''
            params = list(update_data.values()) + [p_id, class_id]
            result = self.db_mysql.execute(sql, params)
            return result.affected_rows > 0
        except Exception as e:
            print(f"更新专利分类信息失败: {e}")
            return False
