#!/usr/bin/env python3
import os
import sys
import multiprocessing
from pathlib import Path

# 添加当前目录到临时环境变量
if (r := Path(__file__).parent.as_posix()) not in sys.path:
    sys.path.append(r)

from typing import Optional
from core import worker_create_task, worker_process_queue
from exceptions import ReaderConfNotFoundError
from common.utils import statistics
from settings import ARGS, logging


@statistics
def ignition(file: Optional[str] = None):
    # 读取参数
    reader_file = ARGS.reader or os.environ.get("reader") or file
    logging.info(reader_file)
    if reader_file is None:
        raise ReaderConfNotFoundError

    # 读取任务信息 - tasks_1d为一维数组
    tasks_1d, worker_num = worker_create_task(reader_file)

    # 任务转矩阵 - tasks_matrix为多维数组
    # tasks_matrix = tasks_to_matrix(tasks_1d, worker_num, iterator=False)

    # 多进程调度 - 进程池 - worker_num为1时即单进程
    # pool = multiprocessing.Pool(worker_num)
    # pool.map(worker_process_data, ((reader_file, task) for task in tasks))

    # producer
    queue = multiprocessing.Queue()
    for task in tasks_1d:
        queue.put(task)
    for _ in range(worker_num):  # 每一个进程一个退出标记
        queue.put(None)
    # 手动多进程
    executors = list()
    for _ in range(worker_num):
        executor = multiprocessing.Process(target=worker_process_queue, args=(reader_file, queue,))
        executors.append(executor)
        executor.start()
    for executor in executors:
        executor.join()

def test_unit():
    reader_file = 'customs_trade_all.yml'

    # 读取任务信息 - tasks_1d为一维数组
    tasks_1d, worker_num = worker_create_task(reader_file)

    # 任务转矩阵 - tasks_matrix为多维数组
    # tasks_matrix = tasks_to_matrix(tasks_1d, worker_num, iterator=False)

    # 多进程调度 - 进程池 - worker_num为1时即单进程
    # pool = multiprocessing.Pool(worker_num)
    # pool.map(worker_process_data, ((reader_file, task) for task in tasks))

    # producer
    queue = multiprocessing.Queue()
    for task in tasks_1d:
        queue.put(task)
    for _ in range(worker_num):  # 每一个进程一个退出标记
        queue.put(None)
    # 手动多进程
    executors = list()
    for _ in range(worker_num):
        executor = multiprocessing.Process(target=worker_process_queue, args=(reader_file, queue,))
        executors.append(executor)
        executor.start()
    for executor in executors:
        executor.join()


if __name__ == '__main__':
    ignition()
    # test_unit()
