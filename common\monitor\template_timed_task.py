#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: template_timed_task.py
Description: apscheduler调度模板
Author: Peng
Email: <EMAIL>
Date: 2023-07-07
"""

from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger


class TestTask:
    def task1(self):
        print("task1")

    def task2(self):
        print("task2")

    def task3(self):
        print("task3")

    def task4(self):
        print("task4")


def ignition():
    t = TestTask()
    scheduler = BlockingScheduler()
    scheduler.add_job(t.task1, 'interval', minutes=2)
    scheduler.add_job(t.task2, trigger=CronTrigger.from_crontab('0 0 * * *'))
    scheduler.add_job(t.task3, trigger=IntervalTrigger(seconds=20))
    scheduler.add_job(t.task4, trigger=CronTrigger(hour=11, minute=36))
    scheduler.start()


if __name__ == '__main__':
    ignition()
