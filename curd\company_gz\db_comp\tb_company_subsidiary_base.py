#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_subsidiary_base.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence


class TableCompanySubsidiaryBase(TableBase):
    db = 'db_comp'
    table = 'company_subsidiary_base'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_company_subsidiary(self, cids: list) -> dict[str, Sequence[dict]]:
        """
        获取公司子公司
        :param cids: 公司ID列表
        :return:
        """
        if not cids:
            return {}
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select csb.pid,csb.subsidiary_pid,csb.subsidiary_direct,
            cbi.company_name,cbi.country_iso_code,cbi.us_state,cbi.city
            from {self.db}.{self.table} as csb
            left join {self.db}.company_base_information_old as cbi
            on csb.subsidiary_pid=cbi.pid
            where csb.pid in ({bfhs_str})
        '''
        data_src = self.db_mysql.read(sql, cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        company_subsidiary_data: dict = {}
        for result in results:
            pid = result.get('pid', '')
            bus_id_old = result.get('subsidiary_pid', '')
            subsidiary_name = result.get('company_name', '')
            country_iso_code = result.get('country_iso_code', '')
            province = result.get('us_state', '')
            city = result.get('city', '')
            shareholder_direct = result.get('subsidiary_direct', '')
            shareholder_total = ''
            if not pid or not bus_id_old or not subsidiary_name:
                continue
            shareholder_direct = shareholder_direct if shareholder_direct else ''
            shareholder_total = shareholder_total if shareholder_total else ''
            if shareholder_direct == '-' or shareholder_direct.lower() == 'n.a.':
                shareholder_direct = ''
            if shareholder_total == '-' or shareholder_total.lower() == 'n.a.':
                shareholder_total = ''
            data = {'cid_source': pid, 'bus_id_old': bus_id_old, 'subsidiary_name': subsidiary_name,
                    'country_iso_code': country_iso_code, 'province': province, 'city': city,
                    'shareholder_direct': shareholder_direct, 'shareholder_total': shareholder_total}
            if pid not in company_subsidiary_data:
                company_subsidiary_data[pid] = []
            company_subsidiary_data[pid].append(data)
        return company_subsidiary_data
