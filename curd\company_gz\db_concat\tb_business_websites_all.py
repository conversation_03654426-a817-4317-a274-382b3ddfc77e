#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_business_websites_all.py
@Date: 2024/2/26
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableBusinessWebsitesAll(TableBase):
    db = 'db_concat'
    table = 'business_websites_all'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_bus_websites(self, bus_ids: list, bus_type: str, batch_size: int = None) -> dict[str, Sequence[dict]]:
        """
        获取 各维度 网址 信息
        """
        if not bus_ids:
            return {}
        if not batch_size:
            batch_size = len(bus_ids)
        bus_websites_data: dict = {}
        for i in range(0, len(bus_ids), batch_size):
            bus_ids_batch = bus_ids[i:i + batch_size]  # 切片获取当前批次
            bfhs_str = ','.join(['%s'] * len(bus_ids_batch))
            sql = f'''
                select bwa.`bus_id`,bwa.`bus_type`,bwa.`domain`,bwa.`website`,bwa.`source_name`,wvr.`is_valid`,wvr.`status`,wvr.`detail_reson`,wvr.`reason` from {self.db}.{self.table} as bwa
                left join {self.db}.websites_verifier_result as wvr
                on wvr.domain = bwa.domain
                where bwa.bus_id in ({bfhs_str}) and bwa.is_deleted=0
            '''
            if bus_type:
                sql += f' and bwa.bus_type="{bus_type}" '
            data_src = self.db_mysql.read(sql, bus_ids_batch, return_dict=True)
            results: Sequence[dict] = data_src.data

            for result in results:
                bus_id = result.get('bus_id', '')
                bus_type = result.get('bus_type', '')
                domain = result.get('domain', '')
                website = result.get('website', '')
                source_name = result.get('source_name', '')
                is_valid = result.get('is_valid', 0)
                status = result.get('status', 0)
                detail_reason = result.get('detail_reson', '')
                reason = result.get('reason', '')
                is_sensitive = status if status else 0
                data = {
                    'bus_id': bus_id,
                    'bus_type': bus_type,
                    'domain': domain,
                    'website': website,
                    'is_valid': is_valid,
                    'is_sensitive': is_sensitive,
                    'detail_reason': detail_reason,
                    'reason': reason,
                    'source_name': source_name,
                }
                if bus_id not in bus_websites_data:
                    bus_websites_data[bus_id] = []
                bus_websites_data[bus_id].append(data)
        return bus_websites_data


