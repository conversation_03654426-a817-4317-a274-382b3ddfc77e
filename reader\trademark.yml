# 商标数据清洗配置文件
# 数据源：WIPO商标数据

mysql:
  # 数据源连接配置
  source:
    name: spider_gz  # 源数据连接名
    max_connections: 10
  # 目标库连接配置  
  target:
    name: trademark_gz  # 商标库连接名
    max_connections: 10

query:
  # 查询商标基础信息（主表）
  sql: |
    SELECT id, brand_name, logo, category, status, score, trademark_type,
           office_status, gbd_status, application_number, application_date,
           registration_number, registration_date, publication_date, expiry_date,
           status_date, application_language, introduction
    FROM db_spider_wipo.brand_info_new 
    WHERE id >= %s AND id <= %s
    ORDER BY id

split: # 拆分数据的逻辑
  type: id  # id:1 or time:2，必选
  field: # 如果type是time，下面两个都要写，如果type是id，下面可以只填id
    id: id  # 自增id字段，必选
    time: update_time  # 时间字段，可选
  batch_size: 1000  # 任务分片大小，必选

preprocess: clean_trademark  # 预处理方法名，必选

process:  # 结果mysql实例业务名称
  - trademark

worker: 8  # 多进程并行量

# 数据处理配置
data_config:
  # 商标数据源类型
  source_type: 1  # 1:wipo, 2:swissreg
  
  # 数据清洗规则
  clean_rules:
    # 日期格式转换
    date_format: "YYYY-MM-DD"
    # 商标名称清洗
    trademark_name_clean: true
    # 国家代码映射
    country_mapping:
      "WO": "World Intellectual Property Organization"
      "US": "United States"
      "EP": "European Union"
      "CN": "China"
  
  # 实体类型判断规则
  entity_detection:
    # 公司关键词
    company_keywords:
      - "ltd"
      - "limited" 
      - "inc"
      - "incorporated"
      - "corp"
      - "corporation"
      - "llc"
      - "gmbh"
      - "ag"
      - "sa"
      - "company"
      - "group"
    # 默认类型
    default_type: "company"
  
  # 批量处理配置
  batch_config:
    trademark_batch_size: 500
    owner_batch_size: 1000
    class_batch_size: 1000
