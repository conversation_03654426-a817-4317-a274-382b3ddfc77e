#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: Su<PERSON><PERSON>
@File: db_concat.py
@Date: 2024/2/7
@Desc: 联系方式模型
@Server: 
"""
from dataclasses import dataclass
from public.public_funcs import public_funcs_obj
from settings import initialize_phone_obj


@dataclass
class BusinessEmailsAll:
    """
    联系方式 各业务联系邮箱 模型
    """
    source_name: str
    bus_id: str  # 业务唯一标识
    bus_type: int  # 业务唯一标识类型;1-公司,2-人物
    email: str  # 邮箱
    domain: str = ''  # 域名

    def __post_init__(self):
        self.domain = self.email.split('@')[-1]


@dataclass
class BusinessPhonesAll:
    """
    联系方式 各业务联系电话 模型
    """
    source_name: str
    bus_id: str  # 业务唯一标识
    bus_type: int  # 业务唯一标识类型;1-公司,2-人物
    phone: str  # 电话(仅保留数字)

    def __post_init__(self):
        self.phone = public_funcs_obj.retain_numbers(self.phone)


@dataclass
class BusinessSocialsAll:
    """
    联系方式 各业务联系社媒 模型
    """
    source_name: str
    bus_id: str  # 业务唯一标识
    bus_type: int  # 业务唯一标识类型;1-公司,2-人物
    social_url: str  # 社媒链接
    social_type: str = ''  # 社媒类型;linkedin,facebook,twitter,youtube,instagram,pinterest,github,tiktok
    domain: str = ''  # 域名

    def __post_init__(self):
        if not self.domain:
            self.domain = public_funcs_obj.clean_website(self.social_url)
        if self.domain:
            # 社媒链接仅保留域名后的路径
            social_url = self.social_url.split(self.domain)[-1]
            url_paths = [url_path for url_path in social_url.split('/') if url_path]
            self.social_url = '/'.join(url_paths)


@dataclass
class BusinessWebsitesAll:
    """
    联系方式 各业务联系网址 模型
    """
    source_name: str
    bus_id: str  # 业务唯一标识
    bus_type: int  # 业务唯一标识类型;1-公司,2-人物
    website: str  # 网址
    domain: str = ''  # 域名

    def __post_init__(self):
        self.domain = public_funcs_obj.clean_website(self.website)


@dataclass
class EmailsAll:
    """
    联系方式 全量邮箱 模型
    """
    email: str  # 邮箱
    domain: str = ''  # 域名
    is_mx: int = 0  # 是否邮件服务器;0-未检测，1-是，2-否，3-不确定
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    score: int = 0  # 评分
    reason: str = ''  # 原因
    source_name: str = ''

    def __post_init__(self):
        self.domain = self.email.split('@')[-1]


@dataclass
class PhonesAll:
    """
    联系方式 全量电话 模型
    """
    phone_raw: str  # 原始电话
    phone: str = ''  # 纯数字号码
    phone_type: int = 0  # 号码类型，0：未检测，1：固定电话，2：移动电话，3：已检测但未知
    country_code: str = ''  # 国家二字码
    dialing_code: str = ''  # 国际冠码
    area_code: str = ''  # 地区码
    telephone: str = ''  # 号码(去除冠码与区码)
    national_number: str = ''  # 号码属国格式
    international_number: str = ''  # 国际格式号码
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    is_ws: int = 0  # 是否WhatsApp;0-未检测，1-是，2-否，3-不确定
    source_name: str = ''

    def __post_init__(self):
        item = initialize_phone_obj.clean_phone(self.phone_raw, self.country_code)
        is_valid = item.get('is_valid', 0)
        phone = item.get('phone', '')
        dialing_code = item.get('country_code', '')
        area_code = item.get('area_code', '')
        telephone = item.get('telephone', '')
        phone_type = item.get('phone_type', '')
        national_number = item.get('national_number', '')
        international_number = item.get('international_number', '')
        if not self.is_valid and is_valid:
            self.is_valid = is_valid
        if not self.phone and phone:
            self.phone = phone
        if not self.dialing_code and dialing_code:
            self.dialing_code = dialing_code
        if not self.area_code and area_code:
            self.area_code = area_code
        if not self.telephone and telephone:
            self.telephone = telephone
        if not self.phone_type and phone_type:
            self.phone_type = phone_type
        if not self.national_number and national_number:
            self.national_number = national_number
        if not self.international_number and international_number:
            self.international_number = international_number

    def check_phone(self, checker):
        if not self.country_code:
            info = checker.check_number_area(self.phone)
            # {'dialing_code': '1', 'area_code': '917', 'code': 'US', 'en': 'United States', 'cn': '美国', 'province_list': [{'id': 3033, 'en': 'River Cess County', 'cn': '里弗塞斯', 'code': 'LR'}], 'short_number': '4500174'}
            if isinstance(info, int):
                print(f"{info=},{self.phone=}")
            self.country_code = info.get('code', '')
            if not self.area_code:
                self.area_code = info.get('area_code', '')


@dataclass
class SocialsAll:
    """
    联系方式 全量社媒 模型
    """
    social_url: str  # 社媒链接
    social_type: str = ''  # 社媒类型;linkedin,facebook,twitter,youtube,instagram,pinterest,github,tiktok
    domain: str = ''  # 域名
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    source_name: str = ''

    def __post_init__(self):
        if not self.domain:
            self.domain = public_funcs_obj.clean_website(self.social_url)
        if self.domain:
            # 社媒链接仅保留域名后的路径
            social_url = self.social_url.split(self.domain)[-1]
            url_paths = [url_path for url_path in social_url.split('/') if url_path]
            self.social_url = '/'.join(url_paths)


@dataclass
class WebsitesAll:
    """
    联系方式 全量网址 模型
    """
    website: str  # 网址
    domain: str = ''  # 域名
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    is_sensitive: int = 0  # 是否敏感;0-未检测，1-是，2-否，3-不确定
    reason: str = ''  # 原因
    source_name: str = ''

    def __post_init__(self):
        self.domain = public_funcs_obj.clean_website(self.website)
