#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_business_emails_all.py
@Date: 2024/2/26
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableBusinessEmailsAll(TableBase):
    db = 'db_concat'
    table = 'business_emails_all'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_bus_emails(self, bus_ids: list, bus_type: str, batch_size: int = None) -> dict[str, Sequence[dict]]:
        """
        获取 各维度 邮箱 信息
        """
        if not bus_ids:
            return {}
        if not batch_size:
            batch_size = len(bus_ids)
        bus_emails_data: dict = {}
        num = 0
        for i in range(0, len(bus_ids), batch_size):
            bus_ids_batch = bus_ids[i:i + batch_size]  # 切片获取当前批次
            bfhs_str = ','.join(['%s'] * len(bus_ids_batch))
            sql = f'''
                select bea.bus_id,bea.bus_type,bea.email,bea.domain,bea.is_mx,bea.source_name,evr.status,evr.result,evr.score from {self.db}.{self.table} as bea
                left join {self.db}.email_verifier_result as evr
                on evr.email=bea.email
                where bea.bus_id in ({bfhs_str}) and is_deleted=0
            '''
            if bus_type:
                sql += f' and bea.bus_type="{bus_type}" '
            data_src = self.db_mysql.read(sql, bus_ids_batch, return_dict=True)
            results: Sequence[dict] = data_src.data
            num += len(results)
            for result in results:
                bus_id = result.get('bus_id', '')
                bus_type = result.get('bus_type', '')
                email = result.get('email', '')
                domain = result.get('domain', '')
                is_mx = result.get('is_mx', None)
                source_name = result.get('source_name', '')
                status = result.get('status', None)
                reason = result.get('result', None)
                score = result.get('score', None)
                is_mx = is_mx if is_mx else 0
                reason = reason if reason else ''
                score = score if score else 0
                if status == 0:
                    is_valid = 2
                elif status == 1:
                    is_valid = 1
                elif status is None:
                    is_valid = 0
                else:
                    is_valid = 3
                data = {
                    'bus_id': bus_id,
                    'bus_type': bus_type,
                    'email': email,
                    'domain': domain,
                    'is_mx': is_mx,
                    'is_valid': is_valid,
                    'reason': reason,
                    'score': score,
                    'source_name': source_name,
                }
                if bus_id not in bus_emails_data:
                    bus_emails_data[bus_id] = []
                bus_emails_data[bus_id].append(data)
        # print(f'bus email number: {num}')
        return bus_emails_data


