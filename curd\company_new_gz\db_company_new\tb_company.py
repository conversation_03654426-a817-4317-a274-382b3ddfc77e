#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company.py
@Date: 2024/4/25
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence


class TableCompany(TableBase):
    db = 'db_company_new'
    table = 'company'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_pids(self, company_names: List[str], countries: List[str], provinces: List[str]) -> List[dict]:
        """
        获取 公司ID
        """
        if not company_names:
            return []
        values = company_names
        cnames_bfhs = ",".join(["%s"] * len(company_names))
        sql = f'''
            select id, pid, company_name, country, province from {self.db}.{self.table}
            where company_name in ({cnames_bfhs})
        '''
        if countries:
            sql += f' and country in ({",".join(["%s"] * len(countries))}) '
            values += countries
        if provinces:
            sql += f' and province in ({",".join(["%s"] * len(provinces))} '
            values += provinces
        data_src = self.db_mysql.read(sql, value=values, return_dict=True)
        results: List[dict] = data_src.data
        return results
