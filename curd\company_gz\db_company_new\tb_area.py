#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_area.py
@Date: 2024/1/5
@Desc: 获取地区相关数据
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableArea(TableBase):
    db = 'db_company_new'
    country_table = 'c_area_country'
    province_table = 'c_area_province'
    city_table = 'c_area_city'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_area_map_old(self) -> dict:
        """
        获取 地区ID与地区间的 映射
        :return: {"country_iso_code": {"country_en": country_en, "country_cn": country_cn, "province": {"province_id": {"province_en": province_en, "province_cn": province_cn, "city": {city_id: {"city_en": city_en, "city_cn": city_cn}}}}}
        """
        sql = f'''
            select c_country.area, c_country.`name` as country_cn, c_country.`name_en` as country_en, c_country.`code` as country_iso_code,
            c_province.`name` as province_cn, c_province.`name_en` as province_en, c_province.`id` as province_id,
            c_city.`name` as city_cn, c_city.`name_en` as city_en, c_city.`id` as city_id
            from {self.db}.{self.city_table} as c_city
            join {self.db}.{self.province_table} as c_province
            on c_city.province_id=c_province.id
            join {self.db}.{self.country_table} as c_country
            on c_city.country_id=c_country.id
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        area_code_map = {}
        for data in results:
            area = data.get('area', '')
            country_cn = data.get('country_cn', '')
            country_en = data.get('country_en', '')
            country_iso_code = data.get('country_iso_code', '')
            province_cn = data.get('province_cn', '')
            province_en = data.get('province_en', '')
            province_id = data.get('province_id', 0)
            city_cn = data.get('city_cn', '')
            city_en = data.get('city_en', '')
            city_id = data.get('city_id', 0)
            if not country_iso_code:
                continue
            if country_iso_code not in area_code_map:
                area_code_map[country_iso_code] = {"country_iso_code": country_iso_code, "country_en": country_en,
                                                   "country_cn": country_cn, "province": {}, "city": {}}
                # area_code_map[country_en] = {"country_iso_code": country_iso_code, "country_en": country_en, "country_cn": country_cn, "province": {}, "city": {}}
                # area_code_map[country_cn] = {"country_iso_code": country_iso_code, "country_en": country_en, "country_cn": country_cn, "province": {}, "city": {}}
            if province_id not in area_code_map[country_iso_code]["province"]:
                area_code_map[country_iso_code]["province"][province_id] = {"province_en": province_en,
                                                                            "province_cn": province_cn, "city": {}}
            if city_id not in area_code_map[country_iso_code]["province"][province_id]["city"]:
                area_code_map[country_iso_code]["province"][province_id]["city"][city_id] = {"city_en": city_en,
                                                                                             "city_cn": city_cn}
        return area_code_map

    def get_country_code_map(self) -> dict:
        """
        获取 国家 编码
        :return:
        """
        sql = f'''
            select `name_en` as country, `code` as country_iso_code, `name` as country_cn from {self.db}.{self.country_table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        country_code_map = {}
        for data in results:
            country = data.get('country', '')
            country_iso_code = data.get('country_iso_code', '')
            country_cn = data.get('country_cn', '')
            country_code_map[country_iso_code] = {'country_en': country, 'country_cn': country_cn}
        return country_code_map

    def get_province_id_map(self) -> dict:
        """
        获取 省份ID 映射
        :return:
        """
        sql = f'''
            select `name` as province_cn, `name_en` as province_en, `id` as province_id from {self.db}.{self.province_table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        code_map = {}
        for data in results:
            province_id = data.get('province_id', 0)
            province_en = data.get('province_en', '')
            province_cn = data.get('province_cn', '')
            code_map[province_id] = {'province_en': province_en, 'province_cn': province_cn}
        return code_map

    def get_city_id_map(self) -> dict:
        """
        获取 城市ID 映射
        :return:
        """
        sql = f'''
            select `name` as city_cn, `name_en` as city_en, `id` as city_id from {self.db}.{self.city_table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        code_map = {}
        for data in results:
            city_id = data.get('city_id', 0)
            city_cn = data.get('city_cn', '')
            city_en = data.get('city_en', '')
            code_map[city_id] = {'city_en': city_en, 'city_cn': city_cn}
        return code_map


    def get_area_map(self) -> dict:
        country_map = self.get_country_map()
        province_map = self.get_province_map()
        city_map = self.get_city_map()
        area_map = {'country_map': country_map, 'province_map': province_map, 'city_map': city_map}
        return area_map

    def get_country_map(self) -> dict:
        """
        获取 国家 编码
        :return:
        """
        sql = f"select `name_en` en, `code` `code`, `name` cn from {self.db}.{self.country_table}"
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        country_map = dict()
        for data in results:
            en = data.get('en', '').strip()
            code = data.get('code', '').strip()
            cn = data.get('cn', '').strip()
            country_map[code] = {'code': code, 'en': en, 'cn': cn}
            country_map[en] = {'code': code, 'en': en, 'cn': cn}
            country_map[cn] = {'code': code, 'en': en, 'cn': cn}
            # 小写映射
            country_map[code.lower()] = {'code': code, 'en': en, 'cn': cn}
            country_map[en.lower()] = {'code': code, 'en': en, 'cn': cn}
        return country_map

    def get_province_map(self) -> dict:
        """
        获取 省份ID 映射
        :return:
        """
        sql = (f"select t1.`id` id, t1.`name` cn, t1.`name_en` en, t2.`code` `code` "
               f"from {self.db}.{self.province_table} t1 "
               f"left join {self.db}.{self.country_table} t2 on t1.country_id=t2.id")
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        province_map = dict()
        for data in results:
            try:
                _id = data.get('id', 0)
                en = data.get('en', '').strip()
                cn = data.get('cn', '').strip()
                code = data.get('code', '').strip()
                province_map[_id] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
                province_map[en] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
                province_map[cn] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
                # 小写映射
                province_map[en.lower()] = {'id': _id, 'en': en, 'cn': cn, 'code': code}
            except Exception as e:
                continue
        return province_map

    def get_city_map(self) -> dict:
        """
        获取 城市ID 映射
        :return:
        """
        sql = (f"select t1.`id` id, t1.`name` cn, t1.`name_en` en, t1.`province_id` province_id, t3.`code` `code` "
               f"from {self.db}.{self.city_table} t1 "
               # f"left join {self.db}.{self.province_table} t2 on t1.province_id=t2.id "
               f"left join {self.db}.{self.country_table} t3 on t1.country_id=t3.id")
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        city_map = {}
        for data in results:
            _id = data.get('id', 0)
            cn = data.get('cn', '').lower().strip()
            en = data.get('en', '').lower().strip()
            province_id = data.get('province_id', 0)
            code = data.get('code', '').lower().strip()
            city_map[en] = {'id': _id, 'en': en, 'cn': cn, 'province_id': province_id, 'code': code}
            city_map[cn] = {'id': _id, 'en': en, 'cn': cn, 'province_id': province_id, 'code': code}
            # 小写映射
            city_map[en.lower()] = {'id': _id, 'en': en, 'cn': cn, 'province_id': province_id, 'code': code}
        return city_map
