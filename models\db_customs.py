#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: db_customs.py
@Date: 2024/3/26
@Desc: 
@Server: 
"""
import copy
import hashlib
import re
from pydantic import BaseModel, root_validator, validator, conint
from datetime import datetime, date
from typing import List, Optional
from common.utils.public_funcs import keep_alnum_chars


class CustomsCompany(BaseModel):
    """
    海关 - 公司数据
    """
    """公共 海关公司"""
    source: int  # 来源平台，1：wmb，4：dingyi，5：importyeti，6：usimports，7：imporinfo
    openid: str  # 来源平台id（备注：source=1时，此字段为唯一键）
    name: str  # 名称
    name_new: str = ''
    logo: str = ''  # 图标
    introduce: str = ''  # 介绍
    industry: str = ''  # 行业
    scope: str = ''  # 经营范围
    attach: str = ''  # 额外关键字
    texture: str = ''  # introduce industry scope attach
    location: str = ''  # 经纬度
    country_id: int = 0  # 国id
    province_id: int = 0  # 省id
    city_id: int = 0  # 市id
    country: str = ''  # 国名称
    province: str = ''  # 省名称
    city: str = ''  # 市名称
    postcode: str = ''  # 邮编
    address: str = ''  # 地址
    email: str = ''  # 邮箱
    website: str = ''  # 网址
    phone: str = ''  # 电话
    is_ws: int = -1  # 是否是whatsapp，-1：未知，0：不是，1：是
    fburl: str = ''  # Facebook
    tturl: str = ''  # Twitter
    isurl: str = ''  # Instagram
    liurl: str = ''  # LinkedIn
    yturl: str = ''  # YouTube
    pturl: str = ''
    tkurl: str = ''  # Tiktok
    register_person: str = ''  # 法人
    register_number: str = ''  # 注册号
    register_date: str = ''  # 注册日期
    register_type: str = ''  # 注册类型
    register_capital: str = ''  # 注册资金
    register_state: str = ''  # 状态
    company_type: int = 0  # 公司类型，0：未知，1：seller，2：buyer，3：seller and buyer
    repair_method: int = 0  # 修补方式，0：原始数据，1：缺失原始数据可以补充，2：depth，3：linkedin


regex_split_tag = re.compile("[, ]+")


class CustomsTrade(BaseModel):
    """
    海关 - 贸易数据
    """
    trade_date: int  # 贸易日期，秒级时间戳
    source: int = 0  # 平台编号，1：wmb，4：dy，5：importyeti，6：usimport
    ymd: str = ''  # 贸易日期(年月日)
    openid: str = ''  # 平台唯一编号
    uuid: str = ''  # 全局唯一id，由多数据拼接取MD5值，前两位用于分表
    sid: str = ''  # 相似id，生成逻辑同上，但少了3个数值字段
    trade_id: str = ''  # 旧数据ID
    trade_code: str = ''  # 提关单号
    weight: float = 0  # 重量
    weight_unit: str = ''  # 重量单位
    quantity: int = 0  # 数量
    quantity_unit: str = ''  # 数量单位
    price: float = 0  # 单价
    amount: float = 0  # 总价
    amount_unit: str = ''  # 价格单位
    product_country: str = ''  # 原产国名称
    product_country_id: int = 0  # 原产国id
    product_desc: str = ''  # 产品描述
    product_tag: str = ''  # 产品标签
    product_hscode: str = ''  # 清洗后的海关编码
    hscode_2: str = ''
    hscode_4: str = ''
    hscode_6: str = ''
    hscode_8: str = ''
    seller_id: int = 0  # 供应商编号
    seller: str = ''  # 供应商名字
    seller_country_id_true: int = 0  # 供应商所属国id
    seller_country: str = ''  # 起运国
    seller_country_id: int = 0  # 起运国id
    seller_port: str = ''  # 起运港
    buyer_id: int = 0  # 采购商编号
    buyer: str = ''  # 采购商
    buyer_country_id_true: int = 0  # 采购商所属国id
    buyer_country: str = ''  # 抵运国
    buyer_country_id: int = 0  # 抵运国id
    buyer_port: str = ''  # 抵运港
    notifier: str = ''  # 联系人
    container: str = ''  # 货柜
    transport: str = ''  # 运输方式
    trade_type: int = 4  # 贸易类型，0为进口数据，1为出口数据，2为过境数据，3为国内数据，4为未知
    is_similar: int = 0     # 是否相似, 0-不相似，1-相似
    create_time: str = ''
    update_time: str = ''

    @validator('product_tag')
    def _transform_product_tag(cls, v):
        """严格逗号分隔"""
        product_tags = set()
        for i in v.lower().split(','):
            r = i.strip()
            if len(r) < 2 or r.replace(' ', '').isdigit():
                continue
            product_tags.add(r)
        return ','.join(product_tags)

    @root_validator(pre=False)
    def _set_ymd_hscode(cls, values):
        """生成 贸易日期(YYMMDD)"""
        trade_date = values['trade_date']
        hscode = values['product_hscode']
        if trade_date:
            ymd = datetime.fromtimestamp(trade_date).strftime("%Y%m%d")
            values['ymd'] = ymd
        if hscode:
            hscode = hscode.strip()
            values['product_hscode'] = hscode
            if hscode and len(cache := regex_split_tag.split(hscode)) > 0:
                hscode_clean = cls.find_first_numeric_string(cache)
                if hscode_clean:
                    values['hscode_2'] = hscode_clean[:2] if len(hscode_clean) >= 2 else ''
                    values['hscode_4'] = hscode_clean[:4] if len(hscode_clean) >= 4 else ''
                    values['hscode_6'] = hscode_clean[:6] if len(hscode_clean) >= 6 else ''
                    values['hscode_8'] = hscode_clean[:8] if len(hscode_clean) >= 8 else ''
        return values

    @root_validator(pre=False)
    def _calculate_uuid_sid(cls, values):
        """计算uuid（唯一编号）和cid（相似编号）"""
        if values['uuid'] != '' and values['sid'] != '':
            return values
        ymd = values['ymd']
        values_copy = copy.deepcopy(values)
        fields = [
            'trade_date', 'seller', 'seller_country_id', 'seller_port',
            'buyer', 'buyer_country_id', 'buyer_port', 'product_desc',
        ]
        fields_val = ['weight', 'quantity', 'amount']
        data_base_list = [keep_alnum_chars(str(values_copy.get(field, ''))).strip().upper() for field in fields]
        # 三个数值字段取整数部分参与去重
        data_all_list = data_base_list + [str(int(values_copy.get(field, 0))) for field in fields_val]
        uuid = hashlib.md5(''.join(data_all_list).encode()).hexdigest()
        sid = hashlib.md5(''.join(data_base_list).encode()).hexdigest()
        values['uuid'] = f'{ymd[:6]}{uuid}'
        values['sid'] = f'{ymd[:6]}{sid}'
        return values

    @root_validator(pre=False)
    def _set_trade_id(cls, values):
        """生成 trade_id"""
        if not values['trade_id'] or values['trade_id'] == '0':
            values['trade_id'] = values['uuid']
        return values

    @staticmethod
    def find_first_numeric_string(data: List[str]):
        """找到列表中第一个纯数字字符串，没有返回空字符串"""
        for d in data:
            if d.isdigit():
                return d
        return ''

    def check_db_table(self):
        db = f'db_customs_{self.ymd[:4]}'
        table = f't_trade_{self.ymd[:4]}_{self.ymd[4:6]}'
        return db, table


if __name__ == '__main__':
    a = CustomsTrade(trade_date=1643212800, buyer='zs d', seller='ls x', product_hscode='72199013,72199012')
    print(a)
    print(a.check_db_table())
