import os
from typing import List
from common.database import MysqlConnectionManager


class PreprocessBase:
    # preprocess_name为固定属性，必选，否则找不到对象
    preprocess_name = os.path.basename(__file__).split('.')[0]  # 取当前执行文件名称

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        self.mysql_manager = mysql_manager

    def deal_datalist(self, data_list: List[dict]):
        """
        业务处理逻辑入口
        """
        pass

    def main(self, data_list: List[dict]) -> int:
        """
        处理数据入口
        :param data_list: 数据
        :return: 1：处理成功，2：处理失败，3：无数据
        """
        self.deal_datalist(data_list)
        return 1

    def test(self):
        print('test ok!')


class Test(PreprocessBase):
    preprocess_name = "test"


class Example(PreprocessBase):
    preprocess_name = "example"
