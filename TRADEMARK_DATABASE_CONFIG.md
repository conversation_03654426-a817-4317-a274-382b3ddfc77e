# 商标数据库连接配置说明

## 概述
本文档说明如何为商标数据清洗功能配置数据库连接。商标数据清洗需要连接到商标数据库（`trademark_gz`），您需要在现有的数据库配置中添加此连接。

## 数据库连接配置

### 1. 在 settings 配置中添加商标数据库连接

请在您的数据库配置文件中添加以下连接配置：

```python
# 商标数据库连接配置
trademark_gz = ConfMysqlInfo(
    host='your_trademark_db_host',      # 商标数据库主机地址
    port=3306,                          # 数据库端口
    user='your_trademark_db_user',      # 数据库用户名
    password='your_trademark_db_password', # 数据库密码
    db='db_trademark_staging',          # 商标数据库名
    charset='utf8mb4'                   # 字符集
)
```

### 2. 数据库表结构

商标数据清洗功能使用以下五个主要表：

#### 2.1 商标基础信息表 (trademark)
```sql
-- 已提供的DDL文件：test_wipo_data/trademark.ddl.sql
-- 表名：db_trademark_staging.trademark
-- 主要字段：t_id, source_type, source_id, trademark_name, filing_country 等
```

#### 2.2 商标所属信息表 (trademark_owner)  
```sql
-- 已提供的DDL文件：test_wipo_data/trademark_owner.ddl.sql
-- 表名：db_trademark_staging.trademark_owner
-- 主要字段：t_id, owner_id, owner_type, owner_name, country 等
```

#### 2.3 商标分类信息表 (trademark_class)
```sql
-- 已提供的DDL文件：test_wipo_data/trademark_class.ddl.sql  
-- 表名：db_trademark_staging.trademark_class
-- 主要字段：t_id, class_id, class_system, class_code 等
```

#### 2.4 商标去重表 (trademark_dedup) **[新增]**
```sql
-- 已提供的DDL文件：test_wipo_data/trademark_dedup.ddl.sql
-- 表名：db_trademark_staging.trademark_dedup
-- 主要字段：t_id, source_type, source_id, trademark_name, process_count 等
-- 功能：防止重复处理相同的商标数据
```

#### 2.5 商标任务记录表 (trademark_task_record) **[新增]**
```sql
-- 已提供的DDL文件：test_wipo_data/trademark_dedup.ddl.sql
-- 表名：db_trademark_staging.trademark_task_record
-- 主要字段：task_date, task_name, start_id, end_id, task_status 等
-- 功能：记录每天的处理进度和任务状态
```

### 3. 配置文件更新

#### 3.1 更新 reader/trademark.yml
配置文件已创建，包含以下关键配置：
- 数据源连接：`spider_gz`（源数据）
- 目标连接：`trademark_gz`（商标库）
- 预处理方法：`clean_trademark`
- 批量大小：1000
- 并发数：8

#### 3.2 连接池配置示例
```python
# 在您的连接池配置中添加
mysql_connections = {
    'spider_gz': {
        'host': 'source_db_host',
        'port': 3306,
        'user': 'source_user', 
        'password': 'source_password',
        'db': 'db_spider_wipo',
        'charset': 'utf8mb4'
    },
    'trademark_gz': {
        'host': 'trademark_db_host',
        'port': 3306,
        'user': 'trademark_user',
        'password': 'trademark_password', 
        'db': 'db_trademark_staging',
        'charset': 'utf8mb4'
    }
}
```

## 数据流程

### 1. 数据源
- 源表：`db_spider_wipo.brand_info_new`（商标基础信息）
- 源表：`db_spider_wipo.applicants_new`（商标所属关系）
- 源表：`db_spider_wipo.brand_classes`（商标分类，可选）

### 2. 增量处理流程
1. **获取处理范围**：根据上次处理的ID确定本次处理的数据范围
2. **创建任务记录**：在`trademark_task_record`表中创建每日任务记录
3. **获取主数据**：从`brand_info_new`表按ID范围获取商标基础信息
4. **获取关联数据**：根据`brand_id`从关联表获取所属关系和分类数据
   - `applicants_new`表：商标所属关系数据
   - `brand_classes`表：商标分类数据（如果存在）
5. **数据整合**：将主数据和关联数据整合为完整的商标信息
6. **实体识别**：智能识别所属关系是公司还是个人
7. **执行去重检查**：在`trademark_dedup`表中检查是否已处理过
8. **处理新数据**：只处理未重复的数据
9. **批量保存**：将清洗后的数据批量保存到目标表
10. **更新进度**：实时更新任务处理进度
11. **完成任务**：更新任务状态和统计信息

### 3. 数据清洗
- 商标基础信息清洗和统一ID生成
- 所属关系处理，智能区分公司和个人
- 分类信息标准化处理
- 日期格式转换（DD.MM.YYYY → YYYY-MM-DD）
- **去重处理**：基于`t_id`进行去重，避免重复处理
- **任务跟踪**：记录每天的处理进度和结果

### 4. 实体类型识别
- **公司识别**：基于关键词（Ltd, Inc, Corp, LLC等）
- **个人识别**：基于姓名格式（如"Smith, John"）
- **默认规则**：不确定时默认为公司

### 5. 目标表
- `db_trademark_staging.trademark`（商标基础信息）
- `db_trademark_staging.trademark_owner`（商标所属关系）
- `db_trademark_staging.trademark_class`（商标分类信息）
- `db_trademark_staging.trademark_dedup`（去重记录）**[新增]**
- `db_trademark_staging.trademark_task_record`（任务记录）**[新增]**

## 使用方法

### 1. 启动商标数据清洗
```bash
# 使用项目的启动脚本
./up.sh trademark

# 或者直接使用 datak.py
python datak.py --reader trademark.yml
```

### 2. 测试数据清洗功能
```bash
# 运行集成测试脚本（不需要数据库连接）
python test_trademark_integration.py
```

## 文件清单

### 已创建的文件
- `models/preprocess_model/trademark.py` - 商标预处理模型
- `models/db_trademark.py` - 商标数据库模型
- `curd/trademark_gz/` - 商标数据库操作类目录
  - `tb_trademark.py` - 商标表操作类
  - `tb_trademark_owner.py` - 商标所属表操作类
  - `tb_trademark_class.py` - 商标分类表操作类
  - `tb_trademark_dedup.py` - 商标去重表操作类 **[新增]**
  - `tb_trademark_task_record.py` - 任务记录表操作类 **[新增]**
- `preprocess/sync_normalization/clean_trademark.py` - 商标预处理方法（集成去重和任务记录）
- `reader/trademark.yml` - 商标配置文件
- `common/trademark_incremental_manager.py` - 增量处理管理器 **[新增]**
- `test_wipo_data/trademark_dedup.ddl.sql` - 去重和任务记录表DDL **[新增]**
- `test_trademark_integration.py` - 数据集成处理测试脚本 **[新增]**

### 需要您配置的
- 数据库连接信息（host, user, password等）
- 根据实际环境调整配置参数
- 创建商标数据库和表结构（使用提供的DDL文件）
- **执行新增的DDL文件创建去重和任务记录表**

## 新增功能说明

### 去重机制
- **去重表**：`trademark_dedup`表记录所有已处理的商标ID
- **去重逻辑**：处理前检查`t_id`是否已存在，避免重复处理
- **去重统计**：记录处理次数和时间，支持统计分析
- **性能优化**：使用批量查询和索引优化，支持大量数据去重

### 任务记录机制
- **每日任务**：每天创建独立的任务记录，记录处理范围
- **进度跟踪**：实时更新处理进度，包括成功、失败、重复数量
- **状态管理**：支持待处理、处理中、已完成、失败等状态
- **增量处理**：基于上次处理位置，自动计算下次处理范围
- **监控统计**：提供详细的处理统计和性能分析

### 实体识别机制
- **智能识别**：基于名称特征自动识别公司/个人
- **关键词匹配**：使用公司关键词库进行匹配
- **格式识别**：识别个人姓名格式特征
- **可配置规则**：支持自定义识别规则

## 注意事项

1. **数据库权限**：确保商标数据库用户具有 SELECT, INSERT, UPDATE 权限
2. **字符集**：建议使用 utf8mb4 字符集支持完整的 Unicode 字符
3. **连接池**：根据并发需求调整最大连接数
4. **监控**：建议监控数据清洗进度和错误日志
5. **备份**：在生产环境中运行前请备份目标数据库
6. **实体识别**：可根据实际数据特点调整实体识别规则

## 支持

如有问题，请检查：
1. 数据库连接配置是否正确
2. 表结构是否已创建（包括新增的去重和任务记录表）
3. 数据库用户权限是否充足
4. 日志文件中的错误信息
5. **新增表的索引是否正确创建**
6. **去重表的数据是否正常**
7. **实体识别规则是否符合数据特点**
