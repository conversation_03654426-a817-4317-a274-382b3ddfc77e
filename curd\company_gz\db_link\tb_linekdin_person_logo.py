#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-01-07 15:23:57
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence, Dict


class TableLinkPersonLogo(TableBase):
    db = 'db_link'
    table = 'linekdin_person_logo'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_logo_infos(self, hids: list, batch_size: int = None) -> dict:
        """
        获取 人物 logo 信息
        :param hids: 人物ID列表
        :return: {人物ID: []}
        """
        if not hids:
            return {}
        if not batch_size:
            batch_size = len(hids)
        data: dict = {}
        for i in range(0, len(hids), batch_size):
            hids_batch = hids[i:i + batch_size]  # 切片获取当前批次
            bfhs_str = ','.join(['%s'] * len(hids_batch))
            sql = f'''
                select * from {self.db}.{self.table} where lid in ({bfhs_str}) and is_deleted=0
            '''
            data_src = self.db_mysql.read(sql, value=hids_batch, return_dict=True)
            results: Sequence[dict] = data_src.data

            for result in results:
                lid = result['lid']
                logo_url = result['logo_url']
                logo_path = result['logo_path']
                if 'http' not in logo_url:
                    continue
                if lid not in data:
                    data[lid] = []
                data[lid].append({
                    'logo_url': logo_url,
                    'logo_path': logo_path,
                })
        return data
