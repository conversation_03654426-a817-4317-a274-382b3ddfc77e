#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2024-12-25 11:29:35
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableWsNumberCheckInfo(TableBase):
    db = 'db_concat'
    table = 'whatsapp_number_check_info'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_is_ws(self, numbers: Sequence[str]) -> Sequence[dict]:
        if not numbers:
            return []
        bfhs_str = ','.join(['%s'] * len(numbers))
        sql = f'''
            select number,is_ws from {self.db}.{self.table}
            where number in ({bfhs_str})
        '''
        data_src = self.db_mysql.read(sql, numbers, return_dict=True)
        if not data_src:
            return []
        results: Sequence[dict] = data_src.data
        return results

