import re
import yaml
from pathlib import Path
from typing import Union
from models import <PERSON>f<PERSON><PERSON><PERSON>, ConfMySQL, ConfSplit, ConfSplitField, ConfQuery
from settings import CONF_GLOBAL


class ConfInit:
    _reader_dir = Path(__file__).parent

    def __init__(self, file: str):
        self.file = file
        self.conf: Union[ConfReader, None] = None
        self._regex_sql = re.compile(r"select\s+(.*?)\s+from\s+([\w.]+)(?:\s+where\s+(.*?))?$", re.IGNORECASE)

    def init(self):
        data = self.read_yml()
        self.init_conf(data)
        self.check_mysql_conf()
        self.parse_sql()
        return self.conf

    def read_yml(self):
        filepath = Path(self.file)
        if not filepath.exists():
            filepath = Path(self._reader_dir, self.file)
        # 读取配置文件
        with open(filepath, encoding='utf8') as f:
            return yaml.safe_load(f)

    def init_conf(self, data: dict):
        # 从字典中创建 Config 类的实例
        self.conf = ConfReader(
            name=data['name'],
            source=data['source'],
            conn_inner=data['conn_inner'],
            mysql=ConfMySQL(**data['mysql']),
            mysql_other=[ConfMySQL(**d) for d in data['mysql_other']],
            query=ConfQuery(**data['query']),
            split=ConfSplit(**{k: ConfSplitField(**v) if k == 'field' else v for k, v in data['split'].items()}),
            preprocess=data['preprocess'],
            process=data['process'] if 'process' in data else [],
            worker=data['worker']
        )

    def check_mysql_conf(self):
        # 校验数据源的配置是否正确
        mysql_conf_set = set(CONF_GLOBAL.mysql.keys())
        if self.conf.mysql.name not in mysql_conf_set:
            raise ValueError("数据源mysql配置错误")
        # 校验其他源的配置是否正确
        if len(self.conf.mysql_other) > 0:
            cache_list = list()
            for mysql in self.conf.mysql_other:
                if mysql.name in mysql_conf_set:
                    cache_list.append(mysql)
            self.conf.mysql_other = cache_list
        if not self.conf.conn_inner:
            CONF_GLOBAL.mysql = CONF_GLOBAL.mysql_out

    def parse_sql(self):
        """解析sql，只能识别简单的select field from table where condition"""
        if self.conf.query.sql == "":
            # sql和查询字段都为空的情况
            if self.conf.query.select == '' or self.conf.query.table == '':
                raise ValueError("查询参数解析错误！")
            # sql为空的情况
            self.conf.query.where = self.conf.query.where or None
        # 查询字段为空的情况
        elif match := self._regex_sql.search(self.conf.query.sql):
            select, table, where = match.groups()
            self.conf.query.select = select
            self.conf.query.table = table
            self.conf.query.where = where
        else:
            raise ValueError("查询sql解析失败！")
        # 给表添加数据库
        self.conf.query.table = f"{self.conf.mysql.db}.{self.conf.query.table.split('.')[-1]}"
        self.conf.query.sql = f"select {self.conf.query.select} from {self.conf.query.table}"
        if self.conf.query.where is not None:
            self.conf.query.sql += f" where {self.conf.query.where}"

    def check_split(self):
        pass
