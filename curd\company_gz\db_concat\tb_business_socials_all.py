#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_business_socials_all.py
@Date: 2024/2/26
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableBusinessSocialsAll(TableBase):
    db = 'db_concat'
    table = 'business_socials_all'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_bus_socials(self, bus_ids: list, bus_type: str, batch_size: int = None) -> dict[str, Sequence[dict]]:
        """
        获取 各维度 社媒 信息
        """
        if not bus_ids:
            return {}
        if not batch_size:
            batch_size = len(bus_ids)
        bus_socials_data: dict = {}
        for i in range(0, len(bus_ids), batch_size):
            bus_ids_batch = bus_ids[i:i + batch_size]  # 切片获取当前批次
            bfhs_str = ','.join(['%s'] * len(bus_ids_batch))
            sql = f'''
                select bus_id,bus_type,social_type,social_url,source_name from {self.db}.{self.table}
                where bus_id in ({bfhs_str}) and is_deleted=0
            '''
            if bus_type:
                sql += f' and bus_type="{bus_type}" '
            data_src = self.db_mysql.read(sql, bus_ids_batch, return_dict=True)
            results: Sequence[dict] = data_src.data

            for result in results:
                bus_id = result.get('bus_id', '')
                bus_type = result.get('bus_type', '')
                social_url = result.get('social_url', '')
                social_type = result.get('social_type', '')
                source_name = result.get('source_name', '')
                if not social_url:
                    continue
                if social_type == 'facebook':
                    social_url_complete = f'https://www.facebook.com/{social_url}'
                elif social_type == 'linkedin':
                    social_url_complete = f'https://www.linkedin.com/{social_url}'
                elif social_type == 'twitter':
                    social_url_complete = f'https://twitter.com/{social_url}'
                elif social_type == 'github':
                    social_url_complete = f'https://github.com/{social_url}'
                elif social_type == 'youtube':
                    social_url_complete = f'https://www.youtube.com/{social_url}'
                elif social_type == 'instagram':
                    social_url_complete = f'https://www.instagram.com/{social_url}'
                elif social_type == 'pinterest':
                    social_url_complete = f'https://www.pinterest.com/{social_url}'
                elif social_type == 'tiktok':
                    social_url_complete = f'https://www.tiktok.com/{social_url}'
                else:
                    social_url_complete = social_url
                data = {
                    'bus_id': bus_id,
                    'bus_type': bus_type,
                    'social_url': social_url_complete,
                    'social_type': social_type,
                    'source_name': source_name,
                }
                if bus_id not in bus_socials_data:
                    bus_socials_data[bus_id] = []
                bus_socials_data[bus_id].append(data)
        return bus_socials_data


