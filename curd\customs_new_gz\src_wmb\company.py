#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_trade.py
@Date: 2024/5/29
@Desc:
@Server:
"""
from curd.tb_base import TableBase
from typing import List, Sequence


class TableCompany(TableBase):
    db = "src_wmb"

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_company_info(self, openid_list: list) -> List:
        """
        根据 源id 获取公司信息
        """
        openid_list = list(set(openid_list))
        zwf = ",".join(["%s"]*len(openid_list))
        # sql = f'''select * from {self.db}.company where company_id in ({zwf})'''
        fields_company_read = [
            'company_id', 'name', 'business', 'country', 'address', 'manager',
            'telephone', 'fax', 'email', 'website'
        ]
        sql = f"select {', '.join(fields_company_read)} from {self.db}.company where company_id in ({zwf})"
        data_src = self.db_mysql.read(sql, value=openid_list, return_dict=False)
        results: List = data_src.data
        return results

