#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: db_mysql.py
Description: 高效操作数据库
Author: Peng
Email: <EMAIL>
Date: 2024-01-04
"""
from typing import Union, List, Optional, Sequence, Any, Dict
from models import MysqlResult, ConfMySQL
from exceptions import MysqlConfNotFoundError
from settings import *
from public_utils_configs.util.mysql_pool_v3 import DBPoolMysql, PoolMysql


class MysqlConnectionManager:
    """连接池管理器"""

    def __init__(self, conf_list: List[ConfMySQL]):
        self._conf_list = conf_list
        self.connections = {}

    def init(self):
        for conf in self._conf_list:
            mysql_info = CONF_GLOBAL.mysql.get(conf.name)
            if mysql_info is None:
                raise MysqlConfNotFoundError
            # mysql_info['db'] = conf.db
            self.connections[conf.name] = DBPoolMysql(mysql_info.__dict__, max_connections=conf.max_connections)

    def get_pool(self, name):
        return self.connections.get(name)

    def close(self):
        for conn in self.connections.values():
            conn.close()
