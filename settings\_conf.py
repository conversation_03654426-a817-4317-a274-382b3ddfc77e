import os
import yaml
from pathlib import Path
from models import ConfGlobal, ConfLog, ConfRedis, ConfMysqlInfo, ConfTables, ConfMySQL
from settings._setting import ARGS, DIR_CONF

# 配置文件
_files = [ARGS.conf, os.environ.get("conf", None), "config.yml", "config.yaml"]
# 找到下一个存在的配置文件
# noinspection PyUnboundLocalVariable
_file = next((r for file in _files if isinstance(file, str) and (r := Path(DIR_CONF, file)).exists()), None)
if _file is None:
    raise FileNotFoundError("config.yml not found")
# 读取配置文件
with open(_file, encoding='utf8') as f:
    _data = yaml.safe_load(f)

# 从字典中创建 Config 类的实例
CONF_GLOBAL = ConfGlobal(
    log=ConfLog(**_data['log']),
    mysql={k: ConfMysqlInfo(**v) for k, v in _data['mysql'].items()},
    mysql_out={k: ConfMysqlInfo(**v) for k, v in _data['mysql_out'].items()},
    tables=ConfTables(**{k: ConfMySQL(**v) for k, v in _data['tables'].items()}),
    redis=ConfRedis(**_data['redis']),
    split_size=_data['split_size'],
    use_proxy=_data['use_proxy'],
    workers=_data['workers'],
    queue_max_size=_data['queue_max_size'],
    timeout=_data['timeout']
)

if __name__ == '__main__':
    print(CONF_GLOBAL)
    print(CONF_GLOBAL.mysql)
    print(CONF_GLOBAL.redis)
