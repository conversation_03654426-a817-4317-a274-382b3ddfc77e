#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2024-12-27 14:25:28
@Desc: 公共清洗方法 联系方式
@Server: 
"""
from settings import *
from typing import Optional, Union, List
from models.db_concat import *
from models.db_company import *
from models.db_human import *
from models.preprocess_model import *
from curd import TableWsNumberCheckInfo
from curd import TableSTDPhone
from common.database import DBPoolMysql
from common.database import MysqlConnectionManager


class PublicFuncCleanConcatsMain:
    def __init__(self, mysql_manager: MysqlConnectionManager):
        self.mysql_obj_company_gz: DBPoolMysql = mysql_manager.get_pool("company_gz")
        self.table_ws_number_check_info_obj = TableWsNumberCheckInfo(self.mysql_obj_company_gz)
        self.table_std_phone_obj = TableSTDPhone(self.mysql_obj_company_gz)

    def create_email_all(self, obj_list: List[PreprocessEmail]) -> List[EmailsAll]:
        """
        创建全量邮箱对象
        """
        p_obj_list_tmp: List[EmailsAll] = []
        for _obj in obj_list:
            _email = _obj.email.strip()
            _domain = _obj.domain
            _is_mx = _obj.is_mx
            _is_valid = _obj.is_valid
            _score = _obj.score
            _reason = _obj.reason
            _source_name = _obj.source_name
            if not _email:
                logger.warning(f'缺少实例化必要参数 -> 【EmailsAll】')
                continue
            p_obj: EmailsAll = EmailsAll(
                email=_email, domain=_domain, is_mx=_is_mx,
                is_valid=_is_valid, score=_score, reason=_reason,
                source_name=_source_name
            )
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp

    def create_phone_all(self, obj_list: List[PreprocessPhone]) -> List[PhonesAll]:
        """
        创建全量电话对象
        """
        p_obj_list_tmp: List[PhonesAll] = []
        phone_numbers = set()  # 批量校验电话是否注册 ws
        for _obj in obj_list:
            _phone_raw = _obj.phone_raw
            if not _phone_raw:
                continue
            phone_numbers.add(_phone_raw)
        if not phone_numbers:
            return p_obj_list_tmp
        # 批量校验电话是否注册 ws
        number_ws_map = {}
        check_results = self.table_ws_number_check_info_obj.check_is_ws(list(phone_numbers))
        for check_result in check_results:
            number = check_result.get('number', '')
            _is_ws = check_result.get('is_ws', 0)
            if _is_ws == 1:
                is_ws = 1
            elif _is_ws == 0:
                is_ws = 2
            else:
                is_ws = 0
            number_ws_map[number] = is_ws

        for _obj in obj_list:
            _phone_raw = _obj.phone_raw.strip()
            _phone = _obj.phone.strip()
            _phone_type = _obj.phone_type
            _country_code = _obj.country_code
            _dialing_code = _obj.dialing_code
            _area_code = _obj.area_code
            _telephone = _obj.telephone
            _national_number = _obj.national_number
            _international_number = _obj.international_number
            _is_valid = _obj.is_valid
            _is_ws = _obj.is_ws
            _source_name = _obj.source_name
            if not _phone_raw:
                logger.warning(f'缺少实例化必要参数 -> 【PhonesAll】')
                continue
            if len(_phone) < 5:
                continue
            if not _is_ws and _phone_raw in number_ws_map:
                _is_ws = number_ws_map[_phone_raw]
            p_obj: PhonesAll = PhonesAll(
                phone_raw=_phone_raw, phone=_phone, phone_type=_phone_type,
                country_code=_country_code, dialing_code=_dialing_code, area_code=_area_code,
                telephone=_telephone, national_number=_national_number, international_number=_international_number,
                is_valid=_is_valid, is_ws=_is_ws, source_name=_source_name
            )
            # 电话再校验
            p_obj.check_phone(self.table_std_phone_obj)
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp

    def create_social_all(self, obj_list: List[PreprocessSocial]) -> List[SocialsAll]:
        """
        创建全量社媒对象
        """
        p_obj_list_tmp: List[SocialsAll] = []
        for _obj in obj_list:
            _social_url = _obj.social_url.strip()
            _social_type = _obj.social_type
            _domain = _obj.domain
            _is_valid = _obj.is_valid
            _source_name = _obj.source_name
            if not _social_url:
                logger.warning(f'缺少实例化必要参数 -> 【SocialsAll】')
                continue
            p_obj: SocialsAll = SocialsAll(
                social_url=_social_url, social_type=_social_type, domain=_domain,
                is_valid=_is_valid, source_name=_source_name
            )
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp

    def create_website_all(self, obj_list: List[PreprocessWebsite]) -> List[WebsitesAll]:
        """
        创建全量网址对象
        """
        p_obj_list_tmp: List[WebsitesAll] = []
        for _obj in obj_list:
            _website = _obj.website.strip()
            _domain = _obj.domain
            _is_valid = _obj.is_valid
            _is_sensitive = _obj.is_sensitive
            _detail_reason = _obj.detail_reason
            _reason = _obj.reason
            _source_name = _obj.source_name
            if not _website:
                logger.warning(f'缺少实例化必要参数 -> 【WebsitesAll】')
                continue
            if 'twitter.com' in _website or 'facebook.com' in _website or 'youtube.com' in _website or 'linkedin.com' in _website or 'instagram.com' in _website or 'instagram.com' in _website:
                continue
            p_obj: WebsitesAll = WebsitesAll(
                website=_website, domain=_domain, is_valid=_is_valid,
                is_sensitive=_is_sensitive, reason=_reason, source_name=_source_name
            )
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp

    def create_email_bus(self, public_main_obj: Union[Company, Human], obj_list: List[PreprocessEmail],
                         bus_type: int) -> List[BusinessEmailsAll]:
        """
        创建业务邮箱对象
        :param public_main_obj: 公共主模型
        :param obj_list: 要处理的预处理模型
        :param bus_type: 1-公司；2-人物
        :return:
        """
        p_obj_list_tmp: List[BusinessEmailsAll] = []
        if bus_type == 1:
            bus_id = public_main_obj.pid
        elif bus_type == 2:
            bus_id = public_main_obj.hid
        else:
            logger.warning(f'无效的 bus_type -> {bus_type}')
            return p_obj_list_tmp

        for _obj in obj_list:
            _email = _obj.email.strip()
            _domain = _obj.domain
            if not _email:
                logger.warning(f'缺少实例化必要参数 -> 【BusinessEmailsAll】')
                continue
            p_obj: BusinessEmailsAll = BusinessEmailsAll(
                source_name=public_main_obj.source_name, bus_id=bus_id, bus_type=bus_type,
                email=_email, domain=_domain
            )
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp

    def create_phone_bus(self, public_main_obj: Union[Company, Human], obj_list: List[PreprocessPhone],
                         bus_type: int) -> List[BusinessPhonesAll]:
        """
        创建业务电话对象
        :param public_main_obj: 公共主模型
        :param obj_list: 要处理的预处理模型
        :param bus_type: 1-公司；2-人物
        :return:
        """
        p_obj_list_tmp: List[BusinessPhonesAll] = []
        if bus_type == 1:
            bus_id = public_main_obj.pid
        elif bus_type == 2:
            bus_id = public_main_obj.hid
        else:
            logger.warning(f'无效的 bus_type -> {bus_type}')
            return p_obj_list_tmp

        for _obj in obj_list:
            _phone = _obj.phone.strip()
            if not _phone:
                logger.warning(f'缺少实例化必要参数 -> 【BusinessPhonesAll】')
                continue
            if len(_phone) < 5:
                continue
            p_obj: BusinessPhonesAll = BusinessPhonesAll(
                source_name=public_main_obj.source_name, bus_id=bus_id, bus_type=bus_type,
                phone=_phone
            )
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp

    def create_social_bus(self, public_main_obj: Union[Company, Human], obj_list: List[PreprocessSocial],
                          bus_type: int) -> List[BusinessSocialsAll]:
        """
        创建业务社媒对象
        :param public_main_obj: 公共主模型
        :param obj_list: 要处理的预处理模型
        :param bus_type: 1-公司；2-人物
        :return:
        """
        p_obj_list_tmp: List[BusinessSocialsAll] = []
        if bus_type == 1:
            bus_id = public_main_obj.pid
        elif bus_type == 2:
            bus_id = public_main_obj.hid
        else:
            logger.warning(f'无效的 bus_type -> {bus_type}')
            return p_obj_list_tmp

        for _obj in obj_list:
            _social_url = _obj.social_url.strip()
            _social_type = _obj.social_type
            _domain = _obj.domain
            if not _social_url:
                logger.warning(f'缺少实例化必要参数 -> 【BusinessSocialsAll】')
                continue
            p_obj: BusinessSocialsAll = BusinessSocialsAll(
                source_name=public_main_obj.source_name, bus_id=bus_id, bus_type=bus_type,
                social_url=_social_url, social_type=_social_type, domain=_domain,
            )
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp

    def create_website_bus(self, public_main_obj: Union[Company, Human], obj_list: List[PreprocessWebsite],
                           bus_type: int) -> List[BusinessWebsitesAll]:
        """
        创建业务网址对象
        :param public_main_obj: 公共主模型
        :param obj_list: 要处理的预处理模型
        :param bus_type: 1-公司；2-人物
        :return:
        """
        p_obj_list_tmp: List[BusinessWebsitesAll] = []
        if bus_type == 1:
            bus_id = public_main_obj.pid
        elif bus_type == 2:
            bus_id = public_main_obj.hid
        else:
            logger.warning(f'无效的 bus_type -> {bus_type}')
            return p_obj_list_tmp

        for _obj in obj_list:
            _website = _obj.website.strip()
            _domain = _obj.domain
            if not _website:
                logger.warning(f'缺少实例化必要参数 -> 【BusinessWebsitesAll】')
                continue
            if 'twitter.com' in _website or 'facebook.com' in _website or 'youtube.com' in _website or 'linkedin.com' in _website or 'instagram.com' in _website:
                continue
            p_obj: BusinessWebsitesAll = BusinessWebsitesAll(
                source_name=public_main_obj.source_name, bus_id=bus_id, bus_type=bus_type,
                website=_website, domain=_domain
            )
            p_obj_list_tmp.append(p_obj)
        return p_obj_list_tmp
