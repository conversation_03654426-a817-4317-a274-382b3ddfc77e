#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public_clean_main.py
@Date: 2024/1/3
@Desc: 公共清洗服务
@Server: 
"""
from common.database import MysqlConnectionManager
from .public_clean_company import PublicFuncCleanCompanyMain
from .public_clean_customs import PublicFuncCleanCustomsMain
from .public_clean_human import PublicFuncCleanHumanMain
from typing import Optional, Union, List, Tuple
from models.preprocess_model import *
from models.public_clean_model import *


class PublicCleanMain:
    def __init__(self, mysql_manager: MysqlConnectionManager):
        self.mysql_manager = mysql_manager

    def main_clean_company(self,
                           preprocess_company_main_obj_list: List[PreprocessCompanyMain]
                           ) -> List[PublicCleanCompanyMain]:
        """
        清洗 公司数据 执行入口
        :param preprocess_company_main_obj_list: 预处理模型数据 列表
        :return: 
        """
        public_clean_company_main_obj_list = (
            PublicFuncCleanCompanyMain(self.mysql_manager).main_clean_company(preprocess_company_main_obj_list))
        return public_clean_company_main_obj_list

    def main_clean_human(self,
                         preprocess_people_main_obj_list: List[PreprocessPeopleMain],
                         company_bus_id_map_obj_list: Union[List[BusIdMapCompany], None] = None
                         ) -> Tuple[List[PublicCleanHumanMain], List[PublicCleanSchoolMain]]:
        """
        清洗 人物数据 执行入口
        :param preprocess_people_main_obj_list: 预处理模型数据 列表
        :param company_bus_id_map_obj_list: 公司 新旧 ID 映射
        :return:
        """
        public_clean_human_main_obj_list, public_clean_school_main_obj_list = (
            PublicFuncCleanHumanMain(self.mysql_manager).main_clean_human(
                preprocess_people_main_obj_list=preprocess_people_main_obj_list,
                company_bus_id_map_obj_list=company_bus_id_map_obj_list
            ))
        return public_clean_human_main_obj_list, public_clean_school_main_obj_list

    def main_clean_customs_trade(self,
                                 preprocess_customs_trade_main_obj_list: List[PreprocessCustomsTradeMain]
                                 ) -> Optional[List[PublicCleanCustomsTradeMain]]:
        """
        清洗 海关贸易数据 执行入口
        :param preprocess_customs_trade_main_obj_list: 预处理模型数据 列表
        :return:
        """
        public_func_clean_customs_main_obj = PublicFuncCleanCustomsMain(self.mysql_manager)
        return public_func_clean_customs_main_obj.main_clean_customs_trade(preprocess_customs_trade_main_obj_list)

    def main(self):
        pass

    def test_unit(self):
        """
        单元测试
        :return:
        """
        pass


if __name__ == '__main__':
    from common.database import MysqlConnectionManager
    from models import ConfMySQL

    conf_list = [ConfMySQL(name='company_gz', max_connections=1)]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    public_clean_main_obj = PublicCleanMain(mysql_manager)
    public_clean_main_obj.test_unit()
    pass
