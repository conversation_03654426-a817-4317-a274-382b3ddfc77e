#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: tb_trademark.py
@Date: 2025-01-28
@Desc: 商标基础信息表操作类
@Server: 
"""
from typing import List, Sequence, Dict, Any
from curd.tb_base import TableBase


class TableTrademark(TableBase):
    db = 'db_trademark_staging'
    table = 'trademark'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_exist_by_t_id(self, t_id_list: List[str]) -> Dict[str, Any]:
        """
        校验商标是否存在
        :param t_id_list: 商标ID列表
        :return: 存在的商标ID映射
        """
        if not t_id_list:
            return {}
        
        t_id_list = list(set(t_id_list))
        placeholders = ','.join(['%s'] * len(t_id_list))
        sql = f'''
            SELECT t_id, id FROM {self.db}.{self.table} 
            WHERE t_id IN ({placeholders})
        '''
        data_src = self.db_mysql.read(sql, t_id_list, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        t_id_map = {}
        for result in results:
            t_id_map[result['t_id']] = result['id']
        return t_id_map

    def get_trademark_info_by_source(self, source_type: int, source_id_list: List[str]) -> Dict[str, Any]:
        """
        根据数据源信息获取商标信息
        :param source_type: 数据源类型
        :param source_id_list: 源ID列表
        :return: 商标信息映射
        """
        if not source_id_list:
            return {}
        
        source_id_list = list(set(source_id_list))
        placeholders = ','.join(['%s'] * len(source_id_list))
        sql = f'''
            SELECT source_id, t_id, trademark_name, filing_country 
            FROM {self.db}.{self.table} 
            WHERE source_type = %s AND source_id IN ({placeholders})
        '''
        params = [source_type] + source_id_list
        data_src = self.db_mysql.read(sql, params, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        source_map = {}
        for result in results:
            source_map[result['source_id']] = result
        return source_map

    def batch_insert_trademarks(self, trademark_data_list: List[dict]) -> bool:
        """
        批量插入商标数据
        :param trademark_data_list: 商标数据列表
        :return: 是否成功
        """
        if not trademark_data_list:
            return True
        
        try:
            result = self.db_mysql.save(
                table=f"{self.db}.{self.table}",
                items=trademark_data_list,
                ignore=True
            )
            return result is not None
        except Exception as e:
            print(f"批量插入商标数据失败: {e}")
            return False

    def update_trademark_by_t_id(self, t_id: str, update_data: dict) -> bool:
        """
        根据商标ID更新商标信息
        :param t_id: 商标ID
        :param update_data: 更新数据
        :return: 是否成功
        """
        if not t_id or not update_data:
            return False
        
        try:
            set_clause = ', '.join([f"{k} = %s" for k in update_data.keys()])
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET {set_clause} 
                WHERE t_id = %s
            '''
            params = list(update_data.values()) + [t_id]
            result = self.db_mysql.execute(sql, params)
            return result.affected_rows > 0
        except Exception as e:
            print(f"更新商标信息失败: {e}")
            return False

    def get_trademarks_by_name(self, trademark_name: str, limit: int = 100) -> List[dict]:
        """
        根据商标名称查找商标
        :param trademark_name: 商标名称
        :param limit: 限制数量
        :return: 商标列表
        """
        if not trademark_name:
            return []
        
        sql = f'''
            SELECT t_id, trademark_name, filing_country, status, application_date
            FROM {self.db}.{self.table} 
            WHERE trademark_name LIKE %s
            ORDER BY application_date DESC
            LIMIT %s
        '''
        data_src = self.db_mysql.read(sql, [f'%{trademark_name}%', limit], return_dict=True)
        results: Sequence[dict] = data_src.data
        return list(results)
