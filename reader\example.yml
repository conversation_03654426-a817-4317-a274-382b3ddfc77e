name: example  # 任务名字，全局唯一，一旦设定，不要修改，命名前查询一下record总表，必选
source: example  # 业务名字，可以重复，一个业务可以有多个任务，必选
#method: single

conn_inner: true  # 是否内网访问，true：内网，false：外网

mysql: # 数据源mysql的基本配置信息，配置错误会报错
  name: customs_gz  # 配置的名字，详见settings.config.yml，必选
  db: src_wmb  # 数据库，必填
  max_connections: 1  # 最大连接数，可选


mysql_other: # 其他中间表的配置信息，配置错误会被过滤掉，计划报废
  - name: mysql1
    db: db1
    max_connections: 1 # 最大连接数，可选
  - name: mysql2
    db: db2
    max_connections: 1 # 最大连接数，可选

query:
  # 查询的sql语句，优先级最高，可以为空，但不能写错
  sql: select id,company_id,amount from trade where aid between 99664901 and *********
  # 下面三个字段是当没有sql时才有用
  table:  # ，可选
  select:  # ，可选
  where:  # ，可选

split: # 拆分数据的逻辑 - 当前支持 自增id 和 datetime
  type: id  # id:1 or time:2
  field: # 如果type是time，下面两个都要写，如果type是id，下面可以只填id
    id: id  # 自增id字段
    time: update_time  # 时间字段
  batch_size: 100  # 单进程任务分片大小，<=split_size

preprocess: example  # 预处理方法名，必选

#process: # 计划报废
#  - company
#  - trade

worker: 1  # 多进程并行量
