#!/usr/bin/env python3
# encoding: utf8

"""
Module Name: db_mysql.py
Description: 高效操作数据库
Author: Peng
Email: <EMAIL>
Date: 2024-01-04
"""

import pymysql
from pymysql.cursors import Cursor, DictCursor
from typing import Union, List, Optional, Sequence, Any, Dict
from dbutils.pooled_db import PooledDB
from models import MysqlResult, ConfMySQL
from exceptions import MysqlConfNotFoundError
from settings import *


class MysqlConnectionManager:
    """连接池管理器"""

    def __init__(self, conf_list: List[ConfMySQL]):
        self._conf_list = conf_list
        self.connections = {}

    def init(self):
        for conf in self._conf_list:
            mysql_info = CONF_GLOBAL.mysql.get(conf.name)
            if mysql_info is None:
                raise MysqlConfNotFoundError
            # mysql_info['db'] = conf.db
            self.connections[conf.name] = DBPoolMysql(mysql_info.__dict__, max_connections=conf.max_connections)

    def get_pool(self, name):
        return self.connections.get(name)

    def close(self):
        for conn in self.connections.values():
            conn.close()


class DBPoolMysql:
    """连接池操作mysql的封装"""

    def __init__(self, pool: Union[PooledDB, dict], max_connections: int = 1):
        """初始化连接 - 根据连接池实例或配置"""
        if isinstance(pool, PooledDB):
            self.pool = pool
        elif isinstance(pool, dict):
            self.pool = PoolMysql(max_connections=max_connections, **pool)
        else:
            raise ValueError('mysql连接参数错误')

    def execute(self, sql: str, value: Optional[Sequence] = None, return_dict: bool = False, rollback: bool = False,
                retry_num: int = 3) -> MysqlResult:
        """
        执行sql
        :param sql: 语句
        :param value: 需要format到sql的值，默认None
        :param return_dict: select时返回数据的格式，默认元组，可选字典
        :param rollback: 有数据提交时的异常回滚，默认不回滚
        :param retry_num: 重试次数，默认3
        :return: MysqlResult
        """
        # result = MysqlResult(sql)
        # sql类型
        sql_type = sql.strip().lower().split(' ')[0]
        # 返回数据格式
        cursor_class = DictCursor if return_dict and sql_type == 'select' else Cursor
        for _ in range(retry_num):
            conn = self.pool.connection()
            cursor = conn.cursor(cursor_class)
            logging.debug(sql)
            try:
                cursor.execute(sql, value)
                # 根据不同类型的sql返回不同的数据结果
                if sql_type in {"select", "show"}:
                    r = cursor.fetchall()
                    # 整体为None或空的情况
                    if r is None or len(r) == 0:
                        data = tuple()
                    # 里面的值是空或者为None的情况
                    elif (isinstance(r[0], dict) and len(r[0]) == 0) or (isinstance(r[0], tuple) and r[0][0] is None):
                        data = tuple()
                    else:
                        data = r
                    return MysqlResult(sql=sql, data=data)
                # 提交数据
                conn.commit()
                if sql_type in {"insert", "update", "delete"}:  # 返回影响的行数
                    return MysqlResult(sql=sql, lastrowid=cursor.lastrowid, rowcount=cursor.rowcount)
                else:
                    return MysqlResult(sql=sql)
            except Exception as e:
                if rollback and sql_type in {"insert", "update", "delete"}:
                    conn.rollback()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logging.warning(f"{exc_type.__name__}: {e}")
            finally:
                cursor.close()
                conn.close()  # 将连接归还给连接池
        logging.error({"msg": "最大重试次数", "sql": sql})

        return MysqlResult(status=False, sql=sql)

    def execute_many(self, sql: str, values: Sequence[Sequence[Any]], retry_num: int = 3) -> MysqlResult:
        """
        批量写或更新
        :param sql: 语句
        :param values: 嵌套的数据列表或元组
        :param retry_num: 重试次数，默认3
        :return: int or None
        """
        for _ in range(retry_num):
            conn = self.pool.connection()
            cursor = conn.cursor()
            try:
                cursor.executemany(sql, values)
                conn.commit()
                return MysqlResult(sql=sql, rowcount=cursor.rowcount)
            except Exception as e:
                conn.rollback()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logging.warning(f"{exc_type.__name__}: {e}")
            finally:
                cursor.close()
                conn.close()  # 将连接归还给连接池
        logging.error({"msg": "最大重试次数", "sql": sql})
        return MysqlResult(status=False, sql=sql)

    def read(self, sql: str, value: Optional[Sequence] = None, return_dict: bool = False,
             retry_num: int = 3) -> MysqlResult:
        """
        读取数据
        :param sql: 语句
        :param value: 拼接到语句的值，默认None
        :param retry_num: 重试次数，默认3
        :param return_dict: 返回每条数据格式，默认元组
        :return: tuple or None
        """
        return self.execute(sql=sql, value=value, return_dict=return_dict, retry_num=retry_num)

    def write(self, sql: str, values: Union[Sequence[Sequence[Any]], Sequence[Any]],
              retry_num: int = 3) -> Union[MysqlResult, None]:
        """
        写数据 - 根据sql和数据内容拼接
        :param sql: 语句
        :param values: 嵌套的数据列表,元组 或 单条数据
        :param retry_num: 重试次数，默认3
        :return: int or None
        """
        if len(values) == 0:
            logging.warning("values is empty.")
            return
        elif isinstance(values[0], Sequence):
            return self.execute_many(sql=sql, values=values, retry_num=retry_num)
        else:
            return self.execute(sql=sql, value=values, retry_num=retry_num)

    def read_column_default(self, table: str, db: Optional[str] = None):
        """查询表的字段默认值"""
        # 校验db和table，sql中的db.table优先级比较高
        if isinstance(db, str) and '.' not in table:
            table = f"{db}.{table}"
        if '.' not in table:
            raise ValueError("Please enter 'db' and 'table'.")
        db, table = table.split('.')
        sql = (f"select COLUMN_NAME, COLUMN_DEFAULT from INFORMATION_SCHEMA.COLUMNS "
               f"where TABLE_SCHEMA={db!r} and TABLE_NAME={table!r}")
        result = self.read(sql, return_dict=True)
        if result.length > 0:
            return {data['COLUMN_NAME']: data['COLUMN_DEFAULT'] for data in result.data}

    def save(self, table: str, items: Union[Sequence[dict], dict], db: Optional[str] = None,
             ignore: bool = False, field_defaults: Optional[dict] = None) -> Union[MysqlResult, None]:
        """
        写数据 - 根据table和数据字典动态生成sql
        :param table: 表名
        :param items: 嵌套的数据列表,元组 或 单条数据字典
        :param db: 数据库名字，可选，也可以在table参数中使用db.table的格式
        :param ignore: insert ignore into or insert into ... on duplicate key update ...
        :param field_defaults: 字段默认值，用于安全的更新
        :return: int or None
        """
        # logging.info({"len": len(items), "pre": json.dumps(items)[:50]})
        if len(items) == 0:
            logging.warning("items is empty.")
            return
        # 校验db和table，sql中的db.table优先级比较高
        if isinstance(db, str) and '.' not in table:
            table = f"{db}.{table}"
        # 确认字段
        fields = items[0].keys() if isinstance(items, Sequence) else items.keys()
        # 生成sql
        insert_str = ', '.join(fields)
        # 跳过冲突数据
        if ignore:
            sql = f"insert ignore into {table}({insert_str}) value({', '.join(['%s'] * len(fields))})"
        # 冲突数据安全的更新
        elif field_defaults is not None:
            update_list = list()
            for field in fields:
                field_default = field_defaults.get(field, '')
                # 默认值为None的情况
                if field_default is None:
                    # is_artificial 跳过人工校验
                    sql_cur = (f"{field}=if(is_artificial=1 or values({field}) is null or "
                               f"values({field}) in ('', 0), {field}, values({field}))")
                    update_list.append(sql_cur)
                # 默认值是整形或浮点数的情况（mysql默认值数字是字符串格式）
                elif field_default.replace('.', '').isdigit():
                    sql_cur = (f"{field}=if(is_artificial=1 or "
                               f"values({field})={field_default}, {field}, values({field}))")
                    update_list.append(sql_cur)
                # 默认值是字符串的情况
                else:
                    sql_cur = (f"{field}=if(is_artificial=1 or "
                               f"values({field})={field_default!r}, {field}, values({field}))")
                    update_list.append(sql_cur)
            update_str = ','.join(update_list)
            sql = (f"insert into {table}({insert_str}) value({', '.join(['%s'] * len(fields))}) "
                   f"on duplicate key update {update_str}")
        # 冲突数据强制更新
        else:
            update_str = ','.join([f'{field}=values({field})' for field in fields])
            sql = (f"insert into {table}({insert_str}) value({', '.join(['%s'] * len(fields))}) "
                   f"on duplicate key update {update_str}")
        # 单条或批量写入
        if isinstance(items, Sequence):
            return self.execute_many(sql, [list(item.values()) for item in items])
        else:
            return self.execute(sql, list(items.values()))

    def read_column_default_new(self, table: str, db: Optional[str] = None):
        """查询表的字段默认值与类型"""
        # 校验db和table，sql中的db.table优先级比较高
        if isinstance(db, str) and '.' not in table:
            table = f"{db}.{table}"
        if '.' not in table:
            raise ValueError("Please enter 'db' and 'table'.")
        db, table = table.split('.')
        sql = (f"select COLUMN_NAME, COLUMN_DEFAULT, DATA_TYPE from INFORMATION_SCHEMA.COLUMNS "
               f"where TABLE_SCHEMA={db!r} and TABLE_NAME={table!r}")
        result = self.execute(sql, return_dict=True)
        if result.length > 0:
            return {data['COLUMN_NAME']: {'COLUMN_DEFAULT': data['COLUMN_DEFAULT'], 'DATA_TYPE': data['DATA_TYPE']} for
                    data in result.data}

    def _generate_update_statements(self, fields: list, field_schema: Dict[str, Dict]) -> list:
        """
        生成更新语句部分的列表，根据字段类型和默认值决定如何处理更新逻辑。

        :param fields: 表的字段名列表
        :param field_schema: 字段描述的字典，包含字段的默认值和数据类型 {
            'id': {'COLUMN_DEFAULT': None, 'DATA_TYPE': 'bigint'},
            'v1': {'COLUMN_DEFAULT': None, 'DATA_TYPE': 'varchar'}
        }
        :return: 更新语句部分的列表
        """
        update_statements = []
        has_artificial = False
        if 'is_artificial' in field_schema:
            has_artificial = True

        for field in fields:
            if field not in field_schema:
                continue

            schema = field_schema[field]
            default_value = schema.get('COLUMN_DEFAULT')
            data_type = schema.get('DATA_TYPE')

            if data_type in ('varchar', 'text', 'char', 'binary'):
                # 字符串类型字段
                if default_value is None:
                    # 如果默认值是 None，空字符和 None 不更新
                    if has_artificial:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) IS NULL OR VALUES({field}) = '' OR is_artificial != 0, {field}, VALUES({field}))"
                        )
                    else:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) IS NULL OR VALUES({field}) = '', {field}, VALUES({field}))"
                        )
                else:
                    # 有默认值，考虑默认值是否为字符串
                    if has_artificial:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) = {default_value!r} OR is_artificial != 0, {field}, VALUES({field}))"
                        )
                    else:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) = {default_value!r}, {field}, VALUES({field}))"
                        )

            elif data_type in ('int', 'bigint', 'float', 'double', 'decimal'):
                # 数值类型字段
                if default_value is None:
                    # 如果默认值是 None，0 和 None 不更新
                    if has_artificial:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) IS NULL OR VALUES({field}) = 0 OR is_artificial != 0, {field}, VALUES({field}))"
                        )
                    else:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) IS NULL OR VALUES({field}) = 0, {field}, VALUES({field}))"
                        )
                else:
                    # 有默认值，考虑默认值是否为数值
                    if isinstance(default_value, (int, float)):
                        if has_artificial:
                            update_statement = (
                                f"{field}=IF(VALUES({field}) = {default_value} OR is_artificial != 0, {field}, VALUES({field}))"
                            )
                        else:
                            update_statement = (
                                f"{field}=IF(VALUES({field}) = {default_value}, {field}, VALUES({field}))"
                            )
                    else:
                        if has_artificial:
                            update_statement = (
                                f"{field}=IF(VALUES({field}) = {default_value!r} OR is_artificial != 0, {field}, VALUES({field}))"
                            )
                        else:
                            update_statement = (
                                f"{field}=IF(VALUES({field}) = {default_value!r}, {field}, VALUES({field}))"
                            )

            elif data_type in ('date', 'datetime', 'timestamp'):
                # 日期和时间类型字段
                if default_value is None:
                    # 如果默认值是 None，空值和 None 不更新
                    if has_artificial:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) IS NULL OR VALUES({field}) = '' OR is_artificial != 0, {field}, VALUES({field}))"
                        )
                    else:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) IS NULL OR VALUES({field}) = '', {field}, VALUES({field}))"
                        )
                else:
                    # 有默认值，考虑默认值是否为字符串（日期格式）
                    if has_artificial:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) = {default_value!r} OR is_artificial != 0, {field}, VALUES({field}))"
                        )
                    else:
                        update_statement = (
                            f"{field}=IF(VALUES({field}) = {default_value!r}, {field}, VALUES({field}))"
                        )
            else:
                # 其他数据类型（例如布尔型等）
                if default_value is None:
                    update_statement = (
                        f"{field}=IF(VALUES({field}) IS NULL OR VALUES({field}) = '' OR is_artificial = 1, {field}, VALUES({field}))"
                    )
                else:
                    update_statement = (
                        f"{field}=IF(VALUES({field}) = {default_value!r} OR is_artificial = 1, {field}, VALUES({field}))"
                    )

            update_statements.append(update_statement)

        return update_statements

    def save_new(self, table: str, items: Union[Sequence[dict], dict], db: Optional[str] = None,
             ignore: bool = False, field_schema: Optional[dict] = None) -> Union[MysqlResult, None]:
        """
        写数据 - 根据table和数据字典动态生成sql
        :param table: 表名
        :param items: 嵌套的数据列表,元组 或 单条数据字典
        :param db: 数据库名字，可选，也可以在table参数中使用db.table的格式
        :param ignore: insert ignore into or insert into ... on duplicate key update ...
        :param field_schema: 字段情况，默认值、类型，用于安全的更新
        :return: int or None
        """
        if len(items) == 0:
            logging.warning("items is empty.")
            return

        if isinstance(db, str) and '.' not in table:
            table = f"{db}.{table}"

        fields = items[0].keys() if isinstance(items, Sequence) else items.keys()
        insert_str = ', '.join(fields)

        if ignore:
            sql = f"INSERT IGNORE INTO {table}({insert_str}) VALUES({', '.join(['%s'] * len(fields))})"
        elif field_schema is not None:
            update_list = self._generate_update_statements(fields, field_schema)
            update_str = ', '.join(update_list)
            sql = (f"INSERT INTO {table}({insert_str}) VALUES({', '.join(['%s'] * len(fields))}) "
                   f"ON DUPLICATE KEY UPDATE {update_str}")
        else:
            update_str = ','.join([f'{field}=VALUES({field})' for field in fields])
            sql = (f"INSERT INTO {table}({insert_str}) VALUES({', '.join(['%s'] * len(fields))}) "
                   f"ON DUPLICATE KEY UPDATE {update_str}")

        if isinstance(items, Sequence):
            return self.execute_many(sql, [list(item.values()) for item in items])
        else:
            return self.execute(sql, list(items.values()))

    def read_id_range(self, table: str, id_name='aid', where: Optional[str] = None) -> tuple:
        """读取字段的最大最小值，自增id"""
        sql = f"select min({id_name}), max({id_name}) from {table}"
        if where is not None:
            sql += f" where {where}"
        result: MysqlResult = self.read(sql)
        return (0, 0) if result.length == 0 else result.data[0]

    @staticmethod
    def id_iterator(
            min_id: int, max_id: int, batch_size: Optional[int] = None, workers: Optional[int] = None) -> tuple:
        step = batch_size if workers is None else (max_id - min_id) // workers
        yield from ((i, min(i + step - 1, max_id)) for i in range(min_id, max_id + 1, step))

    @staticmethod
    def id_iterator_matrix(
            min_id: int, max_id: int, batch_size: int, workers: int = 1, asc: bool = True) -> List[List[int]]:
        """任务分割 - 矩阵模式"""
        # 参数验证
        if min_id < 1 or max_id < 1 or batch_size < 1 or workers < 1 or min_id > max_id:
            yield []
            return
        # 任务块总数((max_id - min_id + 1) % batch_size > 0)为不被整除的部分是否需要一个单独的列表
        total_batches = (max_id - min_id + 1) // batch_size + ((max_id - min_id + 1) % batch_size > 0)
        # 直接按需生成和分配批次
        for worker in range(workers):
            worker_batches = []
            # 按进程数计算每个进程所有分片数
            for batch_num in range(worker, total_batches, workers):
                start_id = min_id + batch_num * batch_size
                end_id = min(start_id + batch_size - 1, max_id)
                if start_id <= max_id:
                    worker_batches.append([start_id, end_id])
            if worker_batches:
                yield worker_batches if asc else worker_batches[::-1]

    def read_min_max_value(self, table: str, column: str = 'id', where: Optional[str] = None,
                           column_type: str = 'int', total: bool = False) -> MysqlResult:
        """读取字段的最大最小值，不限类型. """
        fields = list()
        if column_type == 'time':
            fields.append(f'DATE_FORMAT(min(`{column}`),"%Y-%m-%d %H:%i:%s")')
            fields.append(f'DATE_FORMAT(max(`{column}`),"%Y-%m-%d %H:%i:%s")')
        else:
            fields.append(f'min({column})')
            fields.append(f'max({column})')
        if total:
            fields.append('count(*)')
        sql = f"select {','.join(fields)} from {table}"
        if where is not None:
            sql += f" where {where}"
        return self.read(sql)

    def test_connect(self):
        sql = "show databases;"
        logging.info(self.read(sql))

    def close(self):
        self.pool.close()


class PoolMysql:
    """构建连接池"""

    def __new__(cls, max_connections: int = 1, **mysql_info) -> PooledDB:
        min_cached = int(max_connections * 0.2)  # 例如，最大连接数的20%
        max_cached = int(max_connections * 0.5)  # 例如，最大连接数的40%

        # 确保 min_cached 和 max_cached 至少为1
        min_cached = max(1, min_cached)
        max_cached = max(min_cached, max_cached)  # max_cached 不应小于 min_cached

        info = dict(
            blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待
            ping=1,  # 在每次获取连接时ping
            mincached=min_cached,  # 初始化时，连接池中至少创建的空闲的连接，0表示不创建
            maxcached=max_cached,  # 连接池中最多闲置的连接，0和None不限制
            maxconnections=max_connections,  # 连接池允许的最大连接数，0和None表示不限制连接数
        )
        info.update(mysql_info)
        return PooledDB(creator=pymysql, **info)
