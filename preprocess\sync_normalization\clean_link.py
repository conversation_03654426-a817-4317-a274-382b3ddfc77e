#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-02-24 15:18:15
@Desc: 
@Server: 
"""
import arrow
import time
import traceback
import json
from settings import *
from typing import Optional, Sequence, Tuple, Union, Dict
from preprocess import PreprocessBase
from preprocess.sync_normalization.clean_funcs import preprocess_clean_funcs_obj
from models.preprocess_model import *
from models.public_clean_model import *
from common.database import MysqlConnectionManager
from common.database import DBPoolMysql
from public.public_clean_main import PublicCleanMain
from curd import TableBusinessEmailsAll
from curd import TableBusinessPhonesAll
from curd import TableBusinessSocialsAll
from curd import TableBusinessWebsitesAll
from curd import TableDimWorldLocationCountry
from curd import TableLinkCompanyLogo
from curd import TableLinkPersonLogo
from curd import TableLinkCompanyBaseInformation
from curd import TableLinkPersonList
from curd import TableLinkPersonExperience
from curd import TableLinkPersonEducation


class CleanLink(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.source_name = 'linkedin'
        self.mysql_obj_company_gz: DBPoolMysql = mysql_manager.get_pool("company_gz")
        self.public_clean_main_obj = PublicCleanMain(mysql_manager)

        self.table_business_emails_all_obj = TableBusinessEmailsAll(self.mysql_obj_company_gz)
        self.table_business_phones_all_obj = TableBusinessPhonesAll(self.mysql_obj_company_gz)
        self.table_business_socials_all_obj = TableBusinessSocialsAll(self.mysql_obj_company_gz)
        self.table_business_websites_all_obj = TableBusinessWebsitesAll(self.mysql_obj_company_gz)

        self.table_link_company_base_information_obj = TableLinkCompanyBaseInformation(self.mysql_obj_company_gz)
        self.table_link_person_list_obj = TableLinkPersonList(self.mysql_obj_company_gz)
        self.table_link_person_experience_obj = TableLinkPersonExperience(self.mysql_obj_company_gz)
        self.table_link_person_education_obj = TableLinkPersonEducation(self.mysql_obj_company_gz)
        self.table_link_company_logo_obj = TableLinkCompanyLogo(self.mysql_obj_company_gz)
        self.table_link_person_logo_obj = TableLinkPersonLogo(self.mysql_obj_company_gz)

        self.table_dim_world_location_country_obj = TableDimWorldLocationCountry(self.mysql_obj_company_gz)

        # 国家二字码与国家名称映射
        self.country_code_map = self.table_dim_world_location_country_obj.get_country_code_map()

    def clean_company_name_patch(self, company_name: str) -> str:
        """
        对 公司名进行一些 特殊处理
        """
        # cobot logistics belgium · 自雇
        # boles oilpalm plantation  · contract
        if not company_name:
            return ''
        try:
            if '·' in company_name:
                company_name_split = company_name.strip().split(' · ')
                company_name = ' · '.join(company_name_split[:-1]).strip()
        except Exception as e:
            pass
        return company_name

    def clean_human_name_patch(self, human_name: str) -> str:
        """
        对 人名进行一些 特殊处理
        """
        if not human_name:
            return ''
        try:
            human_name = human_name.lower().strip()
            human_name_sp = human_name.split(' ')
            if human_name_sp[0] in ['mr', 'mrs', 'mr.', 'mrs.']:
                human_name_sp = human_name_sp[1:]
            if human_name_sp and human_name_sp[-1] in ['n/a']:
                human_name_sp = human_name_sp[:-1]
            if not human_name_sp:
                return ''
            human_name = ' '.join(human_name_sp).strip()
        except Exception as e:
            pass
        return human_name

    def create_preprocess_experience(
            self, experience_datalist: List[dict],
            preprocess_company_main_obj_map: dict[str, PreprocessCompanyMain],
    ) -> List[PreprocessExperience]:
        """
        处理 人物 工作经历
        :param experience_datalist: 工作经历数据
        :param preprocess_company_main_obj_map: 公司预处理后的模型
        :return:
        """
        preprocess_experience_obj_list = []
        if not experience_datalist:
            return []
        for _data in experience_datalist:
            cid_source = _data['cid_source'] or ''
            hid_source = _data['hid_source'] or ''
            human_name = _data['human_name'] or ''
            human_type = _data['human_type'] or ''
            start_date_str = _data['start_date_str'] or ''
            end_date_str = _data['end_date_str'] or ''
            title_name = _data['title_name'] or ''
            title_role = _data['title_role'] or ''
            title_sub_role = _data['title_sub_role'] or ''
            summary = _data['summary'] or ''
            title_levels = _data['title_levels'] or ''
            post_status = _data['post_status'] or 0
            job_company_name = _data['job_company_name'] or ''
            job_company_location_country = _data['job_company_location_country'] or ''
            job_company_location_region = _data['job_company_location_region'] or ''
            job_company_location_locality = _data['job_company_location_locality'] or ''
            country_ios_code = _data['country_ios_code'] or ''

            job_company_name = self.clean_company_name_patch(job_company_name)

            preprocess_company_main_obj: PreprocessCompanyMain = preprocess_company_main_obj_map.get(cid_source, None)
            company_name = preprocess_company_main_obj.company_name if preprocess_company_main_obj else job_company_name
            country = preprocess_company_main_obj.country if preprocess_company_main_obj else job_company_location_country
            country_code = preprocess_company_main_obj.country_code if preprocess_company_main_obj else country_ios_code
            province = preprocess_company_main_obj.province if preprocess_company_main_obj else job_company_location_region
            city = preprocess_company_main_obj.city if preprocess_company_main_obj else job_company_location_locality
            start_date = int(arrow.get(start_date_str, 'YYYY-MM-DD').timestamp()) if start_date_str else 0
            end_date = int(arrow.get(end_date_str, 'YYYY-MM-DD').timestamp()) if end_date_str else 0

            try:
                title_levels = eval(title_levels) if title_levels else []
                title_levels = list(set([_d.lower().strip() for _d in title_levels]))
            except Exception as e:
                title_levels = []
            title_levels = json.dumps(title_levels, ensure_ascii=False) if title_levels else ''

            preprocess_experience_obj = PreprocessExperience(
                company_name=company_name,
                country=country,
                country_code=country_code,
                province=province,
                city=city,
                bus_id_old=cid_source,
                title_name=title_name,
                title_role=title_role,
                title_sub_role=title_sub_role,
                title_levels=title_levels,
                start_date=start_date,
                end_date=end_date,
                post_status=post_status,
                summary=summary,
            )
            preprocess_experience_obj_list.append(preprocess_experience_obj)
        return preprocess_experience_obj_list

    def create_preprocess_education(
            self, education_datalist: List[dict]
    ) -> List[PreprocessEducation]:
        """
        处理 人物 教育经历
        :param education_datalist: 教育 经历数据
        :return:
        """
        preprocess_education_obj_list = []
        if not education_datalist:
            return []
        for education in education_datalist:
            hid_source = education['hid_source'] or ''
            sid_source = education['sid_source'] or ''
            human_name = education['human_name'] or ''
            school_name = education['school_name'] or ''
            school_type = education['school_type'] or ''
            school_country_code = education['school_country_code'] or ''
            school_country = education['school_country'] or ''
            school_province = education['school_province'] or ''
            school_city = education['school_city'] or ''
            start_date_str = education['start_date_str'] or ''
            end_date_str = education['end_date_str'] or ''
            website = education['website'] or ''
            linkedin_url = education['linkedin_url'] or ''
            facebook_url = education['facebook_url'] or ''
            twitter_url = education['twitter_url'] or ''
            degrees = education['degrees'] or ''
            majors = education['majors'] or ''
            minors = education['minors'] or ''
            gpa = education['gpa'] or ''
            summary = education['summary'] or ''

            start_date = int(arrow.get(start_date_str, 'YYYY-MM-DD').timestamp()) if start_date_str else 0
            end_date = int(arrow.get(end_date_str, 'YYYY-MM-DD').timestamp()) if end_date_str else 0

            try:
                degrees = eval(degrees) if degrees else []
                degrees = list(set([_d.lower().strip() for _d in degrees]))
            except Exception as e:
                degrees = []
            degrees = json.dumps(degrees, ensure_ascii=False) if degrees else ''

            try:
                majors = eval(majors) if majors else []
                majors = list(set([_d.lower().strip() for _d in majors]))
            except Exception as e:
                majors = []
            majors = json.dumps(majors, ensure_ascii=False) if majors else ''

            try:
                minors = eval(minors) if minors else []
                minors = list(set([_d.lower().strip() for _d in minors]))
            except Exception as e:
                minors = []
            minors = json.dumps(minors, ensure_ascii=False) if minors else ''

            # 联系方式
            social_obj_list = []
            website_obj_list = []
            if linkedin_url:
                social_obj_list.append(
                    PreprocessSocial(
                        social_url=linkedin_url,
                        social_type='linkedin',
                        source_name=self.source_name
                    )
                )
            if twitter_url:
                social_obj_list.append(
                    PreprocessSocial(
                        social_url=twitter_url,
                        social_type='twitter',
                        source_name=self.source_name
                    )
                )
            if facebook_url:
                social_obj_list.append(
                    PreprocessSocial(
                        social_url=facebook_url,
                        social_type='facebook',
                        source_name=self.source_name
                    )
                )
            if website:
                website_obj_list.append(
                    PreprocessWebsite(
                        website=website,
                        source_name=self.source_name
                    )
                )
            concat_obj = PreprocessConcatMain(
                social_obj_list=social_obj_list,
                website_obj_list=website_obj_list
            )

            preprocess_education_obj = PreprocessEducation(
                school_name=school_name,
                bus_id_old=sid_source,
                school_type=school_type,
                country=school_country,
                country_code=school_country_code,
                province=school_province,
                city=school_city,
                start_date=start_date,
                end_date=end_date,
                degrees=degrees,
                majors=majors,
                minors=minors,
                gpa=gpa,
                summary=summary,
                concat_obj=concat_obj
            )
            preprocess_education_obj_list.append(preprocess_education_obj)
        return preprocess_education_obj_list

    def deal_data_company(self, company_data: dict, **kwargs) -> Union[PreprocessCompanyMain, None]:
        """
        处理公司数据
        """
        if not company_data:
            return None
        job_company_name = company_data.get('job_company_name', '')
        if not job_company_name:
            return None
        bus_id_old = company_data.get('job_company_id', '')
        # 旧公司 ID与新公司 ID的映射
        cid_pid_map = kwargs.get('cid_pid_map', {})
        bus_id_new = cid_pid_map.get(bus_id_old, '')
        if not bus_id_new:
            return None
        job_company_size = company_data.get('job_company_size', '')
        job_company_founded = company_data.get('job_company_founded', '')
        job_company_industry = company_data.get('job_company_industry', '')
        country_ios_code = company_data.get('country_ios_code', '')
        job_company_location_name = company_data.get('job_company_location_name', '')
        job_company_location_country = company_data.get('job_company_location_country', '')
        job_company_location_region = company_data.get('job_company_location_region', '')
        job_company_location_locality = company_data.get('job_company_location_locality', '')
        job_company_location_street_address = company_data.get('job_company_location_street_address', '')
        job_company_location_address_line_2 = company_data.get('job_company_location_address_line_2', '')
        job_company_location_postal_code = company_data.get('job_company_location_postal_code', '')
        job_company_location_geo = company_data.get('job_company_location_geo', '')
        specialities = company_data.get('specialities', '')
        description = company_data.get('description', '')

        company_logo_obj_list = []
        company_address_obj_list = []
        company_industry_obj_list = []

        # 处理清洗字段
        job_company_name = self.clean_company_name_patch(job_company_name)
        country = self.country_code_map.get(country_ios_code.lower(), {}).get('country_en', '') \
            if country_ios_code else job_company_location_country
        job_company_location_region = job_company_location_region.strip() if job_company_location_region else ''
        job_company_location_locality = job_company_location_locality.strip() if job_company_location_locality else ''
        job_company_location_street_address = job_company_location_street_address.strip() if job_company_location_street_address else ''
        job_company_location_address_line_2 = job_company_location_address_line_2.strip() if job_company_location_address_line_2 else ''
        job_company_location_postal_code = job_company_location_postal_code.strip() if job_company_location_postal_code else ''

        inc_date = preprocess_clean_funcs_obj.date_str_to_timestamp(job_company_founded, date_format='%Y')

        latitude, longitude = '', ''
        if job_company_location_geo:
            latitude_longitude = job_company_location_geo.split(',')
            if len(latitude_longitude) == 2:
                latitude = latitude_longitude[0].strip()
                longitude = latitude_longitude[1].strip()
        try:
            company_size = int(job_company_size.split('-')[0]) if job_company_size else 0
        except ValueError:
            company_size = 0

        # logo
        company_logo_data = kwargs.get('company_logo_data', {})
        company_logo_datalist = company_logo_data.get(bus_id_old, [])
        for _data in company_logo_datalist:
            logo_url = _data.get('logo_url', '')
            logo_path = _data.get('logo_path', '')
            if logo_url or logo_path:
                company_logo_obj = PreprocessLogo(
                    logo_url=logo_url,
                    logo_url_local=logo_path
                )
                company_logo_obj_list.append(company_logo_obj)

        address = preprocess_clean_funcs_obj.format_address(
            country_name=country,
            state_name=job_company_location_region,
            city_name=job_company_location_locality,
            street1=job_company_location_street_address,
            street2=job_company_location_address_line_2,
            postal_code=job_company_location_postal_code,
        )
        if address:
            company_address_obj = PreprocessAddress(
                address=address,
                postal_code=job_company_location_postal_code,
                country_code_iso2=country_ios_code,
                country_en=country,
                province_en=job_company_location_region,
                city_en=job_company_location_locality,
            )
            company_address_obj_list.append(company_address_obj)
        if job_company_industry:
            company_industry_obj = PreprocessCompanyIndustry(
                industry=job_company_industry
            )
            company_industry_obj_list.append(company_industry_obj)

        # 联系方式
        bus_emails_data_g = kwargs.get('bus_emails_data_g', {})
        bus_phones_data_g = kwargs.get('bus_phones_data_g', {})
        bus_socials_data_g = kwargs.get('bus_socials_data_g', {})
        bus_websites_data_g = kwargs.get('bus_websites_data_g', {})
        be_datalist = bus_emails_data_g.get(bus_id_old, [])
        company_email_obj_list = preprocess_clean_funcs_obj.create_preprocess_email(be_datalist)
        bp_datalist = bus_phones_data_g.get(bus_id_old, [])
        company_phone_obj_list = preprocess_clean_funcs_obj.create_preprocess_phone(bp_datalist)
        bs_datalist = bus_socials_data_g.get(bus_id_old, [])
        company_social_obj_list = preprocess_clean_funcs_obj.create_preprocess_social(bs_datalist)
        bw_datalist = bus_websites_data_g.get(bus_id_old, [])
        company_website_obj_list = preprocess_clean_funcs_obj.create_preprocess_website(bw_datalist)

        preprocess_concat_main_obj = PreprocessConcatMain(
            email_obj_list=company_email_obj_list,
            phone_obj_list=company_phone_obj_list,
            social_obj_list=company_social_obj_list,
            website_obj_list=company_website_obj_list,
        )

        preprocess_company_main_obj: PreprocessCompanyMain = PreprocessCompanyMain(
            source_name=self.source_name,
            company_name=job_company_name,
            country=country,
            province=job_company_location_region,
            country_code=country_ios_code,
            city=job_company_location_locality,
            bus_id_old=bus_id_old,
            bus_id_new=bus_id_new,
            inc_date=inc_date,
            latitude=latitude,
            longitude=longitude,
            business_scope=specialities,
            overview=description,
            company_size=company_size,
            company_logo_obj_list=company_logo_obj_list,
            company_address_obj_list=company_address_obj_list,
            company_industry_obj_list=company_industry_obj_list,
            concat_obj=preprocess_concat_main_obj
        )
        return preprocess_company_main_obj

    def deal_data_human(self, data: dict, **kwargs) -> Union[PreprocessPeopleMain, None]:
        """
        处理 人物数据
        :param data: link人物数据
        :return: PreprocessPeopleMain
        """
        full_name = data.get('full_name', '')
        full_name = self.clean_human_name_patch(full_name)
        if not full_name:
            return None
        bus_id_old = data.get('lid', '')
        gender = data.get('gender', '')
        birth_year = data.get('birth_year', '')
        birth_date = data.get('birth_date', '')
        industry = data.get('industry', '')
        job_title = data.get('job_title', '')
        job_title_role = data.get('job_title_role', '')
        job_title_sub_role = data.get('job_title_sub_role', '')
        job_title_levels = data.get('job_title_levels', '')
        job_start_date = data.get('job_start_date', '')
        job_company_id = data.get('job_company_id', '')
        job_company_name = data.get('job_company_name', '')
        country_ios_code = data.get('country_ios_code', '')
        location_name = data.get('location_name', '')
        location_country = data.get('location_country', '')
        location_region = data.get('location_region', '')
        location_locality = data.get('location_locality', '')
        summary = data.get('summary', '')
        profiles = data.get('profiles', '')
        interests = data.get('interests', '')
        skills = data.get('skills', '')
        certifications = data.get('certifications', '')
        languages = data.get('languages', '')

        preprocess_company_main_obj_map: dict[str, PreprocessCompanyMain] = kwargs.get(
            'preprocess_company_main_obj_map', {})

        human_logo_obj_list = []
        human_address_obj_list = []

        # 处理清洗字段
        job_company_name = self.clean_company_name_patch(job_company_name)
        country = self.country_code_map.get(country_ios_code.lower(), {}).get('country_en', '') \
            if country_ios_code else location_country
        location_region = location_region.strip() if location_region else ''
        location_locality = location_locality.strip() if location_locality else ''

        if gender.lower() == 'male':
            gender = 'M'
        elif gender.lower() == 'female':
            gender = 'F'
        else:
            gender = ''

        try:
            title_levels = eval(job_title_levels) if job_title_levels else []
            title_levels = list(set([_d.lower().strip() for _d in title_levels]))
        except Exception as e:
            title_levels = []
        title_levels = json.dumps(title_levels, ensure_ascii=False) if title_levels else ''

        try:
            interests = eval(interests) if interests else []
            interests = list(set([_d.lower().strip() for _d in interests]))
        except Exception as e:
            interests = []
        interests = json.dumps(interests, ensure_ascii=False) if interests else ''

        try:
            skills = eval(skills) if skills else []
            skills = list(set([_d.lower().strip() for _d in skills]))
        except Exception as e:
            skills = []
        skills = json.dumps(skills, ensure_ascii=False) if skills else ''

        try:
            languages = eval(languages) if languages else []
            languages = list(set([_d.lower().strip() for _d in languages]))
        except Exception as e:
            languages = []
        languages = json.dumps(languages, ensure_ascii=False) if languages else ''

        try:
            certifications = eval(certifications) if certifications else []
            certifications = list(set([_d.lower().strip() for _d in certifications]))
        except Exception as e:
            certifications = []
        certifications = json.dumps(certifications, ensure_ascii=False) if certifications else ''

        if not profiles and summary:
            profiles = summary

        if job_start_date:
            job_start_date = public_funcs_obj.normalize_date(job_start_date)

        if location_name:
            human_address_obj = PreprocessAddress(
                address=location_name,
                country_code_iso2=country_ios_code,
                country_en=country,
                province_en=location_region,
                city_en=location_locality,
            )
            human_address_obj_list.append(human_address_obj)

        # logo
        human_logo_data = kwargs.get('human_logo_data', {})
        human_logo_datalist = human_logo_data.get(bus_id_old, [])
        for _data in human_logo_datalist:
            logo_url = _data.get('logo_url', '')
            logo_path = _data.get('logo_path', '')
            if logo_url or logo_path:
                human_logo_obj = PreprocessLogo(
                    logo_url=logo_url,
                    logo_url_local=logo_path
                )
                human_logo_obj_list.append(human_logo_obj)
        # 工作经历
        human_experience_data = kwargs.get('human_experience_data', {})
        human_experience_datalist = human_experience_data['human'].get(bus_id_old, [])
        human_experience_obj_list: List[PreprocessExperience] = self.create_preprocess_experience(
            experience_datalist=human_experience_datalist,
            preprocess_company_main_obj_map=preprocess_company_main_obj_map
        )
        if job_company_id in preprocess_company_main_obj_map:
            # 当前数据也算一条工作经历
            preprocess_company_main_obj: PreprocessCompanyMain = preprocess_company_main_obj_map[job_company_id]
            company_name = preprocess_company_main_obj.company_name
            country = preprocess_company_main_obj.country
            country_code = preprocess_company_main_obj.country_code
            province = preprocess_company_main_obj.province
            city = preprocess_company_main_obj.city
            start_date = int(arrow.get(job_start_date, 'YYYY-MM-DD').timestamp()) if job_start_date else 0
            end_date = 0
            human_experience_obj = PreprocessExperience(
                company_name=company_name,
                country=country,
                country_code=country_code,
                province=province,
                city=city,
                bus_id_old=job_company_id,
                title_name=job_title,
                title_role=job_title_role,
                title_sub_role=job_title_sub_role,
                title_levels=title_levels,
                start_date=start_date,
                end_date=end_date,
                post_status=1,
                summary=summary,
            )
            human_experience_obj_list.append(human_experience_obj)

        # 教育经历
        human_education_data = kwargs.get('human_education_data', {})
        human_education_datalist = human_education_data.get(bus_id_old, [])
        human_education_obj_list = self.create_preprocess_education(human_education_datalist)

        # 联系方式
        bus_emails_data_h = kwargs.get('bus_emails_data_h', {})
        bus_phones_data_h = kwargs.get('bus_phones_data_h', {})
        bus_socials_data_h = kwargs.get('bus_socials_data_h', {})
        bus_websites_data_h = kwargs.get('bus_websites_data_h', {})
        be_datalist = bus_emails_data_h.get(bus_id_old, [])
        company_email_obj_list = preprocess_clean_funcs_obj.create_preprocess_email(be_datalist)
        bp_datalist = bus_phones_data_h.get(bus_id_old, [])
        company_phone_obj_list = preprocess_clean_funcs_obj.create_preprocess_phone(bp_datalist)
        bs_datalist = bus_socials_data_h.get(bus_id_old, [])
        company_social_obj_list = preprocess_clean_funcs_obj.create_preprocess_social(bs_datalist)
        bw_datalist = bus_websites_data_h.get(bus_id_old, [])
        company_website_obj_list = preprocess_clean_funcs_obj.create_preprocess_website(bw_datalist)

        preprocess_concat_main_obj = PreprocessConcatMain(
            email_obj_list=company_email_obj_list,
            phone_obj_list=company_phone_obj_list,
            social_obj_list=company_social_obj_list,
            website_obj_list=company_website_obj_list,
        )

        preprocess_people_main_obj = PreprocessPeopleMain(
            source_name=self.source_name,
            human_name=full_name,
            company_bus_id_old=job_company_id,
            human_type=1,
            country=country,
            country_code=country_ios_code,
            province=location_region,
            city=location_locality,
            bus_id_old=bus_id_old,
            gender=gender,
            birth_year=birth_year,
            birth_date=birth_date,
            industry=industry,
            interests=interests,
            skills=skills,
            profiles=profiles,
            languages=languages,
            certifications=certifications,
            human_logo_obj_list=human_logo_obj_list,
            human_address_obj_list=human_address_obj_list,
            human_education_obj_list=human_education_obj_list,
            human_experience_obj_list=human_experience_obj_list,
            concat_obj=preprocess_concat_main_obj
        )
        return preprocess_people_main_obj

    def deal_datalist(self, datalist: List[dict]) -> Tuple[
        List[PreprocessCompanyMain], List[PreprocessPeopleMain],
        List[PublicCleanCompanyMain], List[PublicCleanHumanMain],
        List[PublicCleanSchoolMain]
    ]:
        """
        处理批量数据
        :param datalist:
        :return:
        """
        (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
         public_clean_company_main_obj_list, public_clean_human_main_obj_list,
         public_clean_school_main_obj_list) = [], [], [], [], []
        logger.info(f'start preprocess deal datalist ...')
        s_t = time.time()
        hids = set()
        cid_pid_map = dict()
        for data in datalist:
            job_company_id = data.get('job_company_id', '')
            pid = data.get('pid', '')
            if not job_company_id or not pid:
                continue
            cid_pid_map[job_company_id] = pid
        cids = list(cid_pid_map.keys())

        # 获取 员工人物-详情 信息(在职)
        person_info_datalist = self.table_link_person_list_obj.get_person_info(cids=cids)
        cid_person_info_map = {}    # 公司 ID 与 员工映射
        for _data in person_info_datalist:
            person_id = _data.get('lid', '')
            job_company_id = _data.get('job_company_id', '')
            if not person_id:
                continue
            hids.add(person_id)
            if job_company_id not in cid_person_info_map:
                cid_person_info_map[job_company_id] = []
            cid_person_info_map[job_company_id].append(_data)
        hids = list(hids)

        # 获取 所有 公司 信息
        company_information_data = self.table_link_company_base_information_obj.get_company_information(cids)
        # 获取 所有 邮箱
        bus_emails_data_g = self.table_business_emails_all_obj.get_bus_emails(cids, bus_type='领英公司')
        # 获取 所有 电话
        bus_phones_data_g = self.table_business_phones_all_obj.get_bus_phones(cids, bus_type='领英公司')
        # 获取 所有 社媒
        bus_socials_data_g = self.table_business_socials_all_obj.get_bus_socials(cids, bus_type='领英公司')
        # 获取 所有 网址
        bus_websites_data_g = self.table_business_websites_all_obj.get_bus_websites(cids, bus_type='领英公司')
        # 获取 所有 头像
        company_logo_data = self.table_link_company_logo_obj.get_logo_infos(cids)

        # 获取 员工人物-工作经历 信息(仅获取映射公司的，防止未映射的脏数据污染)
        human_experience_data = self.table_link_person_experience_obj.get_person_director(cids=cids)

        batch_size = 1000
        # 获取教育经历
        human_education_data = self.table_link_person_education_obj.get_person_education(hids, batch_size=batch_size)
        # print(f'{len(human_education_data)=}')

        # 获取 所有 邮箱
        bus_emails_data_h = self.table_business_emails_all_obj.get_bus_emails(hids, bus_type='领英人物', batch_size=batch_size)
        # print(f'{len(bus_emails_data_h)=}')
        # 获取 所有 电话
        bus_phones_data_h = self.table_business_phones_all_obj.get_bus_phones(hids, bus_type='领英人物', batch_size=batch_size)
        # print(f'{len(bus_phones_data_h)=}')
        # 获取 所有 社媒
        bus_socials_data_h = self.table_business_socials_all_obj.get_bus_socials(hids, bus_type='领英人物', batch_size=batch_size)
        # print(f'{len(bus_socials_data_h)=}')
        # 获取 所有 网址
        bus_websites_data_h = self.table_business_websites_all_obj.get_bus_websites(hids, bus_type='领英人物', batch_size=batch_size)
        # print(f'{len(bus_websites_data_h)=}')
        # 获取所有头像
        human_logo_data = self.table_link_person_logo_obj.get_logo_infos(hids, batch_size=batch_size)
        # print(f'{len(human_logo_data)=}')

        # 预处理 公司信息
        preprocess_company_main_obj_map = {}
        for cid, company_data in company_information_data.items():
            preprocess_company_main_obj = self.deal_data_company(
                company_data=company_data,
                cid_pid_map=cid_pid_map,
                bus_emails_data_g=bus_emails_data_g,
                bus_phones_data_g=bus_phones_data_g,
                bus_socials_data_g=bus_socials_data_g,
                bus_websites_data_g=bus_websites_data_g,
                company_logo_data=company_logo_data,
            )
            preprocess_company_main_obj_map[cid] = preprocess_company_main_obj
            preprocess_company_main_obj_list.append(preprocess_company_main_obj)

        # 预处理 人物信息
        for data in person_info_datalist:
            preprocess_people_main_obj = self.deal_data_human(
                data,
                bus_emails_data_h=bus_emails_data_h,
                bus_phones_data_h=bus_phones_data_h,
                bus_socials_data_h=bus_socials_data_h,
                bus_websites_data_h=bus_websites_data_h,
                preprocess_company_main_obj_map=preprocess_company_main_obj_map,
                human_logo_data=human_logo_data,
                human_education_data=human_education_data,
                human_experience_data=human_experience_data,
            )
            if not preprocess_people_main_obj:
                continue
            preprocess_people_main_obj_list.append(preprocess_people_main_obj)
        logger.info(f'end preprocess deal datalist === hs: {time.time() - s_t:.2f} s')

        # ===== 执行公共清洗 =====
        # 执行公共清洗 - 公司
        public_clean_company_main_obj_list: List[PublicCleanCompanyMain] = (
            self.public_clean_main_obj.main_clean_company(preprocess_company_main_obj_list)
        )
        # 获取 公司清洗后的 新旧 ID 映射
        # 所有新旧 ID映射
        company_bus_id_map_obj_list = []
        for public_clean_company_main_obj in public_clean_company_main_obj_list:
            _bus_id_map_obj_list = public_clean_company_main_obj.bus_id_map_obj_list
            if _bus_id_map_obj_list:
                company_bus_id_map_obj_list += _bus_id_map_obj_list
        # 执行公共清洗 - 人物
        public_clean_human_main_obj_list, public_clean_school_main_obj_list = (
            self.public_clean_main_obj.main_clean_human(
                preprocess_people_main_obj_list=preprocess_people_main_obj_list,
                company_bus_id_map_obj_list=company_bus_id_map_obj_list
            ))
        return (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
                public_clean_company_main_obj_list, public_clean_human_main_obj_list,
                public_clean_school_main_obj_list)

    def test_unit(self):
        """
        单元测试
        :return:
        """
        # sql = f'''
        #     select * from db_link.linkedin_company_new_map where rate>=10 order by id limit 1
        # '''
        sql = f'''
            select * from db_link.linkedin_company_new_map where id between 151281 and 151290
        '''
        data_src = self.mysql_obj_company_gz.read(sql, return_dict=True)
        results: List[dict] = data_src.data
        (preprocess_company_main_obj_list, preprocess_people_main_obj_list,
         public_clean_company_main_obj_list, public_clean_human_main_obj_list,
         public_clean_school_main_obj_list) = self.deal_datalist(results)
        for _data in preprocess_company_main_obj_list:
            print(_data)
            print('*' * 100)
        for _data in preprocess_people_main_obj_list:
            print(_data)
            print('*' * 100)
        for _data in public_clean_company_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in public_clean_human_main_obj_list:
            print(_data)
        print('*' * 100)
        for _data in public_clean_school_main_obj_list:
            print(_data)


if __name__ == '__main__':
    from common.database import MysqlConnectionManager
    from models import ConfMySQL

    conf_list = [
        ConfMySQL(name='company_gz', max_connections=1),
        ConfMySQL(name='company_new_gz', max_connections=1),
    ]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    CleanLink(mysql_manager).test_unit()
