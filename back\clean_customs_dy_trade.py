#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import hashlib
from typing import Optional, List
from common.database import MysqlConnectionManager
from preprocess import PreprocessBase
from back.clean_customs_trade_real import CleanCustomsTrade
from settings import *
from curd import TableArea
from curd.customs_gz.db_customs.tb_haiguan import TableHaiGuan
from curd.customs_new_gz.src_wmb import company
from common.utils.mapping_currency_unit import MAPPING_CURRENCY_UNIT
import re


class CleanCustomsWmbTrade(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.mysql_obj_customs_new_gz = mysql_manager.get_pool("customs_new_gz")
        self.mysql_obj_company_gz = mysql_manager.get_pool("company_gz")
        self.table_Area_obj = TableArea(self.mysql_obj_company_gz)
        self.get_country_map = self.table_Area_obj.get_country_map()
        self.table_hg_obj = TableHaiGuan(self.mysql_obj_customs_new_gz)
        self.black_name = {"NOT AVAILABLE", "UNKNOW", "NO DISPONIBLE", "N A", "N/A"}
        self.regex_split_hs = re.compile(r'[, ]+')
        self.regex = re.compile('[^A-Z]')
        self.source = 4
        self.regex_s = re.compile(r'\s+')
        # 读取港口映射
        self.mapping_port = self.table_hg_obj.read_customs_port_map()
        # 单位映射
        self.mapping_quantity, self.mapping_weight = self.table_hg_obj.read_mapping_unit()
        self.trade_clean_obj = CleanCustomsTrade(mysql_manager)
        self.src_table_obj = company.TableCompany(self.mysql_obj_customs_new_gz)

    def create_uuid_sid(self, data):
        """生成uuid和sid"""
        data_list = [str(d).strip().lower() for d in data]
        uuid = hashlib.md5(''.join(data_list).encode()).hexdigest()
        sid = hashlib.md5(''.join(data_list[:-3]).encode()).hexdigest()
        return uuid, sid

    def transform_port(self, raw):
        new_port = raw.strip().upper()
        if new_port == '-':
            new_port = ''
        elif new_port == 'N/A':
            new_port = ''
        elif new_port.isdigit():
            new_port = ''
        if len(new_port) > 100:
            new_port = ''
        if new_port != '':
            port = self.mapping_port.get(new_port)
            if port is not None:
                new_port = port
        return new_port

    def transform_company_name(self, name: str):
        if name == "":
            return name
        name = name.strip().upper()
        for black in self.black_name:
            if name.startswith(black):
                return ""
        return name

    def transform_unit(self, raw, mapping):
        src = raw.upper().strip()
        res = self.regex.sub('', raw)
        return '' if res == '' else mapping.get(res, src)

    def transform_hs_code(self, src):
        hs_code_list = self.regex_split_hs.split(src)
        res_list = [x.replace(".", "") for x in hs_code_list if "E+" not in x.upper()]
        return ', '.join(res_list)

    def transform_currency_unit(self, raw: str):
        raw = raw.upper().strip()
        raw = self.regex_s.sub(' ', raw)
        unit = MAPPING_CURRENCY_UNIT.get(raw, raw)
        return unit

    def trade_type_map(self, tradetype):
        if tradetype == "Import" or tradetype == "0":
            return 0
        elif tradetype == "Export":  # 历史数据源没有查找到Export，为防止数据源新数据有，在此提前添加
            return 1
        else:
            return 4

    def deal_company(self, data_list) -> List:
        company_items = list()
        if len(data_list) == 0:
            return company_items
        cur_id_set = set()
        for data in data_list:
            _id, amount, amountcurrency, qty, qtyunit, weight, weightunit, ymd, exporter, \
                origincountrytag, salescountrytag, hs_code, hs_code_description, productdesc, \
                importer, importercountrytag, sourceid, tradetype, seller_port, buyer_port = data
            # 供应商
            exporter = self.transform_company_name(exporter)
            if exporter != '':
                openid = exporter
                name = exporter
                sale_info = self.get_country_map.get(salescountrytag.lower(), dict())
                country_id = sale_info.get('id', 0)
                country = sale_info.get('en', '')
                uniqueid = f"{openid}-{country_id}"  # 去重键
                # uniqueid = ''
                if uniqueid not in cur_id_set:
                    company_items.append({
                        "source": self.source,
                        "openid": str(openid),
                        "uniqueid": uniqueid,
                        "name": name,
                        "introduce": "",
                        "industry": "",
                        "scope": "",
                        "attach": "",
                        "texture": "",
                        "country_id": country_id,
                        "country": country,
                        "province": "",
                        "province_id": 0,
                        "city": "",
                        "city_id": 0,
                        "address": "",
                        "phone": "",
                        "email": "",
                        "website": ""
                    })
                    cur_id_set.add(uniqueid)
            # 采购商
            importer = self.transform_company_name(importer)
            if importer != '':
                importer = importer.upper()
                openid = importer
                name = importer
                importer_info = self.get_country_map.get(importercountrytag.lower(), dict())
                country_id = importer_info.get('id', 0)
                country = importer_info.get('en', '')
                uniqueid = f"{openid}-{country_id}"  # 去重键
                # uniqueid = ''
                if uniqueid not in cur_id_set:
                    company_items.append({
                        "source": self.source,
                        "openid": str(openid),
                        "uniqueid": uniqueid,
                        "name": name,
                        "introduce": "",
                        "industry": "",
                        "scope": "",
                        "attach": "",
                        "texture": "",
                        "country_id": country_id,
                        "country": country,
                        "province": "",
                        "province_id": 0,
                        "city": "",
                        "city_id": 0,
                        "address": "",
                        "phone": "",
                        "email": "",
                        "website": ""
                    })
                    cur_id_set.add(uniqueid)
        return company_items

    def deal_trade(self, data_raw) -> Optional[list]:
        if len(data_raw) == 0:
            return
        insert_list = list()
        cur_id_set = set()
        customs_company_map = self.deal_company_datalist(data_raw)
        for data in data_raw:
            _id, amount, amountcurrency, qty, qtyunit, weight, weightunit, ymd, exporter, \
                origincountrytag, salescountrytag, hs_code, hs_code_description, productdesc, \
                importer, importercountrytag, sourceid, tradetype, seller_port, buyer_port = data
            # 平台唯一id
            openid = sourceid
            if openid == "":
                continue
            # 全量结果表唯一key
            uniqueid = f"{self.source}-{openid}"
            # 提单号
            trade_code = _id
            # 时间戳
            trade_date = int(time.mktime(time.strptime(ymd, "%Y-%m-%d")) * 1000)
            # 重量
            weight_res = 0
            if weight != '':
                try:
                    weight_res = round(float(weight))
                except ValueError:
                    weight_res = 0
            weight_unit = self.transform_unit(weightunit, self.mapping_weight)
            # 数量
            quantity = 0
            if qty != '':
                try:
                    quantity = round(float(qty))
                except ValueError:
                    quantity = 0
            # 数量单位
            quantity_unit = self.transform_unit(qtyunit, self.mapping_quantity)
            # 总价
            amount_res = 0
            if amount != '':
                try:
                    amount_res = round(float(amount))
                except ValueError:
                    amount_res = 0
            if amount_res > 10000000000000:  # 过滤价格的异常值
                continue
            # 单价
            price = 0 if quantity == 0 else round(amount_res / quantity)
            # 价格单位
            amount_unit = amountcurrency
            # 国家映射
            # 原产国
            product_info = self.get_country_map.get(origincountrytag.lower(), dict())
            product_country_id = product_info.get('id', 0)
            product_country = product_info.get('en', '')
            # 供应国
            seller_info = self.get_country_map.get(salescountrytag.lower(), dict())
            seller_country_id = seller_info.get('id', 0)
            seller_country = seller_info.get('en', '')
            # 采购国
            buyer_info = self.get_country_map.get(importercountrytag.lower(), dict())
            buyer_country_id = buyer_info.get('id', 0)
            buyer_country = buyer_info.get('en', '')
            # 产品描述
            product_desc = productdesc
            # 产品标签
            product_tag = ""
            # 产品hscode
            product_hscode = self.transform_hs_code(hs_code)
            # 供应商
            exporter = self.transform_company_name(exporter)

            seller_openid = exporter
            seller = self.transform_company_name(exporter)
            # 采购商
            importer = self.transform_company_name(importer)

            buyer_openid = importer
            buyer = self.transform_company_name(importer)

            if not seller and not buyer:
                continue

            # 出口港
            seller_port = self.transform_port(seller_port)
            # 进口港
            buyer_port = self.transform_port(buyer_port)

            # 交换港口 - 源数据有问题
            if buyer_country_id not in (0, 197):
                seller_port, buyer_port = buyer_port, seller_port

            # 新版去重方案
            weight_res = int(weight_res)
            quantity = int(quantity)
            amount_res = int(amount_res)
            uuid_data = [
                trade_date, seller, seller_country_id, seller_port, buyer, buyer_country_id, buyer_port,
                product_desc, weight_res, quantity, amount_res
            ]
            uuid, cid = self.create_uuid_sid(uuid_data)

            if uuid in cur_id_set:
                continue


            # 转换货币单位
            amount_unit = self.transform_currency_unit(amount_unit)

            trade_type = self.trade_type_map(tradetype)

            cur_id_set.add(uuid)

            insert_list.append({
                "trade_id": 0,
                "source": self.source,
                "openid": openid,
                "uniqueid": uniqueid,
                "trade_code": trade_code,
                "trade_date": trade_date,
                "weight": weight_res,
                "weight_unit": weight_unit,
                "quantity": quantity,
                "quantity_unit": quantity_unit,
                "price": price,
                "amount": amount_res,
                "amount_unit": amount_unit,
                "product_country_id": product_country_id,
                "product_country": product_country,
                "product_desc": product_desc,
                "product_tag": product_tag,
                "product_hscode": product_hscode,
                "seller_id": 0,
                "seller_openid": str(seller_openid),
                "seller": seller,
                "seller_country_id": seller_country_id,
                "seller_country": seller_country,
                "seller_port": seller_port,
                "buyer_id": 0,
                "buyer_openid": str(buyer_openid),
                "buyer": buyer,
                "buyer_country_id": buyer_country_id,
                "buyer_country": buyer_country,
                "buyer_port": buyer_port,
                "notifier": "",
                "container": "",
                "transport": "",
                "trade_type": trade_type,
                "seller_info": customs_company_map.get(seller_openid, {}),
                "buyer_info": customs_company_map.get(buyer_openid, {}),
            })
        return insert_list

    def deal_company_datalist(self, company_info: list):
        customs_company_data_list = self.deal_company(company_info)
        customs_company_map = {}
        for one in customs_company_data_list:
            customs_company_map[one["openid"]] = one
        return customs_company_map

    def deal_trade_datalist(self, data_list: list):
        s_t = time.time()
        customs_trade_data_list = self.deal_trade(data_list)
        if not customs_trade_data_list:
            return
        logger.info(f'end 《dingyi》 preprocess deal datalist === hs: {time.time() - s_t:.2f} s')
        # 执行贸易清洗
        self.trade_clean_obj.deal_datalist(customs_trade_data_list)

    def test_unit(self):
        # sql = f'''select * from {self.customs_db}.{self.customs_trade_table} order by aid limit 1000'''
        global start_ok
        print(f'''初始化耗时：{int(time.time()-start_ok)}''')
        fields = [
            'id', 'amount', 'amountcurrency', 'qty', 'qtyunit', 'weight', 'weightunit', 'ymd',
            'exporter', 'origincountrytag', 'salescountrytag', 'hs_code', 'hs_code_description',
            'productdesc', 'importer', 'importercountrytag', 'sourceid', 'tradetype', 'seller_port', 'buyer_port'
        ]
        sql = f"select {', '.join(fields)} from src_dy.dy_haiguan_202312 order by zid limit 1000"
        # sql = f"select {', '.join(fields)} from {self.customs_db}.{self.customs_trade_table} where id=6667897668"
        data_src = self.mysql_obj_customs_new_gz.read(sql, return_dict=False)
        results: List[dict] = data_src.data
        self.deal_trade_datalist(results)


if __name__ == '__main__':
    from models import ConfMySQL

    conf_list = [
        ConfMySQL(name='customs_new_gz', max_connections=1),
        ConfMySQL(name='customs_gz', max_connections=1),
        ConfMySQL(name='company_gz', max_connections=1),
    ]
    startdd_ = time.time()
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    print(f'''数据库连接耗时：{int(time.time()-startdd_)}''')
    start_ok = time.time()
    CleanCustomsWmbTrade(mysql_manager).test_unit()
