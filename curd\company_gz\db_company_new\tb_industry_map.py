#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_industry_map.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from models import MysqlResult
from typing import Sequence


class TableIndustryMap(TableBase):
    db = 'db_company_new'
    table = 'industry_map'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_industry_map(self) -> dict:
        """获取公司行业数据"""
        # 获取行业数据
        sql = f'''
                select `id`, `industry_class`, `industry_code`, `industry`, `industry_cn` from {self.db}.{self.table} where `is_deleted`=0
            '''
        data_src: MysqlResult = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        industry_map = {}
        for data in results:
            industry_id = data.get('id', 0)
            industry_class = data.get('industry_class', '')
            industry_code = data.get('industry_code', '')
            industry_en = data.get('industry', '')
            industry_cn = data.get('industry_cn', '')
            if industry_class not in industry_map:
                industry_map[industry_class] = {}
            if industry_code not in industry_map[industry_class]:
                industry_map[industry_class][industry_code] = {}
            industry_map[industry_class][industry_code] = {'industry_id': industry_id, 'industry_en': industry_en, 'industry_cn': industry_cn}
        return industry_map
