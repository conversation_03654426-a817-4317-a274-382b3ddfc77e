#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company.py
@Date: 2024/5/29
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence


class TableCompany(TableBase):
    db = 'db_customs'
    table = 't_company'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_exist_openid(self, openid_list: list, source: int) -> dict:
        """
        校验数据源公司是否存在
        """
        if not openid_list or not source:
            return {}
        openid_list = list(set(openid_list))
        sql = f'''
            select openid, source, company_id from {self.db}.{self.table_name} where 
            openid in ({','.join(['%s'] * len(openid_list))}) and source={source}
        '''
        data_src = self.db_mysql.read(sql, openid_list, return_dict=True)
        results: Sequence[dict] = data_src.data
        ocid_map = {}
        for result in results:
            ocid_map[result['openid']] = result['company_id']
        return ocid_map

