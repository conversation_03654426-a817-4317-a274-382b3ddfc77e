FROM python:3.11.6-alpine

# 拷贝当前目录到容器内部的/app目录(会自动创建目录)
COPY . /app

# 设置容器内部的工作目录是/app
WORKDIR /app

# 将项目根目录添加到环境变量(为了兼容项目中的相对路径)
ENV PYTHONPATH /app:$PYTHONPATH

# 设置时区是上海
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

# 更新pip并批量安装项目需要的库(非大陆环境使用)
#RUN pip install --upgrade pip && pip install -r requirements.txt

## 使用清华源 更新pip并批量安装项目需要的库(大陆环境使用)
RUN pip install -i https://mirrors.aliyun.com/pypi/simple --upgrade pip && pip install -i https://mirrors.aliyun.com/pypi/simple -r requirements.txt

# 启动命令，会被docker-compose.yaml中的command覆盖
#CMD python bin/app.py
