#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: tb_patent.py
@Date: 2025-01-28
@Desc: 专利基础信息表操作类
@Server: 
"""
from typing import List, Sequence, Dict, Any
from curd.tb_base import TableBase


class TablePatent(TableBase):
    db = 'db_patent_staging'
    table = 'patent'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_exist_by_p_id(self, p_id_list: List[str]) -> Dict[str, Any]:
        """
        校验专利是否存在
        :param p_id_list: 专利ID列表
        :return: 存在的专利ID映射
        """
        if not p_id_list:
            return {}
        
        p_id_list = list(set(p_id_list))
        placeholders = ','.join(['%s'] * len(p_id_list))
        sql = f'''
            SELECT p_id, id FROM {self.db}.{self.table} 
            WHERE p_id IN ({placeholders})
        '''
        data_src = self.db_mysql.read(sql, p_id_list, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        p_id_map = {}
        for result in results:
            p_id_map[result['p_id']] = result['id']
        return p_id_map

    def get_patent_info_by_source(self, source_type: int, source_id_list: List[str]) -> Dict[str, Any]:
        """
        根据数据源信息获取专利信息
        :param source_type: 数据源类型
        :param source_id_list: 源ID列表
        :return: 专利信息映射
        """
        if not source_id_list:
            return {}
        
        source_id_list = list(set(source_id_list))
        placeholders = ','.join(['%s'] * len(source_id_list))
        sql = f'''
            SELECT source_id, p_id, patent_number, filing_country 
            FROM {self.db}.{self.table} 
            WHERE source_type = %s AND source_id IN ({placeholders})
        '''
        params = [source_type] + source_id_list
        data_src = self.db_mysql.read(sql, params, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        source_map = {}
        for result in results:
            source_map[result['source_id']] = result
        return source_map

    def batch_insert_patents(self, patent_data_list: List[dict]) -> bool:
        """
        批量插入专利数据
        :param patent_data_list: 专利数据列表
        :return: 是否成功
        """
        if not patent_data_list:
            return True
        
        try:
            result = self.db_mysql.save(
                table=f"{self.db}.{self.table}",
                items=patent_data_list,
                ignore=True
            )
            return result is not None
        except Exception as e:
            print(f"批量插入专利数据失败: {e}")
            return False

    def update_patent_by_p_id(self, p_id: str, update_data: dict) -> bool:
        """
        根据专利ID更新专利信息
        :param p_id: 专利ID
        :param update_data: 更新数据
        :return: 是否成功
        """
        if not p_id or not update_data:
            return False
        
        try:
            set_clause = ', '.join([f"{k} = %s" for k in update_data.keys()])
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET {set_clause} 
                WHERE p_id = %s
            '''
            params = list(update_data.values()) + [p_id]
            result = self.db_mysql.execute(sql, params)
            return result.affected_rows > 0
        except Exception as e:
            print(f"更新专利信息失败: {e}")
            return False
