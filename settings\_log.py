import os
import sys
import logging
import threading
from pathlib import Path
from loguru import logger
from settings._conf import CONF_GLOBAL
from settings._setting import DIR_PROJECT
from public_utils_configs.util.logger import ConcurrentTimedRotatingFileHandler

LOG_LEVEL = CONF_GLOBAL.log.level.upper()
ONLY_STDOUT = CONF_GLOBAL.log.only_stdout

ConcurrentTimedRotatingFileHandler.init_logger(level=LOG_LEVEL, only_stdout=ONLY_STDOUT)

# 日志logger
logger.remove()  # 删除默认的日志输出
# 动态调整日志格式
log_format_field = list()
if CONF_GLOBAL.log.pid:
    log_format_field.append('{extra[process]}')
if CONF_GLOBAL.log.tid:
    log_format_field.append('{extra[thread]}')
log_format_field.extend(['{name}', '{function}', '{line}'])
log_format_field_str = ':'.join(log_format_field)
# 拼接日志格式
log_format = (
    # 时间
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
    # 级别，info、warning、error...，<level>会根据级别来改变颜色</level>
    "<level>{level: <0}</level> | "
    # 进程号:线程号:名字:方法:行号
    # "<cyan>{extra[process]}:{extra[thread]}:{name}:{function}:{line}</cyan> | "
    f"<cyan>{log_format_field_str}</cyan> | "
    # 消息
    "<level>{message}</level>"
)
LOG_LEVEL = CONF_GLOBAL.log.level.upper()
# 配置日志器
logger.add(sink=sys.stdout, format=log_format, level=LOG_LEVEL)
# 是否记录警告日志到文件
if CONF_GLOBAL.log.record_warning:
    dir_logs = Path(DIR_PROJECT, 'logs')
    dir_logs.mkdir(exist_ok=True)
    warning_file = Path(dir_logs, 'warning.log')
    logger.add(sink=warning_file, format=log_format, level="WARNING", mode='w', encoding='utf8')


def add_context(record):
    # 给logger添加额外的上下文信息 - 动态获取参数
    if CONF_GLOBAL.log.pid:
        record["extra"]["process"] = os.getpid()  # 添加进程号
    if CONF_GLOBAL.log.tid:
        record["extra"]["thread"] = threading.get_ident()  # 添加线程号


logger = logger.patch(add_context)


def _test():
    logging.info('test')
    logger.info('test')


if __name__ == '__main__':
    _test()
