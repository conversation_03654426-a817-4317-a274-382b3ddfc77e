services:
  datak_depth_company:  # 容器名(前面会自动加上项目目录的名字)
    container_name: datak_depth_company  # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build: .  # 指定Dockerfile的路径，用于构建容器镜像(仅使用于Dockerfile文件名为默认的情况)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: datak_depth_company:0.1  # 镜像名:标签
    command: python datak.py depth_company.yml  # 容器的启动命令
#    environment:
#      reader: depth_company.yml  # reader配置文件路径
#    network_mode: "host"  # 使用宿主机的网络组
    volumes:  # 数据卷
      - ./logs:/app/logs
    logging:  # 日志
      options:
        max-size: "10m"
        max-file: "3"

  datak_customs_trade:  # 容器名(前面会自动加上项目目录的名字)
    container_name: datak_customs_trade  # 容器名(指定完整的容器名，会忽略上面的容器名)
#    build: .  # 指定Dockerfile的路径，用于构建容器镜像(仅使用于Dockerfile文件名为默认的情况)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: datak_customs_trade:0.1  # 镜像名:标签
    command: python datak.py customs_trade_all.yml  # 容器的启动命令
#    environment:
#      reader: depth_company.yml  # reader配置文件路径
#    network_mode: "host"  # 使用宿主机的网络组
#    ports:  # 端口映射
#      - "55001:55001"
    volumes:  # 数据卷
      - ./logs:/app/logs
    logging:  # 日志
      options:
        max-size: "10m"
        max-file: "3"

  datak_apollo_company: # 容器名(前面会自动加上项目目录的名字)
    container_name: datak_apollo_company  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: datak_apollo_company:0.1  # 镜像名:标签
    command: python datak.py apollo_company.yml  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"

  datak_link: # 容器名(前面会自动加上项目目录的名字)
    container_name: datak_link  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: datak_link:0.1  # 镜像名:标签
    command: python datak.py link.yml  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"

  datak_customs_company: # 容器名(前面会自动加上项目目录的名字)
    container_name: datak_customs_company  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: datak_customs_company:0.1  # 镜像名:标签
    command: python datak.py customs_company.yml  # 容器的启动命令
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "10m"
        max-file: "3"

