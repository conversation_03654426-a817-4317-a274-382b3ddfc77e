# 专利数据清洗配置文件
# 数据源：swissreg专利数据

name: patent_clean  # 任务名字，全局唯一
source: patent  # 业务名字

conn_inner: true  # 是否内网访问

mysql: # 数据源mysql的基本配置信息
  name: sp02  # 源数据连接名（使用现有的spider连接）
  db: db_spider  # 数据库名
  max_connections: 10

mysql_other: # 其他中间表的配置信息
  - name: customs_gz  # 目标库连接名
    db: db_patent_staging  # 目标数据库名
    max_connections: 10

query:
  # 查询专利基础信息（主表）
  sql: SELECT id, open_id, patent_id, application_number, publication_date, application_date, grant_date, next_renewal, protection_date_max, status, ip_type, International_registration, international_publication, priorities, an_inheritance, exhibition_immunity, paediatric_SPC, Cancellation, patent_specifications FROM swissreg_patent_info WHERE id >= %s AND id <= %s ORDER BY id

split: # 拆分数据的逻辑
  type: id  # id:1 or time:2，必选
  field: # 如果type是time，下面两个都要写，如果type是id，下面可以只填id
    id: id  # 自增id字段，必选
    time: update_time  # 时间字段，可选
  batch_size: 1000  # 任务分片大小，必选

preprocess: clean_patent  # 预处理方法名，必选

# process:  # 结果mysql实例业务名称（计划报废）
#   - patent

worker: 8  # 多进程并行量

# 数据处理配置
data_config:
  # 专利数据源类型
  source_type: 1  # 0:wipo, 1:swissreg
  
  # 数据清洗规则
  clean_rules:
    # 日期格式转换
    date_format: "YYYY-MM-DD"
    # 专利号清洗
    patent_number_clean: true
    # 国家代码映射
    country_mapping:
      "CH": "Switzerland"
      "EP": "European Patent"
      "US": "United States"
      "CN": "China"
  
  # 实体类型映射
  entity_mapping:
    "INHABER": "company"    # 专利持有人 -> 公司
    "ERFINDER": "human"     # 发明者 -> 个人
  
  # 批量处理配置
  batch_config:
    patent_batch_size: 500
    owner_batch_size: 1000
    class_batch_size: 1000
