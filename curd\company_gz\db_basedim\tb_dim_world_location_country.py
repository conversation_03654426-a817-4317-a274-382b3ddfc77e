#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_dim_world_location_country.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableDimWorldLocationCountry(TableBase):
    db = 'basedim'
    table = 'dim_world_location_country'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_country_code_map(self) -> dict:
        """
        获取 国家 编码
        :return:
        """
        sql = f'''
            select country, country_iso_code, country_cn from {self.db}.{self.table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        country_code_map = {}
        for data in results:
            country = data.get('country', '')
            country_iso_code = data.get('country_iso_code', '')
            country_cn = data.get('country_cn', '')
            country_code_map[country_iso_code.lower()] = {'country_en': country, 'country_cn': country_cn}
        return country_code_map
