#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2024-12-27 15:52:45
@Desc: 
@Server: 
"""
import time
from typing import List, Union
from settings import *
from common.database import DBPoolMysql
from common.database import MysqlConnectionManager
from models.public_clean_model import *


class PublicSaveMain:
    def __init__(self, mysql_manager: MysqlConnectionManager):
        self.mysql_obj_company_new_gz: DBPoolMysql = mysql_manager.get_pool("company_new_gz")
        # 获取表结构默认值信息
        table_infos = [
            {'table_name': 'company', 'db': 'db_company_new'},
            {'table_name': 'company_status_map', 'db': 'db_company_new'},
            {'table_name': 'company_type_map', 'db': 'db_company_new'},
            {'table_name': 'company_area_info', 'db': 'db_company_new'},
            {'table_name': 'company_logo', 'db': 'db_company_new'},
            {'table_name': 'bus_id_map_company', 'db': 'db_company_new'},
            {'table_name': 'company_addresses', 'db': 'db_company_new'},
            {'table_name': 'company_industry', 'db': 'db_company_new'},
            {'table_name': 'company_names', 'db': 'db_company_new'},
            {'table_name': 'company_products', 'db': 'db_company_new'},
            {'table_name': 'company_stock', 'db': 'db_company_new'},
            {'table_name': 'company_shareholders', 'db': 'db_company_new'},
            {'table_name': 'company_subsidiary', 'db': 'db_company_new'},
            {'table_name': 'company_national_identifiers', 'db': 'db_company_new'},
            {'table_name': 'company_tech_stack', 'db': 'db_company_new'},
            {'table_name': 'emails_all', 'db': 'db_concat_new'},
            {'table_name': 'phones_all', 'db': 'db_concat_new'},
            {'table_name': 'socials_all', 'db': 'db_concat_new'},
            {'table_name': 'websites_all', 'db': 'db_concat_new'},
            {'table_name': 'business_emails_all', 'db': 'db_concat_new'},
            {'table_name': 'business_phones_all', 'db': 'db_concat_new'},
            {'table_name': 'business_socials_all', 'db': 'db_concat_new'},
            {'table_name': 'business_websites_all', 'db': 'db_concat_new'},
            {'table_name': 'human', 'db': 'db_human_new'},
            {'table_name': 'human_logo', 'db': 'db_human_new'},
            {'table_name': 'human_addresses', 'db': 'db_human_new'},
            {'table_name': 'human_education', 'db': 'db_human_new'},
            {'table_name': 'human_experience', 'db': 'db_human_new'},
            {'table_name': 'bus_id_map_human', 'db': 'db_human_new'},
            {'table_name': 'bus_id_map_school', 'db': 'db_human_new'},
            {'table_name': 'school', 'db': 'db_human_new'},
        ]
        self.table_filed_map = {}
        for table_info in table_infos:
            table_name = table_info['table_name']
            db = table_info['db']
            field_schema = self.mysql_obj_company_new_gz.read_column_default(table_name, db)
            self.table_filed_map[table_name] = {
                'table_name': table_name,
                'db': db,
                'field_schema': field_schema,
            }

    def save_to_mysql_concat(self, public_clean_concat_main_obj_list: List[PublicCleanConcatMain]):
        """
        联系方式 数据存储至数据库
        """
        if not public_clean_concat_main_obj_list:
            return None

        emails_datalist = []
        phones_datalist = []
        socials_datalist = []
        websites_datalist = []
        business_emails_datalist = []
        business_phones_datalist = []
        business_socials_datalist = []
        business_websites_datalist = []

        for public_clean_concat_main_obj in public_clean_concat_main_obj_list:
            emails_datalist_tmp = d if (d := public_clean_concat_main_obj.emails_obj_list) else list()
            phones_datalist_tmp = d if (d := public_clean_concat_main_obj.phones_obj_list) else list()
            socials_datalist_tmp = d if (d := public_clean_concat_main_obj.socials_obj_list) else list()
            websites_datalist_tmp = d if (d := public_clean_concat_main_obj.websites_obj_list) else list()
            business_emails_datalist_tmp = d if (
                d := public_clean_concat_main_obj.business_emails_obj_list) else list()
            business_phones_datalist_tmp = d if (
                d := public_clean_concat_main_obj.business_phones_obj_list) else list()
            business_socials_datalist_tmp = d if (
                d := public_clean_concat_main_obj.business_socials_obj_list) else list()
            business_websites_datalist_tmp = d if (
                d := public_clean_concat_main_obj.business_websites_obj_list) else list()

            for emails_data in emails_datalist_tmp:
                emails_data = emails_data.__dict__
                emails_datalist.append(emails_data)
            for phones_data in phones_datalist_tmp:
                phones_data = phones_data.__dict__
                phones_datalist.append(phones_data)
            for socials_data in socials_datalist_tmp:
                socials_data = socials_data.__dict__
                socials_datalist.append(socials_data)
            for websites_data in websites_datalist_tmp:
                websites_data = websites_data.__dict__
                websites_datalist.append(websites_data)
            for business_emails_data in business_emails_datalist_tmp:
                business_emails_data = business_emails_data.__dict__
                business_emails_datalist.append(business_emails_data)
            for business_phones_data in business_phones_datalist_tmp:
                business_phones_data = business_phones_data.__dict__
                business_phones_datalist.append(business_phones_data)
            for business_socials_data in business_socials_datalist_tmp:
                business_socials_data = business_socials_data.__dict__
                business_socials_datalist.append(business_socials_data)
            for business_websites_data in business_websites_datalist_tmp:
                business_websites_data = business_websites_data.__dict__
                business_websites_datalist.append(business_websites_data)

        # 存储
        for table_name, v in self.table_filed_map.items():
            datalist = []
            if table_name == 'emails_all' and emails_datalist:
                datalist = emails_datalist
            if table_name == 'phones_all' and phones_datalist:
                datalist = phones_datalist
            if table_name == 'socials_all' and socials_datalist:
                datalist = socials_datalist
            if table_name == 'websites_all' and websites_datalist:
                datalist = websites_datalist
            if table_name == 'business_emails_all' and business_emails_datalist:
                datalist = business_emails_datalist
            if table_name == 'business_phones_all' and business_phones_datalist:
                datalist = business_phones_datalist
            if table_name == 'business_socials_all' and business_socials_datalist:
                datalist = business_socials_datalist
            if table_name == 'business_websites_all' and business_websites_datalist:
                datalist = business_websites_datalist
            if not datalist:
                continue
            # 防止一次太多数量，进行分批次存储
            batch_size = 1000
            for i in range(0, len(datalist), batch_size):
                datalist_batch = datalist[i:i + batch_size]  # 切片获取当前批次
                start = time.time()
                logging.info(f'start save【concat】data {table_name}[{len(datalist_batch)}]')
                res = self.mysql_obj_company_new_gz.save(
                    table_name, datalist_batch,
                    db=v['db'],
                    ignore=True,
                    field_schema=v['field_schema']
                )
                logging.info(f'save【concat】data {table_name}[{len(datalist_batch)}] res.rowcount: {res.rowcount} hs:{time.time() - start:.2f}s')

    def save_to_mysql_school(self, public_clean_school_main_obj_list: List[PublicCleanSchoolMain]):
        """
        学校 数据存储至数据库
        """
        school_datalist = []
        bus_id_map_datalist = []
        public_clean_concat_main_obj_list = []
        for public_clean_school_main_obj in public_clean_school_main_obj_list:
            school_data = d.__dict__ if (d := public_clean_school_main_obj.school_obj) else dict()
            bus_id_map_data = d.__dict__ if (d := public_clean_school_main_obj.bus_id_map_obj) else dict()
            public_clean_concat_main_obj = d if (d := public_clean_school_main_obj.concat_obj) else None
            if school_data:
                school_datalist.append(school_data)
            if bus_id_map_data:
                bus_id_map_datalist.append(bus_id_map_data)
            if public_clean_concat_main_obj:
                public_clean_concat_main_obj_list.append(public_clean_concat_main_obj)

        if not school_datalist:
            return
        for table_name, v in self.table_filed_map.items():
            datalist = []
            if table_name == 'bus_id_map_school' and bus_id_map_datalist:
                datalist = bus_id_map_datalist
            if table_name == 'school' and school_datalist:
                datalist = school_datalist
            if not datalist:
                continue
            start = time.time()
            logging.info(f'start save【school】data {table_name}[{len(datalist)}]')
            res = self.mysql_obj_company_new_gz.save(
                table_name, datalist,
                db=v['db'],
                ignore=True,
                field_schema=v['field_schema']
            )
            logging.info(f'save【school】data {table_name}[{len(datalist)}] res.rowcount: {res.rowcount} hs:{time.time() - start:.2f}s')
        if public_clean_concat_main_obj_list:
            # 联系方式数据存储
            self.save_to_mysql_concat(public_clean_concat_main_obj_list)

    def save_to_mysql_human(self, public_clean_human_main_obj_list: List[PublicCleanHumanMain]):
        """
        人物 数据存储至数据库
        """
        human_datalist = []
        bus_id_map_datalist = []
        human_logo_datalist = []
        human_addresses_datalist = []
        human_education_datalist = []
        human_experience_datalist = []
        # 对 human_education_datalist 去重
        human_education_datalist_dict = {}
        # 对 human_experience_datalist 去重
        human_experience_datalist_dict = {}

        public_clean_concat_main_obj_list = []

        for public_clean_human_main_obj in public_clean_human_main_obj_list:
            human_data = d.__dict__ if (d := public_clean_human_main_obj.human_obj) else dict()
            bus_id_map_data = d.__dict__ if (d := public_clean_human_main_obj.bus_id_map_obj) else dict()
            human_logo_datalist_tmp = d if (d := public_clean_human_main_obj.human_logo_obj_list) else list()
            human_addresses_datalist_tmp = d if (d := public_clean_human_main_obj.human_addresses_obj_list) else list()
            human_education_datalist_tmp = d if (d := public_clean_human_main_obj.human_education_obj_list) else list()
            human_experience_datalist_tmp = d if (d := public_clean_human_main_obj.human_experience_obj_list) else list()
            public_clean_concat_main_obj = d if (d := public_clean_human_main_obj.concat_obj) else None

            if human_data:
                human_datalist.append(human_data)
            if bus_id_map_data:
                bus_id_map_datalist.append(bus_id_map_data)
            for human_logo_data in human_logo_datalist_tmp:
                human_logo_data = human_logo_data.__dict__
                human_logo_datalist.append(human_logo_data)
            for human_addresses_data in human_addresses_datalist_tmp:
                human_addresses_data = human_addresses_data.__dict__
                human_addresses_datalist.append(human_addresses_data)
            for human_education_data in human_education_datalist_tmp:
                human_education_data = human_education_data.__dict__
                _hid = human_education_data.get('hid', '')
                _sid = human_education_data.get('sid', '')
                if _hid and _sid:
                    human_education_datalist_dict[f'{_hid}{_sid}'] = human_education_data
            for human_experience_data in human_experience_datalist_tmp:
                human_experience_data = human_experience_data.__dict__
                if human_experience_data.get('uuid', ''):
                    human_experience_datalist_dict[human_experience_data['uuid']] = human_experience_data
            if public_clean_concat_main_obj:
                public_clean_concat_main_obj_list.append(public_clean_concat_main_obj)

        if human_education_datalist_dict:
            human_education_datalist = list(human_education_datalist_dict.values())
        if human_experience_datalist_dict:
            human_experience_datalist = list(human_experience_datalist_dict.values())

        if not human_datalist:
            return
        for table_name, v in self.table_filed_map.items():
            datalist = []
            if table_name == 'bus_id_map_human' and bus_id_map_datalist:
                datalist = bus_id_map_datalist
            if table_name == 'human' and human_datalist:
                datalist = human_datalist
            if table_name == 'human_logo' and human_logo_datalist:
                datalist = human_logo_datalist
            if table_name == 'human_addresses' and human_addresses_datalist:
                datalist = human_addresses_datalist
            if table_name == 'human_education' and human_education_datalist:
                datalist = human_education_datalist
            if table_name == 'human_experience' and human_experience_datalist:
                datalist = human_experience_datalist

            if not datalist:
                continue
            # 防止一次太多数量，进行分批次存储
            batch_size = 1000
            for i in range(0, len(datalist), batch_size):
                datalist_batch = datalist[i:i + batch_size]  # 切片获取当前批次
                start = time.time()
                logging.info(f'start save【human】data {table_name}[{len(datalist_batch)}]')
                res = self.mysql_obj_company_new_gz.save(
                    table_name, datalist_batch,
                    db=v['db'],
                    ignore=True,
                    field_schema=v['field_schema']
                )
                logging.info(f'save【human】data {table_name}[{len(datalist_batch)}] res.rowcount: {res.rowcount} hs:{time.time() - start:.2f}s')

        if public_clean_concat_main_obj_list:
            # 联系方式数据存储
            self.save_to_mysql_concat(public_clean_concat_main_obj_list)

    def save_to_mysql_company(self, public_clean_company_main_obj_list: List[PublicCleanCompanyMain]):
        """
        公司数据存储至数据库
        """
        if not public_clean_company_main_obj_list:
            return
        company_datalist = []
        company_status_map_datalist = []
        company_type_map_datalist = []
        company_area_info_datalist = []
        company_logo_datalist = []
        bus_id_map_datalist = []
        company_addresses_datalist = []
        company_industry_datalist = []
        company_names_datalist = []
        company_products_datalist = []
        company_stock_datalist = []
        company_shareholders_datalist = []
        company_subsidiary_datalist = []
        company_national_identifiers_datalist = []
        company_tech_stack_datalist = []

        public_clean_concat_main_obj_list = []

        for public_clean_company_main_obj in public_clean_company_main_obj_list:
            company_data = d.__dict__ if (d := public_clean_company_main_obj.company_obj) else dict()
            company_status_map_data = d.__dict__ if (
                d := public_clean_company_main_obj.company_status_map_obj) else dict()
            company_type_map_data = d.__dict__ if (d := public_clean_company_main_obj.company_type_map_obj) else dict()
            company_area_info_data = d.__dict__ if (
                d := public_clean_company_main_obj.company_area_info_obj) else dict()
            bus_id_map_datalist_tmp = d if (d := public_clean_company_main_obj.bus_id_map_obj_list) else list()
            company_logo_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_logo_obj_list) else list()
            company_addresses_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_addresses_obj_list) else list()
            company_industry_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_industry_obj_list) else list()
            company_names_datalist_tmp = d if (d := public_clean_company_main_obj.company_names_obj_list) else list()
            company_products_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_products_obj_list) else list()
            company_stock_datalist_tmp = d if (d := public_clean_company_main_obj.company_stock_obj_list) else list()
            company_shareholders_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_shareholders_obj_list) else list()
            company_subsidiary_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_subsidiary_obj_list) else list()
            public_clean_concat_main_obj = d if (d := public_clean_company_main_obj.concat_obj) else None
            company_national_identifiers_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_national_identifiers_obj_list) else list()
            company_tech_stack_datalist_tmp = d if (
                d := public_clean_company_main_obj.company_tech_stack_obj_list) else list()

            if company_data:
                company_datalist.append(company_data)
            if company_status_map_data:
                company_status_map_datalist.append(company_status_map_data)
            if company_type_map_data:
                company_type_map_datalist.append(company_type_map_data)
            if company_area_info_data:
                company_area_info_datalist.append(company_area_info_data)

            bus_id_map_deduplication = set()  # 去重
            for bus_id_map_data in bus_id_map_datalist_tmp:
                bus_id_map_data = bus_id_map_data.__dict__
                source_name = bus_id_map_data.get('source_name', '')
                bus_id_old = bus_id_map_data.get('bus_id_old', '')
                bus_type = bus_id_map_data.get('bus_type', 0)
                key = f'{source_name}{bus_id_old}{bus_type}'
                if key in bus_id_map_deduplication:
                    continue
                bus_id_map_datalist.append(bus_id_map_data)
                bus_id_map_deduplication.add(key)
            for company_addresses_data in company_addresses_datalist_tmp:
                company_addresses_data = company_addresses_data.__dict__
                company_addresses_datalist.append(company_addresses_data)
            for company_logo_data in company_logo_datalist_tmp:
                company_logo_data = company_logo_data.__dict__
                company_logo_datalist.append(company_logo_data)
            for company_industry_data in company_industry_datalist_tmp:
                company_industry_data = company_industry_data.__dict__
                company_industry_datalist.append(company_industry_data)
            for company_names_data in company_names_datalist_tmp:
                company_names_data = company_names_data.__dict__
                company_names_datalist.append(company_names_data)
            for company_products_data in company_products_datalist_tmp:
                company_products_data = company_products_data.__dict__
                company_products_datalist.append(company_products_data)
            for company_stock_data in company_stock_datalist_tmp:
                company_stock_data = company_stock_data.__dict__
                company_stock_datalist.append(company_stock_data)
            for company_shareholders_data in company_shareholders_datalist_tmp:
                company_shareholders_data = company_shareholders_data.__dict__
                if 'bus_id_old' in company_shareholders_data:
                    # bus_id_old在该模型中仅用于临时存储，对应表无该字段
                    del company_shareholders_data['bus_id_old']
                company_shareholders_datalist.append(company_shareholders_data)
            for company_subsidiary_data in company_subsidiary_datalist_tmp:
                company_subsidiary_data = company_subsidiary_data.__dict__
                if 'bus_id_old' in company_subsidiary_data:
                    # bus_id_old在该模型中仅用于临时存储，对应表无该字段
                    del company_subsidiary_data['bus_id_old']
                company_subsidiary_datalist.append(company_subsidiary_data)
            for company_national_identifiers_data in company_national_identifiers_datalist_tmp:
                company_national_identifiers_data = company_national_identifiers_data.__dict__
                company_national_identifiers_datalist.append(company_national_identifiers_data)
            for company_tech_stack_data in company_tech_stack_datalist_tmp:
                company_tech_stack_data = company_tech_stack_data.__dict__
                company_tech_stack_datalist.append(company_tech_stack_data)

            if public_clean_concat_main_obj:
                public_clean_concat_main_obj_list.append(public_clean_concat_main_obj)

        # 存储
        for table_name, v in self.table_filed_map.items():
            datalist = []
            if table_name == 'company' and company_datalist:
                datalist = company_datalist
            if table_name == 'company_status_map' and company_status_map_datalist:
                datalist = company_status_map_datalist
            if table_name == 'company_type_map' and company_type_map_datalist:
                datalist = company_type_map_datalist
            if table_name == 'company_area_info' and company_area_info_datalist:
                datalist = company_area_info_datalist
            if table_name == 'company_logo' and company_logo_datalist:
                datalist = company_logo_datalist
            if table_name == 'bus_id_map_company' and bus_id_map_datalist:
                datalist = bus_id_map_datalist
            if table_name == 'company_addresses' and company_addresses_datalist:
                datalist = company_addresses_datalist
            if table_name == 'company_industry' and company_industry_datalist:
                datalist = company_industry_datalist
            if table_name == 'company_names' and company_names_datalist:
                datalist = company_names_datalist
            if table_name == 'company_products' and company_products_datalist:
                datalist = company_products_datalist
            if table_name == 'company_stock' and company_stock_datalist:
                datalist = company_stock_datalist
            if table_name == 'company_shareholders' and company_shareholders_datalist:
                datalist = company_shareholders_datalist
            if table_name == 'company_subsidiary' and company_subsidiary_datalist:
                datalist = company_subsidiary_datalist
            if table_name == 'company_national_identifiers' and company_national_identifiers_datalist:
                datalist = company_national_identifiers_datalist
            if table_name == 'company_tech_stack' and company_tech_stack_datalist:
                datalist = company_tech_stack_datalist
            if not datalist:
                continue
            start = time.time()
            logging.info(f'start save data {table_name}[{len(datalist)}]')
            res = self.mysql_obj_company_new_gz.save(
                table_name, datalist,
                db=v['db'],
                ignore=True,
                field_schema=v['field_schema']
            )
            logging.info(f'save data {table_name}[{len(datalist)}] res.rowcount: {res.rowcount} hs:{time.time() - start:.2f}s')

        if public_clean_concat_main_obj_list:
            # 联系方式数据存储
            self.save_to_mysql_concat(public_clean_concat_main_obj_list)

    def save_to_mysql(
            self,
            public_clean_company_main_obj_list: Union[List[PublicCleanCompanyMain], None] = None,
            public_clean_human_main_obj_list: Union[List[PublicCleanHumanMain], None] = None,
            public_clean_school_main_obj_list: Union[List[PublicCleanSchoolMain], None] = None,
            public_clean_concat_main_obj_list: Union[List[PublicCleanConcatMain], None] = None,
    ):
        """
        存储数据
        """
        if public_clean_company_main_obj_list:
            self.save_to_mysql_company(public_clean_company_main_obj_list)
        if public_clean_human_main_obj_list:
            self.save_to_mysql_human(public_clean_human_main_obj_list)
        if public_clean_school_main_obj_list:
            self.save_to_mysql_school(public_clean_school_main_obj_list)
        if public_clean_concat_main_obj_list:
            self.save_to_mysql_concat(public_clean_concat_main_obj_list)

        pass
