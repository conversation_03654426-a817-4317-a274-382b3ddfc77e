#!/usr/bin/env python
# -*- coding: utf-8 -*-

from curd.tb_base import TableBase
from typing import List, Sequence, Dict, Any


class TableCompany(TableBase):
    db = 'db_customs'
    table = 't_company'
    identify_address_table = 'customs_identify_address'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_company_info(self, company_ids: List[int]) -> dict[Any, Any]:
        """
        通过 公司id 获取 公司基本信息
        """
        if not company_ids:
            return {}

        zwf = ",".join(["%s"] * len(company_ids))
        sql = f'''select * from {self.db}.{self.table} where company_id in ({zwf})'''
        data_src = self.db_mysql.read(sql, value=company_ids, return_dict=True)
        results: List[dict] = data_src.data
        company_info_data_map = {}
        for one in results:
            company_info_data_map[one['company_id']] = one
        return company_info_data_map

    def get_company_address_info(self, company_ids: List[int]) -> dict[Any, Any]:
        """
        通过 公司id 获取 公司地址解析
        """
        if not company_ids:
            return {}

        zwf = ",".join(["%s"] * len(company_ids))
        sql = f'''select * from {self.db}.{self.identify_address_table} where pid in ({zwf})'''
        data_src = self.db_mysql.read(sql, value=company_ids, return_dict=True)
        results: List[dict] = data_src.data
        company_address_data_map = {}
        for one in results:
            company_address_data_map[int(one['pid'])] = one
        return company_address_data_map

    def get_company_id_by_openid(self, openid_list, source):
        """company_id 与 openid 映射"""
        zwf = ",".join(["%s"]*len(openid_list))
        sql = f"select openid, company_id from {self.db}.{self.table} where openid in ({zwf}) and source={source}"
        # data_raw = self.read_data(self.pool_target, sql)
        data_raw = self.db_mysql.read(sql, value=openid_list, return_dict=True).data
        o_c_map = {"0": 0}
        # print(data_raw)
        for one in data_raw:
            o_c_map[one["openid"]] = one["company_id"]
        return o_c_map

    def _exists_company(self, openid, source):
        sql = f"select company_id from {self.db}.{self.table} where openid='{openid}' and source={source}"
        # data_raw = self.read_data(self.pool_target, sql)
        data_raw = self.db_mysql.read(sql, return_dict=False).data
        return False if len(data_raw) == 0 else True