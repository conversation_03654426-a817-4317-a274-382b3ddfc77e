#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: tb_trademark_dedup.py
@Date: 2025-01-28
@Desc: 商标去重表操作类
@Server: 
"""
from typing import List, Sequence, Dict, Any, Set
from curd.tb_base import TableBase


class TableTrademarkDedup(TableBase):
    db = 'db_trademark_staging'
    table = 'trademark_dedup'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def check_duplicate_by_t_ids(self, t_id_list: List[str]) -> Set[str]:
        """
        批量检查商标ID是否已存在（去重）
        :param t_id_list: 商标ID列表
        :return: 已存在的商标ID集合
        """
        if not t_id_list:
            return set()
        
        t_id_list = list(set(t_id_list))  # 去除输入中的重复
        placeholders = ','.join(['%s'] * len(t_id_list))
        sql = f'''
            SELECT t_id FROM {self.db}.{self.table} 
            WHERE t_id IN ({placeholders}) AND status = 1
        '''
        data_src = self.db_mysql.read(sql, t_id_list, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        return {result['t_id'] for result in results}

    def check_duplicate_by_source(self, source_type: int, source_id_list: List[str]) -> Dict[str, str]:
        """
        根据数据源信息检查是否重复
        :param source_type: 数据源类型
        :param source_id_list: 源ID列表
        :return: 源ID到商标ID的映射
        """
        if not source_id_list:
            return {}
        
        source_id_list = list(set(source_id_list))
        placeholders = ','.join(['%s'] * len(source_id_list))
        sql = f'''
            SELECT source_id, t_id FROM {self.db}.{self.table} 
            WHERE source_type = %s AND source_id IN ({placeholders}) AND status = 1
        '''
        params = [source_type] + source_id_list
        data_src = self.db_mysql.read(sql, params, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        return {result['source_id']: result['t_id'] for result in results}

    def batch_insert_dedup_records(self, dedup_data_list: List[dict]) -> bool:
        """
        批量插入去重记录
        :param dedup_data_list: 去重数据列表
        :return: 是否成功
        """
        if not dedup_data_list:
            return True
        
        try:
            # 使用 INSERT IGNORE 避免重复插入
            result = self.db_mysql.save(
                table=f"{self.db}.{self.table}",
                items=dedup_data_list,
                ignore=True
            )
            return result is not None
        except Exception as e:
            print(f"批量插入去重记录失败: {e}")
            return False

    def update_process_count(self, t_id_list: List[str]) -> bool:
        """
        更新处理次数
        :param t_id_list: 商标ID列表
        :return: 是否成功
        """
        if not t_id_list:
            return True
        
        try:
            placeholders = ','.join(['%s'] * len(t_id_list))
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET process_count = process_count + 1,
                    last_processed_time = NOW()
                WHERE t_id IN ({placeholders}) AND status = 1
            '''
            result = self.db_mysql.execute(sql, t_id_list)
            return result.affected_rows >= 0
        except Exception as e:
            print(f"更新处理次数失败: {e}")
            return False

    def get_duplicate_statistics(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """
        获取去重统计信息
        :param start_date: 开始日期 (YYYY-MM-DD)
        :param end_date: 结束日期 (YYYY-MM-DD)
        :return: 统计信息
        """
        where_clause = "WHERE status = 1"
        params = []
        
        if start_date:
            where_clause += " AND DATE(first_processed_time) >= %s"
            params.append(start_date)
        
        if end_date:
            where_clause += " AND DATE(first_processed_time) <= %s"
            params.append(end_date)
        
        sql = f'''
            SELECT 
                COUNT(*) as total_count,
                COUNT(DISTINCT source_type) as source_type_count,
                COUNT(DISTINCT filing_country) as country_count,
                SUM(process_count) as total_process_count,
                AVG(process_count) as avg_process_count,
                MAX(process_count) as max_process_count,
                MIN(first_processed_time) as earliest_time,
                MAX(last_processed_time) as latest_time
            FROM {self.db}.{self.table} 
            {where_clause}
        '''
        
        data_src = self.db_mysql.read(sql, params, return_dict=True)
        results: Sequence[dict] = data_src.data
        
        return results[0] if results else {}

    def clean_old_records(self, days: int = 30) -> bool:
        """
        清理旧的去重记录（软删除）
        :param days: 保留天数
        :return: 是否成功
        """
        try:
            sql = f'''
                UPDATE {self.db}.{self.table} 
                SET status = 0 
                WHERE first_processed_time < DATE_SUB(NOW(), INTERVAL %s DAY)
                AND status = 1
            '''
            result = self.db_mysql.execute(sql, [days])
            print(f"清理了 {result.affected_rows} 条旧记录")
            return True
        except Exception as e:
            print(f"清理旧记录失败: {e}")
            return False

    def get_duplicate_records_by_trademark_name(self, trademark_name: str) -> List[dict]:
        """
        根据商标名称查找重复记录
        :param trademark_name: 商标名称
        :return: 重复记录列表
        """
        if not trademark_name:
            return []
        
        sql = f'''
            SELECT t_id, source_type, source_id, trademark_name, filing_country,
                   first_processed_time, process_count
            FROM {self.db}.{self.table} 
            WHERE trademark_name = %s AND status = 1
            ORDER BY first_processed_time
        '''
        data_src = self.db_mysql.read(sql, [trademark_name], return_dict=True)
        results: Sequence[dict] = data_src.data
        
        return list(results)
