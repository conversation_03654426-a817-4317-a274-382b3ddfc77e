#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_person_director.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence, Dict


class TableCompanyPersonDirector(TableBase):
    db = 'db_comp'
    table = 'company_person_director'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_person_director(self, ucis: list = None, cids: list = None) -> Dict[str, Dict]:
        """
        获取人物任职信息
        :param ucis: 人物ID列表
        :param cids: 公司ID列表
        :return: {"company": {公司ID: []}, "human": {人物ID: []}}
        """
        if not ucis and not cids:
            return {}
        value = []
        sql = f'''
            select pid,uci,full_name,gender,originalpost,
            originalpost_en,preson_type,appointment_date,post_status,lizhi_date,
            zhi<PERSON>,houzhui,adress,department,hierarchy
            from {self.db}.{self.table}
        '''
        w_sqls = []
        if ucis:
            bfhs_str_h = ','.join(['%s'] * len(ucis))
            value += ucis
            w_sqls.append(f'uci in ({bfhs_str_h})')
        if cids:
            bfhs_str_c = ','.join(['%s'] * len(cids))
            value += cids
            w_sqls.append(f'pid in ({bfhs_str_c})')
        w_sqls_str = ' and '.join(w_sqls)
        sql += f' where {w_sqls_str}'
        data_src = self.db_mysql.read(sql, value=value, return_dict=True)
        results: Sequence[dict] = data_src.data
        person_director_data: dict = {"company": {}, "human": {}}
        for result in results:
            pid = result.get('pid', '')
            uci = result.get('uci', '')
            full_name = result.get('full_name', '')
            gender = result.get('gender', '')
            originalpost = result.get('originalpost', '')
            originalpost_en = result.get('originalpost_en', '')
            preson_type = result.get('preson_type', '')
            appointment_date = result.get('appointment_date', '')
            post_status_s = result.get('post_status', '')
            lizhi_date = result.get('lizhi_date', '')
            zhiwei = result.get('zhiwei', '')
            houzhui = result.get('houzhui', '')
            adress = result.get('adress', '')
            department = result.get('department', '')
            hierarchy = result.get('hierarchy', '')
            if not uci or not pid or not full_name:
                continue
            preson_type = preson_type if preson_type else ''
            post_status_s = post_status_s if post_status_s else ''
            cid_source = pid
            hid_source = uci
            human_type = 0
            post_status = 0
            if preson_type.lower() == 'company':
                human_type = 2
            elif preson_type.lower() == 'individual':
                human_type = 1
            if post_status_s.lower() == 'current':
                post_status = 1
            elif post_status_s.lower() == 'previous':
                post_status = 2
            human_name = full_name.lower().strip()
            human_name_sp = human_name.split(' ')
            if human_name_sp[0] in ['mr', 'mrs', 'mr.', 'mrs.']:
                human_name_sp = human_name_sp[1:]
            if human_name_sp and human_name_sp[-1] in ['n/a']:
                human_name_sp = human_name_sp[:-1]
            if not human_name_sp:
                continue
            human_name = ' '.join(human_name_sp).strip()
            gender = gender if gender else ''
            originalpost_en = originalpost_en if originalpost_en else ''
            title_name = originalpost_en.lower().strip() if originalpost_en else ''
            # appointment_date、lizhi_date: 2017 | 01/01/2015
            start_date_str = ''
            end_date_str = ''
            if appointment_date:
                date_sp = appointment_date.split('/')
                if len(date_sp) == 3:
                    start_date_str = f'{date_sp[2]}-{date_sp[1]}-{date_sp[0]}'
                if len(date_sp) == 1:
                    start_date_str = f'{date_sp[0]}-01-01'
            if lizhi_date:
                date_sp = lizhi_date.split('/')
                if len(date_sp) == 3:
                    end_date_str = f'{date_sp[2]}-{date_sp[1]}-{date_sp[0]}'
                if len(date_sp) == 1:
                    end_date_str = f'{date_sp[0]}-01-01'
            address = adress if adress else ''
            title_levels = hierarchy.lower().split(';') if hierarchy else []
            title_levels = [title_level.lower().strip() for title_level in title_levels]
            data = {'cid_source': cid_source, 'hid_source': hid_source, 'human_name': human_name,
                    'human_type': human_type, 'gender': gender, 'title_name': title_name,
                    'start_date_str': start_date_str, 'end_date_str': end_date_str, 'address': address,
                    'department': department, 'title_levels': title_levels, 'post_status': post_status}
            if pid not in person_director_data['company']:
                person_director_data['company'][pid] = []
            person_director_data['company'][pid].append(data)
            if hid_source not in person_director_data['human']:
                person_director_data['human'][hid_source] = []
            person_director_data['human'][hid_source].append(data)
        return person_director_data
