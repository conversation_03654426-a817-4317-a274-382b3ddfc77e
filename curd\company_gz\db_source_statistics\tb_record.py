from typing import Union, Sequence, Optional
from datetime import datetime
from settings import CONF_GLOBAL
from curd.tb_base import TableBase
from models import MysqlResult


class TableRecord(TableBase):
    db = CONF_GLOBAL.tables.record.db
    table = CONF_GLOBAL.tables.record.table

    def read_record(self, name: str, status: int = 0, column_type: Optional[str] = None):
        """读取未完成的任务"""
        column_start = '`start`'
        column_end = '`end`'
        if column_type == 'int':
            column_start = 'cast(`start` as UNSIGNED)'
            column_end = 'cast(`end` as UNSIGNED)'
        sql = (f"select {column_start}, {column_end} from {self.db}.{self.table} "
               f"where `name`={name!r} and `status`={status} order by id")
        return self.db_mysql.read(sql, return_dict=False).data

    def read_record_max(self, name: str, column_type: str = 'str') -> str:
        """
        读取任务最后的分片位置
        :param name: 任务名
        :return: str
        """
        column = '`end`'
        if column_type == 'int':
            column = 'CAST(`end` AS UNSIGNED)'
        sql = f"select max({column}) from {self.db}.{self.table} where `name`='{name}'"
        # print(sql)
        result = self.db_mysql.read(sql, return_dict=False)
        # print(result)
        return '' if result.length == 0 else result.data[0][0]

    def save_record(self, item: Union[Sequence[dict], dict], ignore: bool = False) -> MysqlResult:
        """更新分片任务状态"""
        return self.db_mysql.save(f"{self.db}.{self.table}", item, ignore=ignore)

    @staticmethod
    def iterator_column_time(_min: str, _max: str, total: int, batch_size: int) -> tuple:
        """
        根据字段最大最小值来分片
        :param _min: 时间字符串格式是%Y-%m-%d %H:%M:%S
        :param _max: 时间字符串格式是%Y-%m-%d %H:%M:%S
        :param batch_size: 每个分片的最大数
        :return: yield时间范围切片，使单个切片的数据量<=batch_size
        """
        if _min == _max:
            yield _min, _max
            return
        else:
            min_time = datetime.strptime(_min, '%Y-%m-%d %H:%M:%S')
            max_time = datetime.strptime(_max, '%Y-%m-%d %H:%M:%S')

            # 计算总时间差
            total_duration = max_time - min_time
            # 估算每个批次应该占据的时间跨度
            # 为避免除以零，确保batch_size和total都不为零
            if batch_size == 0 or total == 0:
                raise ValueError("batch_size and total must be greater than 0")

            # 计算每个批次的时间间隔
            batch_duration = total_duration / (total / batch_size)

            # 迭代生成时间范围
            current_start = min_time
            while current_start < max_time:
                current_end = min(current_start + batch_duration, max_time)
                yield (current_start.strftime('%Y-%m-%d %H:%M:%S'), current_end.strftime('%Y-%m-%d %H:%M:%S'))
                current_start = current_end


if __name__ == '__main__':
    # for i in TableRecord.iterator_column_time('2023-04-11 10:39:46', '2023-04-11 10:39:46', 1, 100000):
    for i in TableRecord.iterator_column_time('2022-04-11 10:39:46', '2023-04-11 10:39:46', 2, 100000):
        print(i)