#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-01-06 16:31:37
@Desc: 一些额外的清洗补丁
@Server: 
"""
from typing import List, Union
from common.database import MysqlConnectionManager
from common.database import DBPoolMysql
from curd import TableBusIdMapCompany
from curd import TableBusIdMapHuman
from curd import TableBusIdMapSchool
from models.public_clean_model import PublicCleanCompanyMain
from models.public_clean_model import PublicCleanHumanMain
from models.public_clean_model import PublicCleanSchoolMain
from models.db_company import BusIdMapCompany
from models.db_human import BusIdMapHuman
from models.db_human import BusIdMapSchool


class PatchFuncs:
    def __init__(self, mysql_manager: MysqlConnectionManager):
        self.mysql_obj_company_new_gz: DBPoolMysql = mysql_manager.get_pool("company_new_gz")
        self.table_bus_id_map_company_obj = TableBusIdMapCompany(self.mysql_obj_company_new_gz)
        self.table_bus_id_map_human_obj = TableBusIdMapHuman(self.mysql_obj_company_new_gz)
        self.table_bus_id_map_school_obj = TableBusIdMapSchool(self.mysql_obj_company_new_gz)
        pass

    def check_bus_id_uniformity_func(
            self,
            bus_id_map_obj_list_all: Union[List[BusIdMapCompany], List[BusIdMapHuman], List[BusIdMapSchool]],
            table_bus_id_map_obj: Union[TableBusIdMapCompany, TableBusIdMapHuman, TableBusIdMapSchool]
    ):
        bus_id_map_all = {}
        bus_id_old_all = {}
        for bus_id_map_obj in bus_id_map_obj_list_all:
            source_name = bus_id_map_obj.source_name
            bus_id_old = bus_id_map_obj.bus_id_old
            bus_id_new = bus_id_map_obj.bus_id_new
            if not bus_id_old:
                continue
            if source_name not in bus_id_old_all:
                bus_id_old_all[source_name] = set()
                bus_id_map_all[source_name] = {}
            bus_id_old_all[source_name].add(bus_id_old)
            bus_id_map_all[source_name][bus_id_old] = bus_id_new
        # 在 新旧ID映射表 中校验
        bus_id_map_diff_all = {}  # 与数据库中新ID存在差异的数据
        for source_name, v in bus_id_old_all.items():
            bus_id_map_existed = table_bus_id_map_obj.get_bus_id_news(source_name, list(v))
            if not bus_id_map_existed:
                continue
            bus_id_map_now = bus_id_map_all[source_name]
            # 对比不一样的值
            for bus_id_old_ex, bus_id_new_ex in bus_id_map_existed.items():
                bus_id_new_now = bus_id_map_now[bus_id_old_ex]
                if bus_id_new_ex != bus_id_new_now:
                    continue
                if source_name not in bus_id_map_diff_all:
                    bus_id_map_diff_all[source_name] = {}
                bus_id_map_diff_all[source_name][bus_id_new_now] = bus_id_new_ex
        return bus_id_map_diff_all

    def check_bus_id_uniformity_new(
            self,
            public_clean_company_main_obj_list: List[PublicCleanCompanyMain],
            public_clean_human_main_obj_list: List[PublicCleanHumanMain],
            public_clean_school_main_obj_list: List[PublicCleanSchoolMain]
    ):
        """
        校检 新旧 ID 一致性
        """
        bus_id_map_company_obj_list_all: List[BusIdMapCompany] = []
        bus_id_map_human_obj_list_all: List[BusIdMapHuman] = []
        bus_id_map_school_obj_list_all: List[BusIdMapSchool] = []
        for public_clean_company_main_obj in public_clean_company_main_obj_list:
            bus_id_map_obj_list = public_clean_company_main_obj.bus_id_map_obj_list
            if not bus_id_map_obj_list:
                continue
            bus_id_map_company_obj_list_all += bus_id_map_obj_list
        for public_clean_human_main_obj in public_clean_human_main_obj_list:
            bus_id_map_obj = public_clean_human_main_obj.bus_id_map_obj
            if bus_id_map_obj:
                bus_id_map_human_obj_list_all.append(bus_id_map_obj)
        for public_clean_school_main_obj in public_clean_school_main_obj_list:
            bus_id_map_obj = public_clean_school_main_obj.bus_id_map_obj
            if bus_id_map_obj:
                bus_id_map_school_obj_list_all.append(bus_id_map_obj)

        # 在 新旧ID映射表 中校验
        bus_id_map_company_diff_all = self.check_bus_id_uniformity_func(
            bus_id_map_company_obj_list_all,
            self.table_bus_id_map_company_obj
        )
        bus_id_map_human_diff_all = self.check_bus_id_uniformity_func(
            bus_id_map_human_obj_list_all,
            self.table_bus_id_map_human_obj
        )
        bus_id_map_school_diff_all = self.check_bus_id_uniformity_func(
            bus_id_map_school_obj_list_all,
            self.table_bus_id_map_human_obj
        )

        # 更新模型中的数据
        for public_clean_company_main_obj in public_clean_company_main_obj_list:
            # 更新 company_obj 模型
            company_obj = public_clean_company_main_obj.company_obj
            if company_obj and bus_id_map_company_diff_all:
                bus_id_new_ex = bus_id_map_company_diff_all.get(company_obj.source_name, {}).get(company_obj.pid, '')
                if bus_id_new_ex:
                    company_obj.pid = bus_id_new_ex
            # 更新 bus_id_map 模型
            bus_id_map_obj_list = public_clean_company_main_obj.bus_id_map_obj_list
            if bus_id_map_obj_list and bus_id_map_company_diff_all:
                for bus_id_map_obj in bus_id_map_obj_list:
                    source_name = bus_id_map_obj.source_name
                    bus_id_old = bus_id_map_obj.bus_id_old
                    bus_id_new_now = bus_id_map_obj.bus_id_new
                    if not bus_id_old:
                        continue
                    bus_id_new_ex = bus_id_map_company_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                    if bus_id_new_ex:
                        bus_id_map_obj.bus_id_new = bus_id_new_ex
            # 更新 CompanyShareholders 模型
            company_shareholders_obj_list = public_clean_company_main_obj.company_shareholders_obj_list
            if company_shareholders_obj_list and (bus_id_map_company_diff_all or bus_id_map_human_diff_all):
                for company_shareholders_obj in company_shareholders_obj_list:
                    source_name = company_shareholders_obj.source_name
                    shareholder_type = company_shareholders_obj.shareholder_type
                    if shareholder_type == 2 and bus_id_map_company_diff_all:
                        bus_id_new_now = company_shareholders_obj.shareholder_id
                        bus_id_new_ex = bus_id_map_company_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                    elif shareholder_type == 1 and bus_id_map_human_diff_all:
                        bus_id_new_now = company_shareholders_obj.shareholder_id
                        bus_id_new_ex = bus_id_map_human_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                    else:
                        continue
                    if bus_id_new_ex:
                        company_shareholders_obj.shareholder_id = bus_id_new_ex
            # 更新 CompanySubsidiary 模型
            company_subsidiary_obj_list = public_clean_company_main_obj.company_subsidiary_obj_list
            if company_subsidiary_obj_list and bus_id_map_company_diff_all:
                for company_subsidiary_obj in company_subsidiary_obj_list:
                    source_name = company_subsidiary_obj.source_name
                    bus_id_new_now = company_subsidiary_obj.subsidiary_id
                    bus_id_new_ex = bus_id_map_company_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                    if bus_id_new_ex:
                        company_subsidiary_obj.subsidiary_id = bus_id_new_ex

        # 更新 Human 模型
        if public_clean_human_main_obj_list and bus_id_map_human_diff_all:
            for company_people_obj in public_clean_human_main_obj_list:
                human_obj = company_people_obj.human_obj
                if human_obj:
                    source_name = human_obj.source_name
                    bus_id_new_now = human_obj.hid
                    bus_id_new_ex = bus_id_map_human_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                    if bus_id_new_ex:
                        human_obj.hid = bus_id_new_ex
                # 更新 bus_id_map 模型
                bus_id_map_obj = company_people_obj.bus_id_map_obj
                if bus_id_map_obj:
                    source_name = bus_id_map_obj.source_name
                    bus_id_old = bus_id_map_obj.bus_id_old
                    bus_id_new_now = bus_id_map_obj.bus_id_new
                    if not bus_id_old:
                        continue
                    bus_id_new_ex = bus_id_map_human_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                    if bus_id_new_ex:
                        bus_id_map_obj.bus_id_new = bus_id_new_ex

        # 更新 School 模型
        if public_clean_school_main_obj_list and bus_id_map_school_diff_all:
            for public_clean_school_main_obj in public_clean_school_main_obj_list:
                school_obj = public_clean_school_main_obj.school_obj
                source_name = school_obj.source_name
                bus_id_new_now = school_obj.sid
                bus_id_new_ex = bus_id_map_school_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                if bus_id_new_ex:
                    school_obj.sid = bus_id_new_ex
                # 更新 bus_id_map 模型
                bus_id_map_obj = public_clean_school_main_obj.bus_id_map_obj
                if bus_id_map_obj:
                    source_name = bus_id_map_obj.source_name
                    bus_id_old = bus_id_map_obj.bus_id_old
                    bus_id_new_now = bus_id_map_obj.bus_id_new
                    if not bus_id_old:
                        continue
                    bus_id_new_ex = bus_id_map_school_diff_all.get(source_name, {}).get(bus_id_new_now, '')
                    if bus_id_new_ex:
                        bus_id_map_obj.bus_id_new = bus_id_new_ex
        return public_clean_company_main_obj_list, public_clean_human_main_obj_list, public_clean_school_main_obj_list
