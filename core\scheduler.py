import logging
import traceback
from typing import Union, Optional
from reader import Reader, ReaderID, ReaderTime
from settings import CONF_GLOBAL
from models import ConfMySQL
from common.database import MysqlConnectionManager
from preprocess import PreprocessBase, preprocess_manager


class SchedulerSingle:
    """单进程调度器"""

    def __init__(self, reader_file: str):
        # 读取配置的基本信息
        self.reader: Reader = Reader(reader_file)
        # 调整配置到特定的任务类型
        if self.reader.conf.split.type == 'id':
            self.reader = ReaderID(reader_file)
        elif self.reader.conf.split.type == 'time':
            self.reader = ReaderTime(reader_file)
        else:
            raise KeyError(f"Error split type, use case id|time, got {self.reader.conf.split.type!r}")
        self.mysql_manager: Optional[MysqlConnectionManager] = None
        self.preprocess: Optional[PreprocessBase] = None

    def init(self):
        """初始化连接池"""
        conn_dict = dict()
        # 读取全局的连接配置
        for table_name in self.reader.conf.process:
            table: Union[ConfMySQL, None] = getattr(CONF_GLOBAL.tables, table_name)
            if isinstance(table, ConfMySQL):
                conn_dict[table.name] = table.max_connections
        # 读取其他的连接配置 - 已过滤不存在的配置
        for other in self.reader.conf.mysql_other:
            conn_dict[other.name] = other.max_connections
        # 读取reader中的连接配置
        conn_dict[self.reader.conf.mysql.name] = self.reader.conf.mysql.max_connections

        # 根据最大连接数初始化连接池
        conf_list = [ConfMySQL(name=k, max_connections=v) for k, v in conn_dict.items()]
        self.mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
        self.mysql_manager.init()
        for k, v in conn_dict.items():
            logging.info(f"init mysql ===== >>>>> {k} max_conn: {v}")
        # 将连接池管理器传给各个对象
        self.reader.init(self.mysql_manager)
        preprocess_fun = preprocess_manager.get_preprocessor(self.reader.conf.preprocess)
        self.preprocess: PreprocessBase = preprocess_fun(self.mysql_manager)

    def worker(self, task: tuple):
        """串行任务"""
        logging.info(f"start:{task[0]!r},end:{task[1]!r}")
        result = None
        try:
            # 此处可扩展为多线程或异步执行 - 单核性能未占满的情况下
            if self.reader.conf.split.type == 'id':
                result = self.worker_id(task)
            elif self.reader.conf.split.type == 'time':
                result = self.worker_time(task)
            else:
                return
        except Exception as e:
            # exc_type, exc_obj, exc_tb = sys.exc_info()
            # logging.warning(f"{exc_type.__name__}: {e}")
            traceback.print_exc()
        status = result if isinstance(result, int) else 0
        if status > 0:
            self.reader.update_record(start=str(task[0]), end=str(task[1]), status=status)

    def worker_id(self, task: tuple):
        """串行自增id分片任务"""
        result = self.reader.read_data(task)
        logging.info(f"length:{result.length}")
        return 1 if result.length == 0 else self.preprocess.main(result.data)

    def worker_id_back(self, task: tuple):
        """串行自增id分片任务 - 弃用"""
        result_set = set()
        for task_sub in self.reader.create_sub_task(*task):
            logging.info(f"start_sub:{task_sub[0]},end_sub:{task_sub[1]}")
            result = self.reader.read_data(task_sub)
            logging.info(f"length:{result.length}")
            if result.length == 0:
                continue
            res = self.preprocess.main(result.data)
            result_set.add(res)
        return 2 if 2 in result_set else 1

    def worker_time(self, task: tuple):
        """串行时间分片任务"""
        aid = 0
        result_set = set()
        while True:
            logging.info(f"start:{task[0]!r},end:{task[1]!r},{self.reader.conf.split.field.id}>{aid}")
            result = self.reader.read_data(task=task, aid=aid)
            if result.length == 0:
                break
            res = self.preprocess.main(result.data)
            result_set.add(res)
            aid = result.data[-1].get(self.reader.conf.split.field.id)
        return 2 if 2 in result_set else 1

    def close(self):
        self.mysql_manager.close()
