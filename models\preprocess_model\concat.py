#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: concat.py
@Date: 2024/4/12
@Desc: 
@Server: 
"""
from typing import List
from dataclasses import dataclass
from public.public_funcs import public_funcs_obj
from .public import check_attributes


@dataclass
class PreprocessEmail:
    """
    预处理方法 邮箱 模型
    """
    email: str  # 邮箱
    domain: str = ''  # 域名
    is_mx: int = 0  # 是否邮件服务器;0-未检测，1-是，2-否，3-不确定
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    score: int = 0  # 评分
    reason: str = ''  # 原因
    source_name: str = ''  # 来源

    def __post_init__(self):
        self.domain = self.email.split('@')[0]

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessPhone:
    """
    预处理方法 电话 模型
    """
    phone_raw: str  # 原始电话
    phone: str = ''  # 纯数字号码
    phone_type: int = 0  # 号码类型，0：未检测，1：固定电话，2：移动电话，3：已检测但未知
    country_code: str = ''  # 国家二字码
    dialing_code: str = ''  # 国际冠码
    area_code: str = ''  # 地区码
    telephone: str = ''  # 号码(去除冠码与区码)
    national_number: str = ''  # 号码属国格式
    international_number: str = ''  # 国际格式号码
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    is_ws: int = 0  # 是否WhatsApp;0-未检测，1-是，2-否，3-不确定
    source_name: str = ''  # 来源

    def __post_init__(self):
        self.phone = public_funcs_obj.retain_numbers(self.phone_raw)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessSocial:
    """
    预处理方法 社媒 模型
    """
    social_url: str  # 社媒链接; https://www.facebook.com/abs
    social_type: str  # 社媒类型;linkedin,facebook,twitter,youtube,instagram,pinterest,github,tiktok
    domain: str = ''  # 域名
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    source_name: str = ''  # 来源

    def __post_init__(self):
        if not self.domain:
            self.domain = public_funcs_obj.clean_website(self.social_url)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessWebsite:
    """
    预处理方法 网址 模型
    """
    website: str  # 网址
    domain: str = ''  # 域名
    is_valid: int = 0  # 是否有效;0-未检测，1-是，2-否，3-不确定
    is_sensitive: int = 0  # 是否敏感;0-未检测，1-是，2-否，3-不确定
    detail_reason: str = ''  # 主要问题原因
    reason: str = ''  # 原因
    source_name: str = ''  # 来源

    def __post_init__(self):
        self.domain = public_funcs_obj.clean_website(self.website)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessConcatMain:
    """
    预处理方法 联系方式 模型
    """
    email_obj_list: List[PreprocessEmail] = None  # 邮箱信息
    phone_obj_list: List[PreprocessPhone] = None  # 电话信息
    social_obj_list: List[PreprocessSocial] = None  # 社媒信息
    website_obj_list: List[PreprocessWebsite] = None  # 网址信息
