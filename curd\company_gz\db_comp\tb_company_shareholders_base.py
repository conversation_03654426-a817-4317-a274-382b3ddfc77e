#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_shareholders_base.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence


class TableCompanyShareholdersBase(TableBase):
    db = 'db_comp'
    table = 'company_shareholders_base'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_shareholders(self, cids: list) -> dict[str, Sequence[dict]]:
        """
        获取公司股东
        :param cids: 公司ID列表
        :return:
        """
        if not cids:
            return {}
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select pid,shareholder_pid,uci,shareholder_direct,shareholder_total from {self.db}.{self.table} where pid in ({bfhs_str})
        '''
        data_src = self.db_mysql.read(sql, cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        shareholders_data: dict = {}
        for result in results:
            pid = result.get('pid', '')
            shareholder_pid = result.get('shareholder_pid', '')
            uci = result.get('uci', '')
            shareholder_direct = result.get('shareholder_direct', '')
            shareholder_total = result.get('shareholder_total', '')
            shareholder_type = 0
            bus_id_old = ''
            if shareholder_pid:
                shareholder_type = 2
                bus_id_old = shareholder_pid
            if not bus_id_old and uci:
                shareholder_type = 1
                bus_id_old = uci
            if not pid or not bus_id_old:
                continue
            shareholder_direct = shareholder_direct if shareholder_direct else ''
            shareholder_total = shareholder_total if shareholder_total else ''
            if shareholder_direct == '-' or shareholder_direct.lower() == 'n.a.':
                shareholder_direct = ''
            if shareholder_total == '-' or shareholder_total.lower() == 'n.a.':
                shareholder_total = ''
            data = {'cid_source': pid, 'bus_id_old': bus_id_old, 'shareholder_type': shareholder_type, 'shareholder_direct': shareholder_direct, 'shareholder_total': shareholder_total}
            if pid not in shareholders_data:
                shareholders_data[pid] = []
            shareholders_data[pid].append(data)
        return shareholders_data
