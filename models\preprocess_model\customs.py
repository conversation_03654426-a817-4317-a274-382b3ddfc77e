import re

from typing import List
from pydantic import BaseModel, validator, root_validator
from .concat import PreprocessEmail
from .concat import PreprocessPhone
from .concat import PreprocessWebsite
from .public import PreprocessAddress
from .concat import PreprocessSocial


class PreprocessCustomsCompanyMain(BaseModel):
    """预处理方法 海关公司"""
    source: int  # 来源平台，1：wmb，4：dingyi，5：importyeti，6：usimports，7：imporinfo
    openid: str  # 来源平台id（备注：source=1时，此字段为唯一键）
    name: str  # 名称
    uniqueid: str = ''
    name_new: str = ''
    logo: str = ''  # 图标
    introduce: str = ''  # 介绍
    industry: str = ''  # 行业
    scope: str = ''  # 经营范围
    attach: str = ''  # 额外关键字
    texture: str = ''  # introduce industry scope attach
    location: str = ''  # 经纬度
    country_id: int = 0  # 国id
    province_id: int = 0  # 省id
    city_id: int = 0  # 市id
    country: str = ''  # 国名称
    province: str = ''  # 省名称
    city: str = ''  # 市名称
    postcode: str = ''  # 邮编
    address: List[PreprocessAddress] = None  # 地址
    email: List[PreprocessEmail] = None  # 邮箱
    website: List[PreprocessWebsite] = None  # 网址
    phone: List[PreprocessPhone] = None  # 电话
    is_ws: int = -1  # 是否是whatsapp，-1：未知，0：不是，1：是
    fburl: List[PreprocessSocial] = None  # Facebook
    tturl: List[PreprocessSocial] = None  # Twitter
    isurl: List[PreprocessSocial] = None  # Instagram
    liurl: List[PreprocessSocial] = None  # LinkedIn
    yturl: List[PreprocessSocial] = None  # YouTube
    pturl: List[PreprocessSocial] = None
    tkurl: List[PreprocessSocial] = None  # Tiktok
    register_person: str = ''  # 法人
    register_number: str = ''  # 注册号
    register_date: str = ''  # 注册日期
    register_type: str = ''  # 注册类型
    register_capital: str = ''  # 注册资金
    register_state: str = ''  # 状态
    company_type: int = 0  # 公司类型，0：未知，1：seller，2：buyer，3：seller and buyer
    repair_method: int = 0  # 修补方式，0：原始数据，1：缺失原始数据可以补充，2：depth，3：linkedin


class PreprocessCustomsTradeMain(BaseModel):
    """
    预处理方法 海关贸易 模型
    """
    trade_id: int  # 贸易ID
    trade_date: int  # 交易日期
    source: int = 0  # 平台编号，1：wmb，4：dy，5：importyeti，6：usimport
    openid: str = ''  # 平台唯一编号
    trade_code: str = ''  # 提关单号
    weight: int = 0  # 重量
    weight_unit: str = ''  # 重量单位
    quantity: int = 0  # 数量
    quantity_unit: str = ''  # 数量单位
    price: float = 0  # 单价
    amount: float = 0  # 总价
    amount_unit: str = ''  # 价格单位
    product_country: str = ''  # 原产国名称
    product_country_code: str = ''  # 原产国二字码
    product_desc: str = ''  # 产品描述
    product_tag: str = ''  # 产品标签
    hscode: str = ''  # 海关编码
    seller_id: int = 0  # 供应商编号
    seller: str = ''  # 供应商名字
    seller_country_code: str = ''  # 供应商所属国二字码
    seller_country: str = ''  # 供应商所属国名称
    origin_country: str = ''    # 起运国
    origin_country_code: str = ''  # 起运国二字码
    origin_port: str = ''  # 起运港
    buyer_id: int = 0   # 采购商编号
    buyer: str = ''  # 采购商
    buyer_country_code: str = ''  # 采购商所属国二字码
    buyer_country: str = ''  # 采购商所属国名称
    arrival_country: str = ''   # 抵运国
    arrival_country_code: str = ''   # 抵运国code
    arrival_port: str = ''  # 抵运港
    notifier: str = ''  # 联系人
    container: str = ''  # 货柜
    transport: str = ''  # 运输方式
    trade_type: int = 4  # 贸易类型，0为进口数据，1为出口数据，2为过境数据，3为国内数据，4为未知
    create_time: str = ''
    update_time: str = ''
    seller_company_info: PreprocessCustomsCompanyMain = None  # 供应商公司信息
    buyer_company_info: PreprocessCustomsCompanyMain = None  # 采购商公司信息

    @root_validator(pre=False)
    def _transform_patch_up(cls, values):
        """清洗 一些需要映射打补丁字段"""
        # 国家 补丁
        country_clean_map_dict = {
            "other": "",
            "Logistic Dataset": "",
            "None": "",
            "Mid": "",
            "Yugoslavia": "",
            "European union": "",
            "england": "United Kingdom",
            "Uruguay Bill": "United Kingdom",
            "The Dominican Rep.": "Dominican Republic",
            "Venezuela Bill": "Venezuela",
            "Netherlands Antilles": "Netherlands",
            "Burma": "Myanmar",
            "D.p.r.korea": "North Korea",
            "Cote D'ivoire": "Cote D'Ivoire (Ivory Coast)",
            "Bosnia - Herzegovina": "Bosnia and Herzegovina",
            "Surinam": "Suriname",
            "The Republic Of Congo": "Democratic Republic of the Congo",
            "The Democratic Republic Of The Congo": "Democratic Republic of the Congo",
            "The dominican rep.": "Dominican Republic",
            "British virgin islands": "Virgin Islands (British)",
            "Virgin islands (u.s.) ": "Virgin Islands (US)",
        }
        country_cols = ["seller_country", "buyer_country", "product_country", "origin_country", "arrival_country"]
        for k, v in country_clean_map_dict.items():
            for col in country_cols:
                if values[col] and values[col].strip().lower() == k.lower():
                    values[col] = v
        # 单位 补丁
        unit_clean_map_dict = {
            "other": ""
        }
        unit_cols = ['quantity_unit', 'weight_unit', 'amount_unit']
        for k, v in unit_clean_map_dict.items():
            for col in unit_cols:
                if values[col] and values[col].strip().lower() == k.lower():
                    values[col] = v
        # 港口补丁
        port_clean_map_dict = {
            "other": ""
        }
        port_cols = ['origin_port', 'arrival_port', 'transport']
        for k, v in port_clean_map_dict.items():
            for col in port_cols:
                if values[col] and values[col].strip().lower() == k.lower():
                    values[col] = v

        quantity = values['quantity']
        weight = values['weight']
        values['quantity'] = cls.transform_float(quantity)
        values['weight'] = cls.transform_float(weight)
        return values

    @staticmethod
    def transform_company_name(name: str):
        if name == "":
            return name
        name = name.strip().upper()
        return name

    @validator('seller')
    def _transform_seller(cls, v):
        return cls.transform_company_name(v)

    @validator('buyer')
    def _transform_buyer(cls, v):
        return cls.transform_company_name(v)

    @validator('trade_date')
    def _transform_trade_date(cls, v):
        return int(str(v)[:10])

    @classmethod
    def extract_quantity_weight(cls, quantity: int, weight: int):
        """提取数量、重量、单位"""
        quantity = str(quantity)
        weight = str(weight)
        quantity_unit, weight_unit = '', ''
        if len(quantity_list := quantity.split(' ')) > 1:
            quantity = quantity_list[0]
            quantity_unit = quantity_list[1]
        if len(weight_list := weight.split(' ')) > 1:
            weight = weight_list[0]
            weight_unit = weight_list[1]
        quantity = cls.transform_float(quantity)
        weight = cls.transform_float(weight)
        return quantity, weight, quantity_unit, weight_unit

    @validator('price')
    def _transform_price(cls, v):
        return cls.transform_float(v)

    @validator('amount')
    def _transform_amount(cls, v):
        return cls.transform_float(v)

    @staticmethod
    def transform_float(src: str):
        res = 0
        src = str(src).replace(',', '')
        if src != '':
            try:
                res = round(float(src))
            except ValueError:
                res = 0
        return res


if __name__ == '__main__':
    a = PreprocessCustomsTradeMain(trade_date=1643212800)
    print(a)
