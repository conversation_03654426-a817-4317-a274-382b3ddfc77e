#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_area.py
@Date: 2024/2/26
@Desc: 获取地区相关数据
@Server:
"""
from curd.tb_base import TableBase
from typing import Sequence


class TableArea(TableBase):
    db = 'db_customs'
    country_table = 'c_area_country'
    province_table = 'c_area_province'
    city_table = 'c_area_city'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_country_id_map(self) -> dict:
        """
        通过 国家id/code 获取 国家 编码
        :return:
        """
        sql = f'''
            select `id`, `name_en` as country, `code` as country_iso_code, `name` as country_cn,
            `name_local` as country_local, `code_three` as country_iso_code3
             from {self.db}.{self.country_table}
        '''
        data_src = self.db_mysql.read(sql, return_dict=True)
        results: Sequence[dict] = data_src.data
        country_code_map = {
            0: {
                'country_en': "", 'country_cn': "",
                'country_iso_code': "",
                'country_local': "",
                'country_iso_code3': ""}
        }
        for data in results:
            aid = data.get('id', '')
            country = data.get('country', '')
            country_iso_code = data.get('country_iso_code', '')
            country_cn = data.get('country_cn', '')
            country_local = data.get('country_local', '')
            country_iso_code3 = data.get('country_iso_code3', '')
            country_code_map[aid] = {
                'country_en': country, 'country_cn': country_cn,
                'country_iso_code': country_iso_code,
                'country_local': country_local,
                'country_iso_code3': country_iso_code3}
            country_code_map[country_iso_code.lower()] = {
                'id': aid,
                'country_en': country, 'country_cn': country_cn,
                'country_iso_code': country_iso_code,
                'country_local': country_local,
                'country_iso_code3': country_iso_code3}
        return country_code_map
