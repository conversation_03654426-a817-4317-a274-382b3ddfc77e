#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_linekdin_person_experience.py
@Date: 2024/5/7
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence, Dict
from public.public_funcs import public_funcs_obj


class TableLinkPersonExperience(TableBase):
    db = 'db_link'
    table = 'linekdin_person_experience'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_person_director(self, hids: list = None, cids: list = None) -> Dict[str, Dict]:
        """
        获取人物任职信息
        :param hids: 人物ID列表
        :param cids: 公司ID列表
        :return: {"company": {公司ID: []}, "human": {人物ID: []}}
        """
        if not hids and not cids:
            return {}
        value = []
        sql = f'''
            select job_company_id,lid,full_name,job_company_name,country_ios_code,
            job_company_location_country,job_company_location_region,job_company_location_locality,
            start_date, end_date,title_name,title_role,title_sub_role,title_levels,
            summary, is_primary
            from {self.db}.{self.table}
        '''
        w_sqls = []
        if hids:
            bfhs_str_h = ','.join(['%s'] * len(hids))
            value += hids
            w_sqls.append(f'lid in ({bfhs_str_h})')
        if cids:
            bfhs_str_c = ','.join(['%s'] * len(cids))
            value += cids
            w_sqls.append(f'job_company_id in ({bfhs_str_c})')
        w_sqls_str = ' and '.join(w_sqls)
        sql += f' where {w_sqls_str}'
        data_src = self.db_mysql.read(sql, value=value, return_dict=True)
        results: Sequence[dict] = data_src.data
        person_director_data: dict = {"company": {}, "human": {}}
        for result in results:
            pid = result.get('job_company_id', '')
            uci = result.get('lid', '')
            full_name = result.get('full_name', '')
            job_company_name = result.get('job_company_name', '')
            originalpost_en = result.get('title_name', '')
            country_ios_code = result.get('country_ios_code', '')
            job_company_location_country = result.get('job_company_location_country', '')
            job_company_location_region = result.get('job_company_location_region', '')
            job_company_location_locality = result.get('job_company_location_locality', '')
            start_date = result.get('start_date', '')
            end_date = result.get('end_date', '')
            title_role = result.get('title_role', '')
            title_sub_role = result.get('title_sub_role', '')
            title_levels = result.get('title_levels', '')
            summary = result.get('summary', '')
            is_primary = result.get('is_primary', '')
            if not uci or not pid or not full_name:
                continue
            cid_source = pid
            hid_source = uci
            human_type = 1
            if is_primary and is_primary.lower() == 'true':
                post_status = 1
            else:
                post_status = 0
            human_name = full_name.lower().strip()
            human_name_sp = human_name.split(' ')
            if human_name_sp[0] in ['mr', 'mrs', 'mr.', 'mrs.']:
                human_name_sp = human_name_sp[1:]
            if human_name_sp and human_name_sp[-1] in ['n/a']:
                human_name_sp = human_name_sp[:-1]
            if not human_name_sp:
                continue
            human_name = ' '.join(human_name_sp).strip()
            originalpost_en = originalpost_en if originalpost_en else ''
            title_name = originalpost_en.lower().strip() if originalpost_en else ''
            # start_date、end_date: 2017 | 2008-04 | 2013-11-01
            start_date_str = ''
            end_date_str = ''
            if start_date:
                start_date_str = public_funcs_obj.normalize_date(start_date)
            if end_date:
                end_date_str = public_funcs_obj.normalize_date(end_date)
            data = {'cid_source': cid_source, 'hid_source': hid_source, 'human_name': human_name,
                    'human_type': human_type, 'start_date_str': start_date_str, 'end_date_str': end_date_str,
                    'title_name': title_name, 'title_role': title_role, 'title_sub_role': title_sub_role,
                    'summary': summary, 'title_levels': title_levels, 'post_status': post_status,
                    'job_company_name': job_company_name,
                    'job_company_location_country': job_company_location_country,
                    'job_company_location_region': job_company_location_region,
                    'job_company_location_locality': job_company_location_locality,
                    'country_ios_code': country_ios_code,
                    }
            if pid not in person_director_data['company']:
                person_director_data['company'][pid] = []
            person_director_data['company'][pid].append(data)
            if hid_source not in person_director_data['human']:
                person_director_data['human'][hid_source] = []
            person_director_data['human'][hid_source].append(data)
        return person_director_data
