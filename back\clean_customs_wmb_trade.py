#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import hashlib
from typing import Optional, List
import pytz
from common.database import MysqlConnectionManager
from preprocess import PreprocessBase
from back.clean_customs_trade_real import CleanCustomsTrade
from settings import *
from curd import TableArea
from curd.customs_gz.db_customs.t_company import TableCompany
from curd.customs_gz.db_customs.tb_haiguan import TableHaiGuan
from curd.customs_new_gz.src_wmb import company
import re


class CleanCustomsWmbTrade(PreprocessBase):
    preprocess_name = os.path.basename(__file__).split('.')[0]

    def __init__(self, mysql_manager: MysqlConnectionManager, **kwargs):
        super().__init__(mysql_manager, **kwargs)
        self.mysql_obj_customs_new_gz = mysql_manager.get_pool("customs_new_gz")
        self.mysql_obj_company_gz = mysql_manager.get_pool("company_gz")
        self.mysql_obj_customs_gz = mysql_manager.get_pool("customs_gz")
        self.customs_db = "src_wmb"
        self.customs_trade_table = "trade"
        self.table_Area_obj = TableArea(self.mysql_obj_company_gz)
        self.get_country_map = self.table_Area_obj.get_country_map()
        self.table_o_c_obj = TableCompany(self.mysql_obj_customs_new_gz)
        self.table_hg_obj = TableHaiGuan(self.mysql_obj_customs_new_gz)
        self.black_name = {"NOT AVAILABLE", "UNKNOW", "NO DISPONIBLE", "N A", "N/A"}
        self.regex_email = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.regex_phone = re.compile(
            r'(?:\+?\d{1,3}[-\s.]?)?(?:\(?\d{1,4}\)?[-\s.]?)?\d{1,4}[-\s.]?\d{1,4}(?:[-\s.]\d{1,9})?')
        self.regex_split_hs = re.compile(r'[, ]+')
        self.regex = re.compile('[^A-Z]')
        self.source = 1
        self.shanghai_tz = pytz.timezone('Asia/Shanghai')
        # 读取港口映射
        self.mapping_port = self.table_hg_obj.read_customs_port_map()
        # 单位映射
        self.mapping_quantity, self.mapping_weight = self.table_hg_obj.read_mapping_unit()
        self.trade_clean_obj = CleanCustomsTrade(mysql_manager)
        self.src_table_obj = company.TableCompany(self.mysql_obj_customs_new_gz)

    def create_uuid_sid(self, data):
        """生成uuid和sid"""
        data_list = [str(d).strip().lower() for d in data]
        uuid = hashlib.md5(''.join(data_list).encode()).hexdigest()
        sid = hashlib.md5(''.join(data_list[:-3]).encode()).hexdigest()
        return uuid, sid

    def transform_port(self, raw):
        new_port = raw.strip().upper()
        if new_port == '-':
            new_port = ''
        elif new_port == 'N/A':
            new_port = ''
        elif new_port.isdigit():
            new_port = ''
        if len(new_port) > 100:
            new_port = ''
        if new_port != '':
            port = self.mapping_port.get(new_port)
            if port is not None:
                new_port = port
        return new_port

    def transform_company_name(self, name: str):
        if name == "":
            return name
        name = name.strip().upper()
        for black in self.black_name:
            if name.startswith(black):
                return ""
        return name

    def transform_unit(self, raw, mapping):
        src = raw.upper().strip()
        res = self.regex.sub('', raw)
        return '' if res == '' else mapping.get(res, src)

    def transform_hs_code(self, src):
        hs_code_list = self.regex_split_hs.split(src)
        res_list = [x.replace(".", "") for x in hs_code_list if "E+" not in x.upper()]
        return ', '.join(res_list)

    def extract_emails(self, text: str):
        res = self.regex_email.findall(text)
        return ','.join(res) if len(res) > 0 else ''

    def extract_phones(self, text: str):
        res = self.regex_phone.findall(text)
        return ','.join(res) if len(res) > 0 else ''

    def deal_company(self, data_list) -> List:
        cur_id_set = set()
        company_items = list()
        for data in data_list:
            company_id, name, business, country, address, manager, telephone, fax, email, website = data
            name = self.transform_company_name(name)
            if name == '':  # 没有名字的公司没有意义
                return []
            openid = company_id  # 平台唯一key
            # 去重
            if openid in cur_id_set:
                return []
            introduce = ''  # text类型
            industry = ''
            scope = business  # text类型
            attach = ''  # text类型
            texture = ''  # text类型
            standard = self.get_country_map.get(country.lower(), dict())
            country_id = standard.get('id', 0)
            country = standard.get('name_en', '')
            # uniqueid = f"{openid}-{country_id}"
            uniqueid = f"{name}-{country_id}"  # 并不是唯一key
            phone = telephone

            # 处理邮箱
            email_new = self.extract_emails(email)
            phone_new = self.extract_phones(phone)

            company_items.append({
                "source": self.source,
                "openid": str(openid),
                "uniqueid": uniqueid,
                "name": name,
                "introduce": introduce,
                "industry": industry,
                "scope": scope,
                "attach": attach,
                "texture": texture,
                "country_id": country_id,
                "country": country,
                "province": "",
                "province_id": 0,
                "city": "",
                "city_id": 0,
                "address": address,
                "phone": phone_new,
                "email": email_new,
                "website": website
            })
        return company_items

    def deal_trade(self, data_raw) -> Optional[list]:
        if len(data_raw) == 0:
            return
        insert_list = list()
        cur_id_set = set()
        openid_list = set()
        start_ = time.time()
        for data in data_raw:
            amount, bill_no, buyer, buyer_country, buyer_id_std, buyer_port, container, _date, descript, \
                descript_label, hs, _id, notify_name, origin_country, qty, qty_unit, seller, \
                seller_country, seller_id_std, seller_port, trans, weight, weight_unit = data
            openid_list.add(str(seller_id_std))
            openid_list.add(str(buyer_id_std))
        wmb_customs_company_map = self.deal_company_datalist(list(openid_list))

        for data in data_raw:
            amount, bill_no, buyer, buyer_country, buyer_id_std, buyer_port, container, _date, descript, \
                descript_label, hs, _id, notify_name, origin_country, qty, qty_unit, seller, \
                seller_country, seller_id_std, seller_port, trans, weight, weight_unit = data
            seller_id_std = str(seller_id_std)
            buyer_id_std = str(buyer_id_std)
            # 源网站id
            openid = _id
            if openid == "":
                continue
            # 去重，因分表去重，故可以使用平台唯一key
            # if openid in cur_id_set or self._exists_trade(openid):
            #     continue
            # 结果表唯一key
            uniqueid = f"{self.source}-{openid}"
            # 提单号
            trade_code = bill_no
            # 时间戳
            d_shanghai = self.shanghai_tz.localize(_date)
            trade_date = int(d_shanghai.timestamp()) * 1000
            # 重量
            weight_res = 0
            if weight != '':
                try:
                    weight_res = round(float(weight))
                except ValueError:
                    weight_res = 0
            # 数量
            quantity = 0
            if qty != '':
                try:
                    quantity = round(float(qty))
                except ValueError:
                    quantity = 0
            # 数量单位
            quantity_unit = self.transform_unit(qty_unit, self.mapping_quantity)
            # 重量单位
            weight_unit = self.transform_unit(weight_unit, self.mapping_weight)
            # 总价
            amount_res = 0
            if amount != '':
                try:
                    amount_res = round(float(amount))
                except ValueError:
                    amount_res = 0
            if amount_res > 10000000000000:  # 过滤价格的异常值
                continue
            # 单价
            price = 0 if quantity == 0 else round(amount_res / quantity)
            # 价格单位
            amount_unit = ''  # wmb没有这个字段
            # 国家映射
            # 原产国
            product_info = self.get_country_map.get(origin_country.lower(), dict())
            product_country_id = product_info.get('id', 0)
            product_country = product_info.get('en', '')
            # 供应国
            seller_info = self.get_country_map.get(seller_country.lower(), dict())
            seller_country_id = seller_info.get('id', 0)
            seller_country = seller_info.get('en', '')
            # 采购国
            buyer_info = self.get_country_map.get(buyer_country.lower(), dict())
            buyer_country_id = buyer_info.get('id', 0)
            buyer_country = buyer_info.get('en', '')
            # 产品描述
            product_desc = descript
            # 产品标签
            product_tag = descript_label
            # 产品hscode
            product_hscode = self.transform_hs_code(hs)
            # 源id  放置公共清洗中获取
            # seller_id = o_c_map.get(seller_id_std, 0)
            # buyer_id = o_c_map.get(buyer_id_std, 0)

            # seller
            seller = self.transform_company_name(seller)
            buyer = self.transform_company_name(buyer)

            if not seller and not buyer:
                continue

            # 。。。
            notifier = notify_name
            # 中转
            transport = trans

            # 出口港
            seller_port = self.transform_port(seller_port)
            # 进口港
            buyer_port = self.transform_port(buyer_port)

            # 新版去重方案
            weight_res = round(float(weight_res), 2)
            quantity = round(float(quantity), 2)
            amount_res = round(float(amount_res), 2)
            uuid_data = [
                trade_date, seller, seller_country_id, seller_port, buyer, buyer_country_id, buyer_port,
                product_desc, weight_res, quantity, amount_res
            ]
            uuid, sid = self.create_uuid_sid(uuid_data)

            if uuid in cur_id_set:
                continue

            cur_id_set.add(uuid)

            insert_list.append({
                "trade_id": 0,
                "source": self.source,
                "openid": openid,
                "uniqueid": uniqueid,
                "trade_code": trade_code,
                "trade_date": trade_date,
                "weight": weight_res,
                "weight_unit": weight_unit,
                "quantity": quantity,
                "quantity_unit": quantity_unit,
                "price": price,
                "amount": amount_res,
                "amount_unit": amount_unit,
                "product_country_id": product_country_id,
                "product_country": product_country,
                "product_desc": product_desc,
                "product_tag": product_tag,
                "product_hscode": product_hscode,
                "seller_id": 0,
                "seller_openid": str(seller_id_std),
                "seller": seller,
                "seller_country_id": seller_country_id,
                "seller_country": seller_country,
                "seller_port": seller_port,
                "buyer_id": 0,
                "buyer_openid": str(buyer_id_std),
                "buyer": buyer,
                "buyer_country_id": buyer_country_id,
                "buyer_country": buyer_country,
                "buyer_port": buyer_port,
                "notifier": notifier,
                "container": container,
                "transport": transport,
                "seller_info": wmb_customs_company_map.get(seller_id_std, {}),
                "buyer_info": wmb_customs_company_map.get(buyer_id_std, {}),
            })
        return insert_list

    def deal_company_datalist(self, openid_list: list):
        company_info = self.src_table_obj.get_company_info(openid_list)
        wmb_customs_company_data_list = self.deal_company(company_info)
        wmb_customs_company_map = {}
        for one in wmb_customs_company_data_list:
            wmb_customs_company_map[one["openid"]] = one
        return wmb_customs_company_map

    def deal_trade_datalist(self, data_list: list):
        s_t = time.time()
        wmb_customs_trade_data_list = self.deal_trade(data_list)
        if not wmb_customs_trade_data_list:
            return
        logger.info(f'end 《wmb》 preprocess deal datalist === hs: {time.time() - s_t:.2f} s')
        # 执行贸易清洗
        self.trade_clean_obj.deal_datalist(wmb_customs_trade_data_list)

    def test_unit(self):
        # sql = f'''select * from {self.customs_db}.{self.customs_trade_table} order by aid limit 1000'''
        global start_ok
        print(f'''初始化耗时：{int(time.time()-start_ok)}''')
        fields = [
            'amount', 'bill_no', 'buyer', 'buyer_country', 'buyer_id_std', 'buyer_port', 'container', 'date',
            'descript', 'descript_label', 'hs', 'id', 'notify_name', 'origin_country', 'qty', 'qty_unit',
            'seller', 'seller_country', 'seller_id_std', 'seller_port', 'trans', 'weight', 'weight_unit'
        ]
        sql = f"select {', '.join(fields)} from {self.customs_db}.{self.customs_trade_table} order by aid limit 1000"
        # sql = f"select {', '.join(fields)} from {self.customs_db}.{self.customs_trade_table} where id=6667897668"
        data_src = self.mysql_obj_customs_gz.read(sql, return_dict=False)
        results: List[dict] = data_src.data
        self.deal_trade_datalist(results)


if __name__ == '__main__':
    from models import ConfMySQL

    conf_list = [
        ConfMySQL(name='customs_new_gz', max_connections=1),
        ConfMySQL(name='customs_gz', max_connections=1),
        ConfMySQL(name='company_gz', max_connections=1),
    ]
    startdd_ = time.time()
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    print(f'''数据库连接耗时：{int(time.time()-startdd_)}''')
    start_ok = time.time()
    CleanCustomsWmbTrade(mysql_manager).test_unit()
