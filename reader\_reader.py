import logging
from typing import Union, Tuple, Any, Optional
from models import ConfReader, MysqlResult
from ._conf import ConfInit
from common.database import MysqlConnectionManager, DBPoolMysql
from curd import TableRecord
from common.utils import str2datetime, datetime2str_plus
from settings import CONF_GLOBAL


class Reader:
    def __init__(self, file):
        # 初始化配置文件
        self.conf: ConfReader = ConfInit(file).init()
        # 初始化变量
        self.mysql_manager: Union[MysqlConnectionManager, None] = None
        self.table_record: Union[TableRecord, None] = None
        self.db_mysql: Union[DBPoolMysql, None] = None

    def init(self, mysql_manager: MysqlConnectionManager):
        """初始化数据库连接"""
        self.mysql_manager: MysqlConnectionManager = mysql_manager
        self.table_record: TableRecord = TableRecord(self.mysql_manager.get_pool('company_gz'))
        self.db_mysql: DBPoolMysql = self.mysql_manager.get_pool(self.conf.mysql.name)

    def _init_task(self):
        """更新任务记录"""
        pass

    def read_data(self, *args, **kwargs) -> list:
        """根据分片读取数据"""
        pass

    def create_sub_task(self, start: int, end: int):
        """根据分片创建"""
        yield from self.db_mysql.id_iterator(start, end, self.conf.split.batch_size)

    def create_task(self):
        """创建任务分片"""
        self._init_task()
        return self.read_task()

    def read_task(self):
        """读取未完成的任务"""
        split_type = self.conf.split.type
        if split_type == 'id':
            column_type = 'int'
        elif split_type == 'time':
            column_type = 'time'
        else:
            column_type = None
        return self.table_record.read_record(name=self.conf.name, column_type=column_type)

    def update_record(self, start: str, end: str, status: int = 1) -> MysqlResult:
        """
        更新分片任务状态
        :param start: 任务分片起始位置
        :param end: 任务分片结束位置
        :param status: 任务执行情况
        :return: MysqlResult
        """
        item = {"name": self.conf.name, "start": start, "end": end, "status": status}
        return self.table_record.save_record(item)

    def __repr__(self):
        return f"{self.conf.__dict__}"


class ReaderID(Reader):
    def _init_task(self):
        """根据自增id来更新任务记录表"""
        # 读取历史记录
        logging.info("读取历史记录...")
        max_record = self.table_record.read_record_max(self.conf.name, column_type='int')
        if max_record == '':
            max_record = 1
        elif isinstance(max_record, int):
            max_record += 1
        elif isinstance(max_record, str) and not max_record.isdigit():
            raise TypeError(f"错误的历史记录(id)：{max_record}")
        else:
            max_record = int(max_record) + 1
        # print(f"max_record:{max_record}")
        # 读取当前记录
        logging.info("读取当前记录...")
        min_id, max_id = self.db_mysql.read_id_range(
            self.conf.query.table, self.conf.split.field.id, where=self.conf.query.where)
        min_id = max(max_record, min_id)
        # 分片
        if min_id > max_id:
            return
        # logging.info(f"min_id:{min_id},max_id:{max_id},batch_size:{CONF_GLOBAL.split_size}")
        # tasks = self.db_mysql.id_iterator(min_id, max_id, batch_size=CONF_GLOBAL.split_size)
        batch_size = self.conf.split.batch_size
        logging.info(f"min_id:{min_id},max_id:{max_id},batch_size:{batch_size}")
        tasks = self.db_mysql.id_iterator(min_id, max_id, batch_size=batch_size)
        # 记录表新增任务
        items = [{"name": self.conf.name, "start": task[0], "end": task[1]} for task in tasks]
        logging.info(f"记录表新增任务({len(items)})条...")
        self.table_record.save_record(items, ignore=True)

    def read_data(self, task: Tuple[Any, Any]) -> MysqlResult:
        """根据分片读取数据"""
        start, end = task
        # 拼接sql
        sql = self.conf.query.sql
        if self.conf.query.where is None:
            sql += " where"
        else:
            sql += " and"
        sql += f" {self.conf.split.field.id} between {start} and {end}"
        logging.debug(sql)
        # 读取数据
        return self.db_mysql.read(sql, return_dict=True)


class ReaderTime(Reader):
    def _init_task(self):
        """按时间来分割任务"""
        # 读取历史记录
        logging.info("读取历史记录...")
        max_record = self.table_record.read_record_max(self.conf.name)
        # print(max_record)
        if max_record != '':
            if (r := str2datetime(max_record)) is None:
                raise TypeError(f"错误的历史记录(time)：{max_record}")
            # +1秒并转str
            max_record = datetime2str_plus(r, seconds=1)
            where_str = f"{self.conf.split.field.time} >= {max_record!r}"
            if self.conf.query.where is not None:
                where_str = self.conf.query.where + ' and ' + where_str
        else:
            where_str = self.conf.query.where
        # 读取当前记录
        logging.info("读取当前记录...")
        result = self.db_mysql.read_min_max_value(
            self.conf.query.table, self.conf.split.field.time, where=where_str, column_type='time', total=True)
        if result.length == 0:
            return
        res_data = result.data[0]
        min_time, max_time, total = res_data.get('min_val', ''), res_data.get('max_val', ''), res_data.get('count_num', 0)
        # None 转 ''
        min_time = max(max_record, min_time)
        # 分片
        if min_time > max_time:
            return
        logging.info(f"min_time:{min_time},max_time:{max_time},split_size:{CONF_GLOBAL.split_size}")
        tasks = self.table_record.iterator_column_time(min_time, max_time, total, CONF_GLOBAL.split_size)
        # 记录表新增任务
        items = [{"name": self.conf.name, "start": task[0], "end": task[1]} for task in tasks]
        logging.info(f"记录表新增任务({len(items)})条...")
        self.table_record.save_record(items, ignore=True)

    def read_data(self, task: tuple, aid: int = 0, limit: Optional[int] = None) -> MysqlResult:
        """根据分片读取数据"""
        start, end = task
        # 拼接sql
        sql = self.conf.query.sql
        if self.conf.query.where is None:
            sql += " where"
        else:
            sql += " and"
        column_time = self.conf.split.field.time
        column_id = self.conf.split.field.id
        if limit is None:
            limit = self.conf.split.batch_size
        sql += f" {column_time} between {start!r} and {end!r} and {column_id}>{aid} limit {limit}"
        logging.debug(sql)
        # 读取数据
        return self.db_mysql.read(sql, return_dict=True)
