#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
@File: patent.py
@Date: 2025-01-28
@Desc: 专利数据预处理模型
@Server: 
"""
from dataclasses import dataclass
from typing import List, Optional
from common.utils import clean_key_fields, clean_key_fields_hashdata
from .public import check_attributes


@dataclass
class PreprocessPatentMain:
    """
    预处理方法 专利基础信息 模型
    """
    source_type: int  # 数据源类型, 0:wipo, 1:swissreg
    source_id: str  # 原始数据源中的专利ID
    application_number: str = ''  # 申请号
    patent_number: str = ''  # 专利号/授权号
    publication_number: str = ''  # 公开号
    ip_type: str = ''  # 知识产权类型
    patent_type: str = ''  # 专利类型
    current_status: str = ''  # 当前状态
    application_date: str = ''  # 申请日期
    publication_date: str = ''  # 公开日期
    grant_date: str = ''  # 授权日期
    next_renewal_date: str = ''  # 下次续费日期
    protection_end_date: str = ''  # 保护期结束日期
    application_language: str = ''  # 申请语言
    filing_country: str = ''  # 申请国家代码
    title: str = ''  # 专利标题
    abstract: str = ''  # 专利摘要
    description: str = ''  # 专利说明书
    claims: str = ''  # 权利要求
    patent_specifications: str = ''  # 专利规格说明
    it_registration: str = ''  # 国际注册号
    it_publication: str = ''  # 国际公开号
    priorities: str = ''  # 优先权信息
    an_inheritance: int = 0  # 遗传/传统标识（0:否, 1:是）
    exhibition_immunity: str = ''  # 展览豁免权
    paediatric_spc: str = ''  # 儿科SPC信息
    cancellation: str = ''  # 撤销信息
    p_id: str = ''  # 统一专利ID（全局唯一）MD5(patent_number+filing_country)

    def __post_init__(self):
        # 清洗关键字段
        self.source_id = clean_key_fields(self.source_id)
        self.application_number = clean_key_fields(self.application_number)
        self.patent_number = clean_key_fields(self.patent_number)
        self.publication_number = clean_key_fields(self.publication_number)
        self.filing_country = clean_key_fields(self.filing_country)
        
        # 生成统一专利ID
        if self.patent_number and self.filing_country:
            self.p_id = clean_key_fields_hashdata(f"{self.patent_number}+{self.filing_country}")
        elif self.publication_number and self.filing_country:
            self.p_id = clean_key_fields_hashdata(f"{self.publication_number}+{self.filing_country}")
        elif self.application_number and self.filing_country:
            self.p_id = clean_key_fields_hashdata(f"{self.application_number}+{self.filing_country}")

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessPatentOwner:
    """
    预处理方法 专利所属关系 模型
    """
    p_id: str  # 统一专利ID（全局唯一）
    owner_id: str  # 所属关系ID（全局唯一）
    relationship_type: str = ''  # 关系类型
    entity_type: str = ''  # 实体类型 (company/human)
    entity_identifier: str = ''  # 实体标识ID
    owner_type: str = ''  # 所有者类型 (INHABER/ERFINDER)
    name: str = ''  # 姓名/公司名称
    additional_name: str = ''  # 附加名称
    display_name: str = ''  # 显示名称
    title: str = ''  # 职称/头衔
    street: str = ''  # 街道地址
    additional_street: str = ''  # 附加街道信息
    house_number: str = ''  # 门牌号
    zip_code: str = ''  # 邮政编码
    town: str = ''  # 城市
    country: str = ''  # 国家二字码
    email: str = ''  # 电子邮箱
    phone: str = ''  # 电话号码
    fax: str = ''  # 传真号码
    language: str = ''  # 语言代码
    modification: str = ''  # 修改信息
    priority_order: int = 1  # 优先级顺序
    is_primary: int = 0  # 是否为主要联系人
    status: str = ''  # 关系状态
    effective_date: str = ''  # 生效日期
    expiry_date: str = ''  # 失效日期

    def __post_init__(self):
        # 清洗关键字段
        self.p_id = clean_key_fields(self.p_id)
        self.owner_id = clean_key_fields(self.owner_id)
        self.name = clean_key_fields(self.name)
        self.country = clean_key_fields(self.country)
        
        # 根据owner_type确定entity_type
        if self.owner_type == 'INHABER':
            self.entity_type = 'company'
        elif self.owner_type == 'ERFINDER':
            self.entity_type = 'human'
        
        # 设置显示名称
        if not self.display_name:
            self.display_name = self.name

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessPatentClass:
    """
    预处理方法 专利分类 模型
    """
    p_id: str  # 统一专利ID（全局唯一）
    class_id: str  # 分类关系ID
    class_system: str = ''  # 分类体系类型 (IPC/CPC)
    class_version: str = ''  # 分类版本
    class_symbol: str = ''  # 分类符号
    subclass: str = ''  # 子分类
    main_group: str = ''  # 主组
    subgroup: str = ''  # 子组
    class_date: str = ''  # 分类日期
    effective_date: str = ''  # 生效日期
    class_code: str = ''  # 父级分类代码
    level_depth: int = 1  # 分类层级深度
    class_type: str = ''  # 分类类型
    class_status: str = ''  # 分类状态
    description_en: str = ''  # 英文描述
    is_primary: int = 0  # 是否为主要分类
    priority_order: int = 1  # 优先级顺序

    def __post_init__(self):
        # 清洗关键字段
        self.p_id = clean_key_fields(self.p_id)
        self.class_id = clean_key_fields(self.class_id)
        self.class_symbol = clean_key_fields(self.class_symbol)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass
