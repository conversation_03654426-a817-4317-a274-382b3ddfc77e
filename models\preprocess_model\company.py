from dataclasses import dataclass
from typing import List
from common.utils import clean_key_fields
from .public import check_attributes
from .public import PreprocessAddress
from .public import PreprocessLogo
from .public import transform_country_name
from .concat import PreprocessConcatMain


@dataclass
class PreprocessCompanyIndustry:
    """
    预处理方法 公司行业 模型
    if not industry:
        industry_id or (industry_class and industry_code)
    当 industry 字段为空时，industry_id必须存在 或者 industry_class和industry_code必须存在
    """
    industry: str
    industry_id: int = 0  # 关联行业表ID
    industry_class: str = ''  # 国际行业分类标准(NAICS-北美工业分类系统;NACE-欧洲经济活动分类;ISIC-国际标准行业分类;CPC-中央产品分类;UKSIC-英国标准行业分类)
    industry_code: str = ''  # 行业代码

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessCompanyNames:
    """
    预处理方法 公司非实体现用名称 模型
    """
    company_name: str  # 公司非实体现用名称
    company_name_type_id: int = 1  # 名称类型;曾用名、别名、交易名等
    start_date: int = 0  # 名称使用起始日期;精确到年月日
    end_date: int = 0  # 名称使用结束日期;精确到年月日

    def __post_init__(self):
        self.company_name = clean_key_fields(self.company_name)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessCompanyStock:
    """
    预处理方法 公司上市信息 模型
    """
    stock_code: str  # 股票代码
    listing_sector: str = ''  # 上市板块
    listing_status: str = ''  # 上市状态

    def __post_init__(self):
        self.stock_code = clean_key_fields(self.stock_code)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessCompanyProducts:
    """
    预处理方法 公司产品信息 模型
    """
    product_name: str  # 产品名称

    def __post_init__(self):
        self.product_name = clean_key_fields(self.product_name)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessCompanyShareholders:
    """
    预处理方法 股东 模型
    """
    shareholder_name: str  # 股东名称
    country: str  # 股东所属国家
    province: str  # 股东所属州省
    country_code: str = ''  # 股东所属国家二字码
    bus_id_old: str = ''  # 数据源股东ID
    shareholder_type: int = 0  # 股东类型；0-未检测，1-个人，2-公司，3-不确定
    shareholder_direct: str = ''  # 持股比
    shareholder_total: str = ''  # 持股总比

    def __post_init__(self):
        self.shareholder_name = clean_key_fields(self.shareholder_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        # 特殊映射
        self.country = transform_country_name(self.country)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessCompanySubsidiary:
    """
    预处理方法 子公司 模型
    """
    subsidiary_name: str  # 子公司名称
    country: str  # 子公司所属国家
    province: str  # 子公司所属州省
    country_code: str = ''  # 子公司所属国家二字码
    bus_id_old: str = ''  # 数据源子公司ID
    shareholder_direct: str = ''  # 持股比
    shareholder_total: str = ''  # 持股总比

    def __post_init__(self):
        self.subsidiary_name = clean_key_fields(self.subsidiary_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        # 特殊映射
        self.country = transform_country_name(self.country)

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass


@dataclass
class PreprocessCompanyNationalIdentifiers:
    """
    预处理方法 公司编号 模型
    """
    nat_id: str  # 编号
    nat_label_id: str = ''  # 编号标签类型ID

    def __post_init__(self):
        self.nat_id = self.nat_id.upper()


@dataclass
class PreprocessCompanyTechStack:
    """
    预处理方法 公司技术栈 模型
    """
    tech_name: str  # 技术名称
    tech_logo_url: str = ''  # 技术logo链接
    tech_logo_path: str = ''  # 技术logo于COS存储地址
    tech_category: str = ''  # 技术所属类别
    by_cname: str = ''  # 技术所属公司名称

    def __post_init__(self):
        self.tech_name = clean_key_fields(self.tech_name)


@dataclass
class PreprocessCompanyMain:
    """
    预处理方法 公司 模型
    """
    source_name: str
    company_name: str  # 公司当前使用实体名称
    country: str  # 公司所属国家
    province: str  # 公司所属州省
    country_code: str = ''  # 公司所属国家二字码
    city: str = ''  # 市名
    bus_id_old: str = ''  # 数据源公司ID
    bus_id_new: str = ''  # 新版公司ID(在其他业务中对新版公司进行过匹配，填写则新版公司ID直接使用该值，不填写则会根据新版公司ID计算规则重新计算)
    company_status: str = ''  # 公司状态
    company_type: str = ''  # 公司类型
    company_code: str = ''  # 公司编号(注册号)；企业在当地的唯一标识
    inc_date: int = 0  # 成立日期，秒级时间戳
    reg_date: int = 0  # 注册日期，秒级时间戳
    latitude: str = ''  # 维度|Latitude
    longitude: str = ''  # 经度|Longitude
    opgname: str = ''  # 同业组名称
    business_scope: str = ''  # 企业经营范围
    overview: str = ''  # 公司简介
    agent_name: str = ''  # 代理人名称
    revenue_usd: int = 0  # 营收：单位千美元
    company_size: int = 0  # 准确的员工人数或公司规模范围第一位: 如 11-50 取 11
    company_logo_obj_list: List[PreprocessLogo] = None  # 公司logo数据
    company_address_obj_list: List[PreprocessAddress] = None  # 公司地址数据
    company_industry_obj_list: List[PreprocessCompanyIndustry] = None  # 公司行业信息
    company_names_obj_list: List[PreprocessCompanyNames] = None  # 公司非实体现用名称数据
    company_products_obj_list: List[PreprocessCompanyProducts] = None  # 公司产品名称列表
    company_stock_obj_list: List[PreprocessCompanyStock] = None  # 公司上市信息
    company_shareholders_obj_list: List[PreprocessCompanyShareholders] = None  # 公司股东信息
    company_subsidiary_obj_list: List[PreprocessCompanySubsidiary] = None  # 公司子公司信息
    company_national_identifiers_obj_list: List[PreprocessCompanyNationalIdentifiers] = None  # 公司编号信息
    company_tech_stack_obj_list: List[PreprocessCompanyTechStack] = None  # 公司技术栈信息
    concat_obj: PreprocessConcatMain = None  # 联系方式信息

    def __post_init__(self):
        self.company_name = clean_key_fields(self.company_name)
        self.country = clean_key_fields(self.country)
        self.province = clean_key_fields(self.province)
        self.city = clean_key_fields(self.city)
        self.company_status = clean_key_fields(self.company_status)
        self.company_type = clean_key_fields(self.company_type)

        # 特殊映射
        self.country = transform_country_name(self.country)

        if len(self.province) <= 1:
            self.province = ''
        if len(self.city) <= 1:
            self.city = ''

    @check_attributes
    def check_attrs(self, attrs=None):
        """
        属性校检
        """
        pass

