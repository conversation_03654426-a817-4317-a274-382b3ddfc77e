#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-01-09 20:50:25
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List


class TableApolloPersonInfo(TableBase):
    db = 'db_spider'
    table = 'apollo_person_info'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_person_infos(self, hids: list = None) -> List[dict]:
        """
        获取人物基本信息
        :param hids: 人物ID列表
        :return:
        """
        if not hids:
            return []
        bfhs_str = ','.join(['%s'] * len(hids))
        sql = f'''
            select * from {self.db}.{self.table} where person_id in ({bfhs_str}) and is_deleted=0
        '''
        data_src = self.db_mysql.read(sql, value=hids, return_dict=True)
        results: List[dict] = data_src.data
        return results
