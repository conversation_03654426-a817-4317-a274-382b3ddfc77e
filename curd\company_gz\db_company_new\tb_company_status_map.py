#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: tb_company_status_map.py
@Date: 2024/1/5
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from models import MysqlResult
from typing import Sequence


class TableCompanyStatusMap(TableBase):
    db = 'db_company_new'
    table = 'company_status_map'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_company_status_id(self, company_status_set: set) -> dict:
        """获取公司状态ID"""
        if not company_status_set:
            return {}
        bfhs_str = ','.join(['%s'] * len(company_status_set))
        # 查询公司状态ID
        sql = f'''
                select `id`, `company_status` from {self.db}.{self.table} where `company_status` in ({bfhs_str})
            '''
        data_src: MysqlResult = self.db_mysql.read(sql, value=list(company_status_set), return_dict=True)
        results: Sequence[dict] = data_src.data
        company_status_id_map = {}
        for row in results:
            tid = row.get('id', 0)
            company_status = row.get('company_status', '')
            if not tid:
                continue
            company_status_id_map[company_status] = tid
        # 将不存在的公司类型插入数据表，并获取自增ID
        for company_status in company_status_set:
            if company_status in company_status_id_map:
                continue
            data_src = self.db_mysql.save(self.table, {'company_status': company_status}, db=self.db, ignore=True)
            company_status_id_map[company_status] = data_src.lastrowid
        return company_status_id_map
