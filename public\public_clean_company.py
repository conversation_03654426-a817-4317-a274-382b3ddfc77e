#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public_clean_main.py
@Date: 2024/1/3
@Desc: 公共清洗服务
@Server: 
"""
import time
from common.database import MysqlConnectionManager
from models.db_company import *
from models.db_concat import *
from models.preprocess_model import *
from models.public_clean_model import PublicCleanCompanyMain
from models.public_clean_model import PublicCleanConcatMain
from typing import Optional, Union, Tuple, List
from settings import *
from curd import TableCompanyTypeMap
from curd import TableCompanyStatusMap
from curd import TableArea
from curd import TableIndustryMap
from public.public_funcs import public_funcs_obj
from common.database import DBPoolMysql
from public.public_clean_concats import PublicFuncCleanConcatsMain
from public.public_save import PublicSaveMain


class PublicFuncCleanCompanyMain:
    def __init__(self, mysql_manager: MysqlConnectionManager):
        self.mysql_obj_company_gz: DBPoolMysql = mysql_manager.get_pool("company_gz")
        self.mysql_obj_company_new_gz: DBPoolMysql = mysql_manager.get_pool("company_new_gz")
        self.table_company_type_map_obj = TableCompanyTypeMap(self.mysql_obj_company_new_gz)
        self.table_company_status_map_obj = TableCompanyStatusMap(self.mysql_obj_company_new_gz)
        self.table_area_obj = TableArea(self.mysql_obj_company_gz)
        self.table_industry_map_obj = TableIndustryMap(self.mysql_obj_company_gz)
        self.area_map = self.table_area_obj.get_area_map()
        # 行业代码与行业映射
        self.industry_map = self.table_industry_map_obj.get_industry_map()
        self.public_func_clean_concats_main_obj = PublicFuncCleanConcatsMain(mysql_manager)
        self.public_save_main_obj = PublicSaveMain(mysql_manager)

    def deal_clean_company_shareholders(
            self,
            source_name: str,
            pid: str,
            preprocess_company_shareholders_obj_list: List[PreprocessCompanyShareholders]
    ):
        """
        处理 公司 股东信息
        """
        company_bus_id_map_obj_list = []
        company_shareholders_obj_list: Union[List[CompanyShareholders], None] = []
        if not source_name or not pid or not preprocess_company_shareholders_obj_list:
            return company_shareholders_obj_list, company_bus_id_map_obj_list
        for p_company_shareholders_obj in preprocess_company_shareholders_obj_list:
            _shareholder_name = p_company_shareholders_obj.shareholder_name
            _country = p_company_shareholders_obj.country
            _province = p_company_shareholders_obj.province
            _country_code = p_company_shareholders_obj.country_code
            _bus_id_old = p_company_shareholders_obj.bus_id_old
            _shareholder_type = p_company_shareholders_obj.shareholder_type
            _shareholder_direct = p_company_shareholders_obj.shareholder_direct
            _shareholder_total = p_company_shareholders_obj.shareholder_total
            if not _shareholder_name:
                logger.warning(f'缺少实例化必要参数 -> 【CompanyShareholders】')
                continue
            company_shareholders_obj: CompanyShareholders = CompanyShareholders(
                source_name=source_name, pid=pid, shareholder_name=_shareholder_name,
                country=_country, province=_province, country_code=_country_code,
                bus_id_old=_bus_id_old, shareholder_type=_shareholder_type, shareholder_direct=_shareholder_direct,
                shareholder_total=_shareholder_total
            )
            # 校验新旧ID映射表
            # company_shareholders_obj.check_shareholder_id(self.table_bus_id_map_obj, _bus_id_old)
            # 补充新旧ID映射表
            _shareholder_id = company_shareholders_obj.shareholder_id
            _shareholder_type = company_shareholders_obj.shareholder_type
            if _shareholder_id and _bus_id_old:
                if _shareholder_type == 2:
                    # 公司类型股东
                    company_bus_id_map_obj_list.append(
                        BusIdMapCompany(
                            source_name, _bus_id_old, _shareholder_id
                        )
                    )
                else:
                    # 人物 类型股东
                    pass
            company_shareholders_obj_list.append(company_shareholders_obj)
        return company_shareholders_obj_list, company_bus_id_map_obj_list

    def deal_clean_company(
            self,
            preprocess_company_main_obj: PreprocessCompanyMain,
            company_type_id_map: dict,
            company_status_id_map: dict
    ) -> Union[PublicCleanCompanyMain, None]:
        """
        清洗 公司数据 执行逻辑
        :param preprocess_company_main_obj: 预处理模型数据
        :param company_type_id_map: 公司类型ID映射
        :param company_status_id_map: 公司状态ID映射
        :return:
        """
        # 新旧ID映射模型对象列表; 公司基本信息、公司股东、公司子公司、人物照面信息、人物任职经历中存在
        company_bus_id_map_obj_list = []
        source_name = preprocess_company_main_obj.source_name
        company_name = preprocess_company_main_obj.company_name
        country = preprocess_company_main_obj.country or ''
        province = preprocess_company_main_obj.province or ''
        country_code = preprocess_company_main_obj.country_code or ''
        city = preprocess_company_main_obj.city or ''
        bus_id_old = preprocess_company_main_obj.bus_id_old
        bus_id_new = preprocess_company_main_obj.bus_id_new or ''
        company_status = preprocess_company_main_obj.company_status or ''
        company_type = preprocess_company_main_obj.company_type or ''
        company_code = preprocess_company_main_obj.company_code or ''
        inc_date = preprocess_company_main_obj.inc_date or 0
        reg_date = preprocess_company_main_obj.reg_date or 0
        latitude = preprocess_company_main_obj.latitude or ''
        longitude = preprocess_company_main_obj.longitude or ''
        opgname = preprocess_company_main_obj.opgname or ''
        business_scope = preprocess_company_main_obj.business_scope or ''
        overview = preprocess_company_main_obj.overview or ''
        agent_name = preprocess_company_main_obj.agent_name or ''
        revenue_usd = preprocess_company_main_obj.revenue_usd or 0
        company_size = preprocess_company_main_obj.company_size or 0

        # 规范公司规模
        company_size = public_funcs_obj.clean_company_size(company_size)
        # 匹配公司状态类型ID
        company_type_id = company_type_id_map.get(company_type, 0) if company_type else 0
        company_status_id = company_status_id_map.get(company_status, 0) if company_status else 0
        # 匹配标准地区
        area_data = public_funcs_obj.mate_area_data(self.area_map, country_code, country, province, city)
        country_code = area_data.get('country_iso_code', '')
        province_id = area_data.get('province_id', 0) or 0
        city_id = area_data.get('city_id', 0) or 0
        country = area_data.get('country_en', '') if country_code else country
        province = area_data.get('province_en', '') if province_id else province
        city = area_data.get('city_en', '') if city_id else city

        p_company_logo_obj_list = d if (d := preprocess_company_main_obj.company_logo_obj_list) else []
        p_company_addresses_obj_list = d if (d := preprocess_company_main_obj.company_address_obj_list) else []
        p_company_industry_obj_list = d if (d := preprocess_company_main_obj.company_industry_obj_list) else []
        p_company_names_obj_list = d if (d := preprocess_company_main_obj.company_names_obj_list) else []
        p_company_products_obj_list = d if (d := preprocess_company_main_obj.company_products_obj_list) else []
        p_company_stock_obj_list = d if (d := preprocess_company_main_obj.company_stock_obj_list) else []
        p_company_shareholders_obj_list = d if (d := preprocess_company_main_obj.company_shareholders_obj_list) else []
        p_company_subsidiary_obj_list = d if (d := preprocess_company_main_obj.company_subsidiary_obj_list) else []

        if preprocess_company_main_obj.concat_obj:
            p_company_email_obj_list = d if (d := preprocess_company_main_obj.concat_obj.email_obj_list) else []
            p_company_phone_obj_list = d if (d := preprocess_company_main_obj.concat_obj.phone_obj_list) else []
            p_company_social_obj_list = d if (d := preprocess_company_main_obj.concat_obj.social_obj_list) else []
            p_company_website_obj_list = d if (d := preprocess_company_main_obj.concat_obj.website_obj_list) else []
        else:
            p_company_email_obj_list = []
            p_company_phone_obj_list = []
            p_company_social_obj_list = []
            p_company_website_obj_list = []

        p_company_national_identifiers_obj_list = d if (d := preprocess_company_main_obj.company_national_identifiers_obj_list) else []
        p_company_tech_stack_obj_list = d if (d := preprocess_company_main_obj.company_tech_stack_obj_list) else []
        if not source_name or not company_name:
            logger.warning(f'缺少实例化必要参数 -> 【PublicCleanCompanyMain】')
            return None
        company_obj: Company = Company(
            source_name=source_name, company_name=company_name, country=country,
            province=province, company_code=company_code, inc_date=inc_date,
            reg_date=reg_date, latitude=latitude, longitude=longitude,
            opgname=opgname, business_scope=business_scope, overview=overview,
            agent_name=agent_name, revenue_usd=revenue_usd, company_size=company_size,
            company_type_id=company_type_id, company_status_id=company_status_id,
            pid=bus_id_new
        )
        if not company_obj or not company_obj.company_name:
            return None
        # company_obj.check_pid(self.table_bus_id_map_obj, bus_id_old)
        pid = company_obj.pid
        if not pid:
            logger.warning(f'缺少重要参数 -> 新公司ID')
            return None
        # 赋值给传入对象
        preprocess_company_main_obj.pid = pid
        if bus_id_old:
            company_bus_id_map_obj_list.append(BusIdMapCompany(
                source_name, bus_id_old, pid
            ))

        company_status_map_obj = CompanyStatusMap(
            source_name=source_name, company_status=company_status
        ) if company_status else None

        company_type_map_obj = CompanyTypeMap(
            source_name=source_name, company_type=company_type
        ) if company_type else None

        if country and province:
            company_area_info_obj = CompanyAreaInfo(
                source_name=source_name, pid=pid, country=country,
                province=province, city=city, country_code=country_code,
                province_id=province_id, city_id=city_id
            )
        else:
            company_area_info_obj = None

        company_logo_obj_list = []
        for p_company_logo_obj in p_company_logo_obj_list:
            _logo_url = p_company_logo_obj.logo_url
            _logo_url_local = p_company_logo_obj.logo_url_local
            if _logo_url or _logo_url_local:
                company_logo_obj = CompanyLogo(
                    source_name, pid,
                    logo_url=_logo_url,
                    logo_url_local=_logo_url_local
                )
                company_logo_obj_list.append(company_logo_obj)

        company_addresses_obj_list = []
        for p_company_addresses_obj in p_company_addresses_obj_list:
            _address = p_company_addresses_obj.address
            _address_type_id = p_company_addresses_obj.address_type_id
            _postal_code = p_company_addresses_obj.postal_code
            _start_date = p_company_addresses_obj.start_date
            _end_date = p_company_addresses_obj.end_date
            _country_code_iso2 = p_company_addresses_obj.country_code_iso2
            _country_en = p_company_addresses_obj.country_en
            _province_en = p_company_addresses_obj.province_en
            _city_en = p_company_addresses_obj.city_en
            _street = p_company_addresses_obj.street
            _province_id = p_company_addresses_obj.province_id
            _city_id = p_company_addresses_obj.city_id
            if _address:
                company_addresses_obj = CompanyAddresses(
                    source_name=source_name, pid=pid, address=_address,
                    postal_code=_postal_code, address_type_id=_address_type_id, start_date=_start_date,
                    end_date=_end_date, country_code_iso2=_country_code_iso2, country_en=_country_en,
                    province_en=_province_en, city_en=_city_en, street=_street,
                    province_id=_province_id, city_id=_city_id
                )
                company_addresses_obj_list.append(company_addresses_obj)

        company_industry_obj_list = []
        for p_company_industry_obj in p_company_industry_obj_list:
            _industry = p_company_industry_obj.industry
            if not _industry:
                logger.warning(f'缺少实例化必要参数 -> 【CompanyIndustry】')
                continue
            _industry_id = p_company_industry_obj.industry_id or 0
            _industry_class = p_company_industry_obj.industry_class
            _industry_code = p_company_industry_obj.industry_code
            # 匹配标准行业ID
            _industry_id = public_funcs_obj.mate_industry_id(
                self.industry_map, _industry_id,
                _industry,
                _industry_class,
                _industry_code
            )
            company_industry_obj = CompanyIndustry(
                source_name=source_name, pid=pid, industry=_industry,
                industry_id=_industry_id
            )
            company_industry_obj_list.append(company_industry_obj)

        company_names_obj_list = []
        for p_company_names_obj in p_company_names_obj_list:
            _company_name = p_company_names_obj.company_name
            _company_name_type_id = p_company_names_obj.company_name_type_id
            _start_date = p_company_names_obj.start_date
            _end_date = p_company_names_obj.end_date
            if not _company_name:
                logger.warning(f'缺少实例化必要参数 -> 【CompanyNames】')
                continue
            company_names_obj = CompanyNames(
                source_name=source_name, pid=pid, company_name=_company_name,
                company_name_type_id=_company_name_type_id, start_date=_start_date, end_date=_end_date
            )
            company_names_obj_list.append(company_names_obj)

        company_products_obj_list = []
        for p_company_products_obj in p_company_products_obj_list:
            _product_name = p_company_products_obj.product_name
            if not _product_name:
                logger.warning(f'缺少实例化必要参数 -> 【CompanyProducts】')
                continue
            company_products_obj = CompanyProducts(
                source_name=source_name, pid=pid, product_name=_product_name
            )
            company_products_obj_list.append(company_products_obj)

        company_stock_obj_list = []
        for p_company_stock_obj in p_company_stock_obj_list:
            _stock_code = p_company_stock_obj.stock_code
            _listing_sector = p_company_stock_obj.listing_sector
            _listing_status = p_company_stock_obj.listing_status
            if not _stock_code:
                logger.warning(f'缺少实例化必要参数 -> 【CompanyStock】')
                continue
            company_stock_obj = CompanyStock(
                source_name=source_name, pid=pid, stock_code=_stock_code,
                listing_sector=_listing_sector, listing_status=_listing_status
            )
            company_stock_obj_list.append(company_stock_obj)

        company_shareholders_obj_list, _company_bus_id_map_obj_list = self.deal_clean_company_shareholders(
            source_name=source_name, pid=pid,
            preprocess_company_shareholders_obj_list=p_company_shareholders_obj_list
        )
        company_bus_id_map_obj_list += _company_bus_id_map_obj_list

        company_subsidiary_obj_list: Union[List[CompanySubsidiary], None] = []
        for p_company_subsidiary_obj in p_company_subsidiary_obj_list:
            _subsidiary_name = p_company_subsidiary_obj.subsidiary_name
            _country = p_company_subsidiary_obj.country
            _province = p_company_subsidiary_obj.province
            _country_code = p_company_subsidiary_obj.country_code
            _bus_id_old = p_company_subsidiary_obj.bus_id_old
            _shareholder_direct = p_company_subsidiary_obj.shareholder_direct
            _shareholder_total = p_company_subsidiary_obj.shareholder_total
            if not _subsidiary_name:
                logger.warning(f'缺少实例化必要参数 -> 【CompanySubsidiary】')
                continue
            company_subsidiary_obj: CompanySubsidiary = CompanySubsidiary(
                source_name=source_name, pid=pid, subsidiary_name=_subsidiary_name,
                country=_country, province=_province, country_code=_country_code,
                bus_id_old=_bus_id_old, shareholder_direct=_shareholder_direct, shareholder_total=_shareholder_total
            )
            # 校验新旧ID映射表
            # company_subsidiary_obj.check_subsidiary_id(self.table_bus_id_map_obj, _bus_id_old)
            # 补充新旧ID映射表
            _subsidiary_id = company_subsidiary_obj.subsidiary_id
            if _subsidiary_id and _bus_id_old:
                company_bus_id_map_obj_list.append(BusIdMapCompany(
                    source_name, _bus_id_old, _subsidiary_id
                ))
            company_subsidiary_obj_list.append(company_subsidiary_obj)

        emails_obj_list: Union[List[EmailsAll], None] = []
        emails_obj_list_tmp = self.public_func_clean_concats_main_obj.create_email_all(
            obj_list=p_company_email_obj_list
        )
        emails_obj_list += emails_obj_list_tmp

        phones_obj_list: Union[List[PhonesAll], None] = []
        phones_obj_list_tmp = self.public_func_clean_concats_main_obj.create_phone_all(
            obj_list=p_company_phone_obj_list
        )
        phones_obj_list += phones_obj_list_tmp

        socials_obj_list: Union[List[SocialsAll], None] = []
        social_obj_list_tmp = self.public_func_clean_concats_main_obj.create_social_all(
            obj_list=p_company_social_obj_list
        )
        socials_obj_list += social_obj_list_tmp

        websites_obj_list: Union[List[WebsitesAll], None] = []
        websites_obj_list_tmp = self.public_func_clean_concats_main_obj.create_website_all(
            obj_list=p_company_website_obj_list
        )
        websites_obj_list += websites_obj_list_tmp

        business_emails_obj_list: Union[List[BusinessEmailsAll], None] = []
        business_emails_obj_list_tmp = self.public_func_clean_concats_main_obj.create_email_bus(
            public_main_obj=company_obj,
            obj_list=p_company_email_obj_list,
            bus_type=1
        )
        business_emails_obj_list += business_emails_obj_list_tmp

        business_phones_obj_list: Union[List[BusinessPhonesAll], None] = []
        business_phones_obj_list_tmp = self.public_func_clean_concats_main_obj.create_phone_bus(
            public_main_obj=company_obj,
            obj_list=p_company_phone_obj_list,
            bus_type=1
        )
        business_phones_obj_list += business_phones_obj_list_tmp

        business_socials_obj_list: Union[List[BusinessSocialsAll], None] = []
        business_socials_obj_list_tmp = self.public_func_clean_concats_main_obj.create_social_bus(
            public_main_obj=company_obj,
            obj_list=p_company_social_obj_list,
            bus_type=1
        )
        business_socials_obj_list += business_socials_obj_list_tmp

        business_websites_obj_list: Union[List[BusinessWebsitesAll], None] = []
        business_websites_obj_list_tmp = self.public_func_clean_concats_main_obj.create_website_bus(
            public_main_obj=company_obj,
            obj_list=p_company_website_obj_list,
            bus_type=1
        )
        business_websites_obj_list += business_websites_obj_list_tmp

        company_national_identifiers_obj_list: Union[List[CompanyNationalIdentifiers], None] = []
        for p_company_national_identifiers_obj in p_company_national_identifiers_obj_list:
            _nat_id = p_company_national_identifiers_obj.nat_id
            _nat_label_id = p_company_national_identifiers_obj.nat_label_id
            if not _nat_id:
                continue
            company_national_identifiers_obj = CompanyNationalIdentifiers(
                source_name=source_name,
                pid=pid,
                nat_id=_nat_id,
                nat_label_id=_nat_label_id,
            )
            company_national_identifiers_obj_list.append(company_national_identifiers_obj)

        # 公司技术栈
        company_tech_stack_obj_list: Union[List[CompanyTechStack], None] = []
        for p_company_tech_stack_obj in p_company_tech_stack_obj_list:
            tech_name = p_company_tech_stack_obj.tech_name
            tech_logo_url = p_company_tech_stack_obj.tech_logo_url
            tech_logo_path = p_company_tech_stack_obj.tech_logo_path
            tech_category = p_company_tech_stack_obj.tech_category
            by_cname = p_company_tech_stack_obj.by_cname
            if not tech_name:
                continue
            company_tech_stack_obj = CompanyTechStack(
                source_name=source_name,
                pid=pid,
                tech_name=tech_name,
                tech_logo_url=tech_logo_url,
                tech_logo_path=tech_logo_path,
                tech_category=tech_category,
                by_cname=by_cname,
            )
            company_tech_stack_obj_list.append(company_tech_stack_obj)

        # 联系方式模型
        concat_obj = PublicCleanConcatMain(
            emails_obj_list=emails_obj_list,
            phones_obj_list=phones_obj_list,
            socials_obj_list=socials_obj_list,
            websites_obj_list=websites_obj_list,
            business_emails_obj_list=business_emails_obj_list,
            business_phones_obj_list=business_phones_obj_list,
            business_socials_obj_list=business_socials_obj_list,
            business_websites_obj_list=business_websites_obj_list,
        )

        public_clean_company_main_obj = PublicCleanCompanyMain(
            company_obj=company_obj,
            company_status_map_obj=company_status_map_obj,
            company_type_map_obj=company_type_map_obj,
            company_area_info_obj=company_area_info_obj,
            company_logo_obj_list=company_logo_obj_list,
            bus_id_map_obj_list=company_bus_id_map_obj_list,
            company_addresses_obj_list=company_addresses_obj_list,
            company_industry_obj_list=company_industry_obj_list,
            company_names_obj_list=company_names_obj_list,
            company_products_obj_list=company_products_obj_list,
            company_stock_obj_list=company_stock_obj_list,
            company_shareholders_obj_list=company_shareholders_obj_list,
            company_subsidiary_obj_list=company_subsidiary_obj_list,
            company_national_identifiers_obj_list=company_national_identifiers_obj_list,
            company_tech_stack_obj_list=company_tech_stack_obj_list,
            concat_obj=concat_obj,
        )

        return public_clean_company_main_obj

    def main_clean_company(self,
                           preprocess_company_main_obj_list: List[PreprocessCompanyMain]
                           ) -> List[PublicCleanCompanyMain]:
        """
        清洗 公司数据 执行入口
        :param preprocess_company_main_obj_list: 预处理模型数据 列表
        :return: 
        """
        s_time = time.time()
        logging.info(f'start to clean company === {len(preprocess_company_main_obj_list)}')
        public_clean_company_main_obj_list = []
        company_type_set = set()
        company_status_set = set()
        for preprocess_company_main_obj in preprocess_company_main_obj_list:
            company_type = preprocess_company_main_obj.company_type
            company_status = preprocess_company_main_obj.company_status
            if company_type:
                company_type_set.add(company_type)
            if company_status:
                company_status_set.add(company_status)

        # 获取所有公司类型ID映射
        company_type_id_map = self.table_company_type_map_obj.get_company_type_id(company_type_set)
        # 获取所有公司状态ID映射
        company_status_id_map = self.table_company_status_map_obj.get_company_status_id(company_status_set)

        for preprocess_company_main_obj in preprocess_company_main_obj_list:
            public_clean_company_main_obj = self.deal_clean_company(
                preprocess_company_main_obj, company_type_id_map, company_status_id_map
            )
            if not public_clean_company_main_obj:
                continue
            public_clean_company_main_obj_list.append(public_clean_company_main_obj)
        e_time = time.time()
        logging.info(f'end clean company === {len(preprocess_company_main_obj_list)} === hs: {e_time - s_time:.2f} s')
        # 校检新旧ID一致性
        # public_clean_company_main_obj_list = self.check_bus_id_uniformity(public_clean_company_main_obj_list)
        # 数据存储至数据库
        save_stime = time.time()
        logging.info(f'start to save mysql === {len(public_clean_company_main_obj_list)}')
        self.public_save_main_obj.save_to_mysql(
            public_clean_company_main_obj_list=public_clean_company_main_obj_list
        )
        save_etime = time.time()
        logging.info(
            f'end to save mysql === {len(public_clean_company_main_obj_list)} === hs: {save_etime - save_stime:.2f} s')
        return public_clean_company_main_obj_list

    def test_unit(self):
        """
        单元测试
        :return: 
        """
        preprocess_company_main_obj = PreprocessCompanyMain(
            source_name='depth_company',
            company_name='vong sawat textile co ltd',
            country='Thailand', province='', city='bangkok',
            company_status='active', company_type='corporate',
            company_code='0105536048006', inc_date=*********,
            latitude='13.7399319',
            longitude='100.5041171', opgname='4641-SM',
            business_scope='The company is involved in wholesale of textiles, except clothing.'
        )
        self.main_clean_company([preprocess_company_main_obj])


if __name__ == '__main__':
    from common.database import MysqlConnectionManager
    from models import ConfMySQL

    conf_list = [
        ConfMySQL(name='company_gz', max_connections=1),
        ConfMySQL(name='company_new_gz', max_connections=1),
    ]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    public_func_clean_company_main_obj = PublicFuncCleanCompanyMain(mysql_manager)
    public_func_clean_company_main_obj.test_unit()
    pass
