#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-01-09 14:35:59
@Desc: 
@Server: 
"""
from curd.tb_base import TableBase
from typing import List, Sequence, Dict


class TableApolloEmploymentHistory(TableBase):
    db = 'db_spider'
    table = 'apollo_employment_history'

    def __init__(self, db_mysql):
        super().__init__(db_mysql)

    def get_person_director(self, cids: list = None) -> Sequence[dict]:
        """
        获取人物任职信息
        :param cids: 公司ID列表
        :return:
        """
        person_director_data = {}
        if not cids:
            return person_director_data
        bfhs_str = ','.join(['%s'] * len(cids))
        sql = f'''
            select * from {self.db}.{self.table} where company_id in ({bfhs_str}) and is_deleted=0 order by id
        '''
        data_src = self.db_mysql.read(sql, value=cids, return_dict=True)
        results: Sequence[dict] = data_src.data
        return results

