#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Author: SuSu
@File: public_clean_customs.py
@Date: 2024/5/28
@Desc: 
@Server: 
"""
import json
import time

from common.database import DBPoolMysql
from common.database import MysqlConnectionManager
from settings import *
from models.preprocess_model import *
from models.db_customs import *
from models.public_clean_model import PublicCleanCustomsTradeMain
from curd import TableCustomsTrade, TableArea, TableCustomsCompany
from typing import Dict, List, Union, Optional


class PublicFuncCleanCustomsMain:
    def __init__(self, mysql_manager: MysqlConnectionManager):
        self.mysql_obj_customs_new_gz: DBPoolMysql = mysql_manager.get_pool("customs_new_gz")
        self.mysql_obj_company_gz: DBPoolMysql = mysql_manager.get_pool("company_gz")
        self.table_customs_trade_obj = TableCustomsTrade(self.mysql_obj_customs_new_gz)
        self.table_customs_company_obj = TableCustomsCompany(self.mysql_obj_customs_new_gz)
        # self.field_defaults = self.mysql_obj_customs_new_gz.read_column_default('t_trade_2010_01', 'db_customs')
        self.field_schema = self.mysql_obj_customs_new_gz.read_column_default('t_trade_2010_01', 'db_customs')
        self.table_area_obj = TableArea(self.mysql_obj_company_gz)
        self.get_country_map = self.table_area_obj.get_country_map()
        self.get_province_map = self.table_area_obj.get_province_map()
        self.get_city_map = self.table_area_obj.get_city_map()

        self.COUNTRY_NAME_MAP = {
            'Hong Kong'.lower(): 'Hong Kong(China)'.lower(),
            'Hong Kong SAR'.lower(): 'Hong Kong(China)'.lower(),
            'Taiwan'.lower(): 'Taiwan(China)'.lower(),
            'Macao'.lower(): 'Macao(China)'.lower(),
        }

    def check_is_similar(self, uid_sid_map: dict, sid_uid_not_similar_map: dict) -> dict:
        """
        校验数据是否为相似数据
        :param uid_sid_map: {uuid: sid}
        :param sid_uid_not_similar_map: {sid: not_similar uid}
        :return: {uuid: is_similar}
        """
        uid_is_similar_map = {}
        not_existed_sids = set()
        for uuid, sid in uid_sid_map.items():
            if sid in sid_uid_not_similar_map:
                if uuid == sid_uid_not_similar_map[sid]:
                    uid_is_similar_map[uuid] = 2
                else:
                    uid_is_similar_map[uuid] = 1
            else:
                # sid 不在 not_similar_map 中，则认为 sid 还不在表中，则在该批sid中取一条为非相似
                if sid not in not_existed_sids:
                    not_existed_sids.add(sid)
                    uid_is_similar_map[uuid] = 2
                else:
                    uid_is_similar_map[uuid] = 1
        return uid_is_similar_map

    def unify_country_name_code(self, preprocess_customs_trade_main_obj, field):
        """
        统一国家名称二字码,id
        """
        source_field_country = getattr(preprocess_customs_trade_main_obj, field)
        source_field_country_new = self.COUNTRY_NAME_MAP.get(source_field_country.lower(), source_field_country) or source_field_country
        source_field_country_code = getattr(preprocess_customs_trade_main_obj, f'''{field}_code''')
        field_country_code = self.get_country_map.get(source_field_country_new.lower(), {}).get("code", "")
        field_country = self.get_country_map.get(field_country_code.lower(), {}).get("en", "")
        if not field_country_code and source_field_country:
            #  当名称(不为空字符串)无法匹配上std国家二字码时，使用二字码去匹配std国家二字码，
            #  如国家名称Hong kong、二字码HK，这类应该转化成Hong Kong(China)，二字码HK
            field_country_code = self.get_country_map.get(source_field_country_code.lower(), {}).get("code", "")
            field_country = self.get_country_map.get(source_field_country_code.lower(), {}).get("en", "")
            if not field_country_code and not field_country:
                #  实在匹配不上，保留数据源的数据即可
                field_country = source_field_country
                field_country_code = source_field_country_code
        field_country_id = self.get_country_map.get(field_country_code.lower(), {}).get("id", 0)
        return field_country, field_country_code, field_country_id

    def assignment_customs_company_info(
            self,
            preprocess_customs_company_main_obj: PreprocessCustomsCompanyMain
    ) -> CustomsCompany:
        """公司模型映射"""
        # 匹配国家、省份、城市名称ID
        if not preprocess_customs_company_main_obj.country_id:
            country = preprocess_customs_company_main_obj.country.lower()
            preprocess_customs_company_main_obj.country = self.get_country_map.get(country, {}).get("en", "")
            preprocess_customs_company_main_obj.country_id = self.get_country_map.get(country, {}).get("id", 0)
        if not preprocess_customs_company_main_obj.province_id:
            province = preprocess_customs_company_main_obj.province.lower()
            preprocess_customs_company_main_obj.province = self.get_province_map.get(province, {}).get("en", "")
            preprocess_customs_company_main_obj.province_id = self.get_province_map.get(province, {}).get("id", 0)
        if not preprocess_customs_company_main_obj.city_id:
            city = preprocess_customs_company_main_obj.city.lower()
            preprocess_customs_company_main_obj.city = self.get_city_map.get(city, {}).get("en", "")
            preprocess_customs_company_main_obj.city_id = self.get_city_map.get(city, {}).get("id", 0)
        return CustomsCompany(
            source=preprocess_customs_company_main_obj.source,
            openid=preprocess_customs_company_main_obj.openid,
            name=preprocess_customs_company_main_obj.name,
            name_new=preprocess_customs_company_main_obj.name_new,
            logo=preprocess_customs_company_main_obj.logo,
            introduce=preprocess_customs_company_main_obj.introduce,
            industry=preprocess_customs_company_main_obj.industry,
            scope=preprocess_customs_company_main_obj.scope,
            attach=preprocess_customs_company_main_obj.attach,
            texture=preprocess_customs_company_main_obj.texture,
            location=preprocess_customs_company_main_obj.location,
            country_id=preprocess_customs_company_main_obj.country_id,
            province_id=preprocess_customs_company_main_obj.province_id,
            city_id=preprocess_customs_company_main_obj.city_id,
            country=preprocess_customs_company_main_obj.country,
            province=preprocess_customs_company_main_obj.province,
            city=preprocess_customs_company_main_obj.city,
            postcode=preprocess_customs_company_main_obj.postcode,
            address=preprocess_customs_company_main_obj.address,
            email=preprocess_customs_company_main_obj.email,
            website=preprocess_customs_company_main_obj.website,
            phone=preprocess_customs_company_main_obj.phone,
            is_ws=preprocess_customs_company_main_obj.is_ws,
            fburl=preprocess_customs_company_main_obj.fburl,
            tturl=preprocess_customs_company_main_obj.tturl,
            isurl=preprocess_customs_company_main_obj.isurl,
            liurl=preprocess_customs_company_main_obj.liurl,
            yturl=preprocess_customs_company_main_obj.yturl,
            pturl=preprocess_customs_company_main_obj.pturl,
            tkurl=preprocess_customs_company_main_obj.tkurl,
            register_person=preprocess_customs_company_main_obj.register_person,
            register_number=preprocess_customs_company_main_obj.register_number,
            register_date=preprocess_customs_company_main_obj.register_date,
            register_type=preprocess_customs_company_main_obj.register_type,
            register_capital=preprocess_customs_company_main_obj.register_capital,
            register_state=preprocess_customs_company_main_obj.register_state,
            company_type=preprocess_customs_company_main_obj.company_type,
            repair_method=preprocess_customs_company_main_obj.repair_method
            )

    def save_to_mysql_customs_company(
            self,
            public_clean_customs_company_main_obj: CustomsCompany
    ) -> int:
        """存储 公司信息"""
        if not public_clean_customs_company_main_obj:
            return 0
        data_src = self.mysql_obj_customs_new_gz.save(
            't_company', public_clean_customs_company_main_obj.__dict__,
            db='db_customs',
            ignore=True
        )
        return data_src.lastrowid

    def deal_clean_customs_company(
            self,
            preprocess_customs_trade_main_obj: PreprocessCustomsTradeMain
    ) -> PreprocessCustomsTradeMain:
        seller_company_info = preprocess_customs_trade_main_obj.seller_company_info
        buyer_company_info = preprocess_customs_trade_main_obj.buyer_company_info
        if not seller_company_info and not buyer_company_info:
            return preprocess_customs_trade_main_obj
        openid_list_map = {}
        if seller_company_info:
            seller_source = seller_company_info.source
            seller_openid = seller_company_info.openid
            if seller_source not in openid_list_map:
                openid_list_map[seller_source] = set()
            openid_list_map[seller_source].add(seller_openid)
        else:
            seller_source = 0
            seller_openid = ''
        if buyer_company_info:
            buyer_source = buyer_company_info.source
            buyer_openid = buyer_company_info.openid
            if buyer_source not in openid_list_map:
                openid_list_map[buyer_source] = set()
            openid_list_map[buyer_source].add(buyer_openid)
        else:
            buyer_source = 0
            buyer_openid = ''

        # 查询公司是否已存在
        seller_id = 0
        buyer_id = 0
        for source, openid_list in openid_list_map.items():
            ocid_map = self.table_customs_company_obj.check_exist_openid(list(openid_list), source)
            if seller_source == source and ocid_map.get(seller_openid, ''):
                seller_id = ocid_map[seller_openid]
            if buyer_source == source and ocid_map.get(buyer_openid, ''):
                buyer_id = ocid_map[buyer_openid]

        # 如果 公司不存在;则插入公司获取返回 ID赋给海关公司模型
        if not seller_id:
            # 海关公司模型映射
            seller_company_model_obj = self.assignment_customs_company_info(seller_company_info)
            seller_id = self.save_to_mysql_customs_company(seller_company_model_obj)
        if not buyer_id:
            # 海关公司模型映射
            buyer_company_model_obj = self.assignment_customs_company_info(buyer_company_info)
            buyer_id = self.save_to_mysql_customs_company(buyer_company_model_obj)
        # 将各公司 ID 赋值给贸易数据
        preprocess_customs_trade_main_obj.seller_id = seller_id
        preprocess_customs_trade_main_obj.buyer_id = buyer_id
        return preprocess_customs_trade_main_obj

    def deal_clean_customs_trade(
            self,
            preprocess_customs_trade_main_obj: PreprocessCustomsTradeMain
    ) -> Union[PublicCleanCustomsTradeMain, None]:
        if not preprocess_customs_trade_main_obj.seller_id and not preprocess_customs_trade_main_obj.buyer_id:
            return None
        # 匹配国家名称二字码
        seller_country, seller_country_code, seller_country_id = self.unify_country_name_code(preprocess_customs_trade_main_obj, "seller_country")
        buyer_country, buyer_country_code, buyer_country_id = self.unify_country_name_code(preprocess_customs_trade_main_obj, "buyer_country")
        product_country, product_country_code, product_country_id = self.unify_country_name_code(preprocess_customs_trade_main_obj, "product_country")
        origin_country, origin_country_code, origin_country_id = self.unify_country_name_code(preprocess_customs_trade_main_obj, "origin_country")
        arrival_country, arrival_country_code, arrival_country_id = self.unify_country_name_code(preprocess_customs_trade_main_obj, "arrival_country")
        # print(preprocess_customs_trade_main_obj.__dict__)
        customs_trade_obj: CustomsTrade = CustomsTrade(
            trade_date=preprocess_customs_trade_main_obj.trade_date,
            source=preprocess_customs_trade_main_obj.source,
            openid=preprocess_customs_trade_main_obj.openid,
            trade_id=preprocess_customs_trade_main_obj.trade_id,
            trade_code=preprocess_customs_trade_main_obj.trade_code,
            weight=preprocess_customs_trade_main_obj.weight,
            weight_unit=preprocess_customs_trade_main_obj.weight_unit,
            quantity=preprocess_customs_trade_main_obj.quantity,
            quantity_unit=preprocess_customs_trade_main_obj.quantity_unit,
            price=preprocess_customs_trade_main_obj.price,
            amount=preprocess_customs_trade_main_obj.amount,
            amount_unit=preprocess_customs_trade_main_obj.amount_unit,
            product_country=product_country,
            product_country_id=product_country_id,
            product_desc=preprocess_customs_trade_main_obj.product_desc,
            product_tag=preprocess_customs_trade_main_obj.product_tag,
            product_hscode=preprocess_customs_trade_main_obj.hscode,
            seller_id=preprocess_customs_trade_main_obj.seller_id,
            seller=preprocess_customs_trade_main_obj.seller,
            seller_country_id_true=seller_country_id,
            seller_country=origin_country,
            seller_country_id=origin_country_id,
            seller_port=preprocess_customs_trade_main_obj.origin_port,
            buyer_id=preprocess_customs_trade_main_obj.buyer_id,
            buyer=preprocess_customs_trade_main_obj.buyer,
            buyer_country_id_true=buyer_country_id,
            buyer_country=arrival_country,
            buyer_country_id=arrival_country_id,
            buyer_port=preprocess_customs_trade_main_obj.arrival_port,
            notifier=preprocess_customs_trade_main_obj.notifier,
            container=preprocess_customs_trade_main_obj.container,
            transport=preprocess_customs_trade_main_obj.transport,
            trade_type=preprocess_customs_trade_main_obj.trade_type,
            update_time=preprocess_customs_trade_main_obj.update_time,
            create_time=preprocess_customs_trade_main_obj.create_time,
        )
        # print(customs_trade_obj.__dict__)
        public_clean_customs_trade_main_obj = PublicCleanCustomsTradeMain(customs_trade_obj=customs_trade_obj)
        return public_clean_customs_trade_main_obj

    def save_to_mysql_customs_trade(self, public_clean_customs_trade_main_obj_list: List[PublicCleanCustomsTradeMain]):
        """
        存储数据
        """
        if not public_clean_customs_trade_main_obj_list:
            return
        # 按库表进行分组批量存储
        save_db_table_data_map = {}
        for public_clean_customs_trade_main_obj in public_clean_customs_trade_main_obj_list:
            db, table = public_clean_customs_trade_main_obj.customs_trade_obj.check_db_table()
            key = f'{db}.{table}'
            if key not in save_db_table_data_map:
                save_db_table_data_map[key] = []
            save_db_table_data_map[key].append(public_clean_customs_trade_main_obj.customs_trade_obj.__dict__)
        for key, datalist in save_db_table_data_map.items():
            db, table_name = key.split('.')
            start = time.time()
            logging.info(f'start save data {table_name}')
            res = self.mysql_obj_customs_new_gz.save(
                table_name, datalist,
                db=db,
                # ignore=True
                field_schema=self.field_schema
            )
            logging.info(f'save data {table_name} res.rowcount: {res.rowcount} hs:{time.time() - start:.2f}s')

    def main_clean_customs_trade(self,
                                 preprocess_customs_trade_main_obj_list: List[PreprocessCustomsTradeMain]
                                 ) -> Optional[List[PublicCleanCustomsTradeMain]]:
        """
        清洗 海关贸易数据 执行入口
        :param preprocess_customs_trade_main_obj_list: 预处理模型数据 列表
        :return:
        """
        s_time = time.time()
        logging.info(f'start to clean customs trade === {len(preprocess_customs_trade_main_obj_list)}')
        uid_sid_map = {}
        db_table_sids_map = {}
        public_clean_customs_trade_main_obj_list = []
        for preprocess_customs_trade_main_obj in preprocess_customs_trade_main_obj_list:
            # 处理公司数据
            preprocess_customs_trade_main_obj = self.deal_clean_customs_company(preprocess_customs_trade_main_obj)
            # 处理贸易数据
            public_clean_customs_trade_main_obj = self.deal_clean_customs_trade(preprocess_customs_trade_main_obj)
            if not public_clean_customs_trade_main_obj:
                continue
            public_clean_customs_trade_main_obj_list.append(public_clean_customs_trade_main_obj)
            uuid = public_clean_customs_trade_main_obj.customs_trade_obj.uuid
            sid = public_clean_customs_trade_main_obj.customs_trade_obj.sid
            db, table = public_clean_customs_trade_main_obj.customs_trade_obj.check_db_table()
            uid_sid_map[uuid] = sid
            key = f'{db}.{table}'
            # print(f'''{key}:{uuid}''')
            if key not in db_table_sids_map:
                db_table_sids_map[key] = set()
            db_table_sids_map[key].add(sid)

        # # 相似处理逻辑; 由于存在查询库表判断 不可并发
        # sid_uid_not_similar_map = {}
        # if db_table_sids_map:
        #     # 校验是否相似
        #     for key, sids in db_table_sids_map.items():
        #         db = key.split('.')[0]
        #         table_name = key.split('.')[1]
        #         # 匹配所有sid的非相似数据
        #         sid_uid_not_similar_map_tmp = self.table_customs_trade_obj.get_not_similar(db, table_name, sids=list(sids))
        #         sid_uid_not_similar_map.update(sid_uid_not_similar_map_tmp)
        # uid_is_similar_map = self.check_is_similar(uid_sid_map, sid_uid_not_similar_map)
        # # 是否相似赋值
        # for public_clean_customs_trade_main_obj in public_clean_customs_trade_main_obj_list:
        #     uuid = public_clean_customs_trade_main_obj.customs_trade_obj.uuid
        #     public_clean_customs_trade_main_obj.customs_trade_obj.is_similar = uid_is_similar_map.get(uuid, 0)

        e_time = time.time()
        logging.info(f'end clean customs trade === {len(public_clean_customs_trade_main_obj_list)} === hs: {e_time - s_time:.2f} s')

        # 数据存储至数据库
        save_stime = time.time()
        logging.info(f'start to save mysql === {len(public_clean_customs_trade_main_obj_list)}')
        self.save_to_mysql_customs_trade(public_clean_customs_trade_main_obj_list)
        save_etime = time.time()
        logging.info(
            f'end to save mysql === {len(public_clean_customs_trade_main_obj_list)} === hs: {save_etime - save_stime:.2f} s')
        return public_clean_customs_trade_main_obj_list

    def test_unit(self):
        """
        单元测试
        :return: 
        """
        pass


if __name__ == '__main__':
    from common.database import MysqlConnectionManager
    from models import ConfMySQL

    conf_list = [ConfMySQL(name='customs_new_gz', max_connections=1)]
    mysql_manager: MysqlConnectionManager = MysqlConnectionManager(conf_list)
    mysql_manager.init()
    _obj = PublicFuncCleanCustomsMain(mysql_manager)
    _obj.test_unit()
